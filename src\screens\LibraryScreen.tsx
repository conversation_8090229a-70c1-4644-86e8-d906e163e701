import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, StatusBar, Alert, Platform, FlatList } from 'react-native';
import { Text } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../types';
import { quizService } from '../services/quizService';
import { QuizSet } from '../types';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../theme/colors';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { TAB_BAR_HEIGHT, BOTTOM_SPACING } from '../constants/layout';
import { useProgressCache } from '../hooks/useProgressCache';
import { useFocusEffect } from '@react-navigation/native';

type LibraryScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Main'>;

const LibraryScreen = () => {
  const navigation = useNavigation<LibraryScreenNavigationProp>();
  const insets = useSafeAreaInsets();
  const [groupedQuizSets, setGroupedQuizSets] = useState<Record<string, QuizSet[]>>({});
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState('all');

  // Progress for completion tracking
  const { progress, refreshProgress } = useProgressCache();

  const filters = [
    { id: 'all', label: 'All' },
    { id: 'beginner', label: 'Beginner' },
    { id: 'intermediate', label: 'Intermediate' },
    { id: 'advanced', label: 'Advanced' },
  ];

  useEffect(() => {
    loadQuizSets();
  }, []);

  // Refresh progress when screen gains focus
  useFocusEffect(
    useCallback(() => {
      refreshProgress();
    }, [])
  );

  const loadQuizSets = async () => {
    try {
      setLoading(true);
      setError(null);
      const grouped = await quizService.getQuizSetsByType();
      setGroupedQuizSets(grouped);

      // Initialize all categories as collapsed
      const initialExpanded: Record<string, boolean> = {};
      Object.keys(grouped).forEach(type => {
        initialExpanded[type] = false;
      });
      setExpandedCategories(initialExpanded);
    } catch (error) {
      console.error('Error loading quiz sets:', error);
      setError('Failed to load quiz sets. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const toggleCategory = (categoryType: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryType]: !prev[categoryType]
    }));
  };

  // Filter quiz sets based on active filter
  const getFilteredQuizSets = (quizSets: QuizSet[]) => {
    if (activeFilter === 'all') return quizSets;

    return quizSets.filter(quiz => {
      if (!quiz?.difficulty) return false;
      return quiz.difficulty.toLowerCase() === activeFilter.toLowerCase();
    });
  };

  const renderCategoryContainer = (categoryType: string, allQuizSets: QuizSet[]) => {
    const filteredQuizSets = getFilteredQuizSets(allQuizSets);
    const isExpanded = expandedCategories[categoryType];
    const totalCount = allQuizSets.length;
    const filteredCount = filteredQuizSets.length;

    // Hide category if no quizzes match filter
    if (filteredCount === 0 && activeFilter !== 'all') {
      return null;
    }

    return (
      <View key={categoryType} style={styles.categoryContainer}>
        <TouchableOpacity
          style={styles.categoryHeader}
          onPress={() => toggleCategory(categoryType)}
        >
          <View style={styles.categoryHeaderLeft}>
            <Text style={styles.categoryTitle}>{categoryType}</Text>
            <Text style={styles.categorySubtitle}>
              {activeFilter === 'all'
                ? `${totalCount} Quiz${totalCount !== 1 ? 'sets' : 'set'}`
                : `${filteredCount} of ${totalCount} Quiz${totalCount !== 1 ? 'sets' : 'set'}`
              }
            </Text>
          </View>
          <View style={styles.categoryHeaderRight}>
            {/* Optional: Add "View All" button for detailed screen */}
            {totalCount > 5 && (
              <TouchableOpacity
                style={styles.viewAllButton}
                onPress={() => {
                  // Future: Navigate to detailed type screen
                  // navigation.navigate('TypeDetail', { type: categoryType, title: categoryType });
                }}
              >
                <Text style={styles.viewAllText}>View All</Text>
              </TouchableOpacity>
            )}
            <Icon
              name={isExpanded ? "keyboard-arrow-up" : "keyboard-arrow-down"}
              size={24}
              color={colors.text.secondary}
            />
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.categoryContent}>
            {filteredQuizSets.map(quizSet => renderQuizSetItem(quizSet))}
          </View>
        )}
      </View>
    );
  };

  const renderQuizSetItem = (quizSet: QuizSet) => {
    if (!quizSet?.id || !quizSet?.title) return null;

    const questionCount = Array.isArray(quizSet.questions) ? quizSet.questions.length : 0;
    const difficulty = quizSet.difficulty || 'Beginner';

    const completed = progress?.history?.some(h => h.quizId === quizSet.id);

    return (
      <TouchableOpacity
        key={quizSet.id}
        style={styles.quizSetItem}
        onPress={() => {
          if (!quizSet.questions || quizSet.questions.length === 0) {
            Alert.alert('Error', 'This quiz set has no questions.');
            return;
          }

          const goToQuiz = () => navigation.navigate('Quiz', { quizSetId: quizSet.id });

          if (completed) {
            Alert.alert(
              'Reattempt Quiz',
              'You already completed this quiz. Do you want to reattempt?',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Reattempt', onPress: goToQuiz },
              ]
            );
          } else {
            goToQuiz();
          }
        }}
      >
        <View>
          <Text style={styles.quizSetTitle}>{quizSet.title}</Text>
          {completed && <Text style={styles.completedLabel}>Completed</Text>}
          <Text style={styles.quizSetDescription}>
            {questionCount} words • {difficulty}
          </Text>
        </View>
        <Icon name="chevron-right" size={24} color={colors.text.secondary} />
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Library</Text>
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading quizzes...</Text>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Library</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadQuizSets}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Library</Text>
      </View>

      {/* Filter Categories */}
      <View style={styles.filterSection}>
        <View style={styles.filterHeader}>
          <Icon name="filter-list" size={24} color={colors.text.primary} />
          <Text style={styles.filterTitle}>Filter by Difficulty</Text>
        </View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
          {filters.map((filter) => (
            <TouchableOpacity
              key={filter.id}
              style={[
                styles.filterChip,
                activeFilter === filter.id && styles.activeFilterChip,
              ]}
              onPress={() => setActiveFilter(filter.id)}
            >
              <Text
                style={[
                  styles.filterLabel,
                  activeFilter === filter.id && styles.activeFilterLabel,
                ]}
              >
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Category Containers */}
      <ScrollView
        style={styles.content}
        contentContainerStyle={{
          paddingHorizontal: 16,
          paddingTop: 8,
          paddingBottom:
            TAB_BAR_HEIGHT +
            (Platform.OS === 'ios' ? insets.bottom : BOTTOM_SPACING) +
            16,
        }}
      >
        {Object.keys(groupedQuizSets).length > 0 ? (
          Object.entries(groupedQuizSets)
            .sort(([a], [b]) => {
              // Sort categories: Romeo, Juliet, Hunter, then alphabetically
              const order = ['Romeo', 'Juliet', 'Hunter'];
              const aIndex = order.indexOf(a);
              const bIndex = order.indexOf(b);

              if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
              if (aIndex !== -1) return -1;
              if (bIndex !== -1) return 1;
              return a.localeCompare(b);
            })
            .map(([categoryType, quizSets]) =>
              renderCategoryContainer(categoryType, quizSets)
            )
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No quizzes available</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.secondary,
  },
  header: {
    padding: 16,
    backgroundColor: colors.dark.medium,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  filterSection: {
    padding: 16,
    backgroundColor: colors.dark.medium,
    borderBottomWidth: 1,
    borderBottomColor: colors.dark.border,
  },
  filterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginLeft: 8,
  },
  filterScroll: {
    flexGrow: 0,
  },
  filterChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: colors.dark.dark,
    marginRight: 8,
  },
  activeFilterChip: {
    backgroundColor: colors.primary,
  },
  filterLabel: {
    color: colors.text.secondary,
    fontSize: 14,
  },
  activeFilterLabel: {
    color: colors.text.onPrimary,
    fontWeight: 'bold',
  },
  categoryContainer: {
    marginBottom: 16,
    backgroundColor: colors.dark.medium,
    borderRadius: 8,
    overflow: 'hidden',
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: colors.dark.medium,
  },
  categoryHeaderLeft: {
    flex: 1,
  },
  categoryHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 4,
  },
  categorySubtitle: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  viewAllButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    borderRadius: 4,
    backgroundColor: colors.dark.dark,
  },
  viewAllText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: 'bold',
  },
  categoryContent: {
    backgroundColor: colors.dark.dark,
  },
  content: {
    flex: 1,
  },
  quizSetItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: colors.dark.dark,
    borderBottomWidth: 1,
    borderBottomColor: colors.dark.border,
  },
  quizSetTitle: {
    color: colors.text.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  completedLabel: {
    color: colors.status.success,
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 2,
  },
  quizSetDescription: {
    color: colors.text.secondary,
    fontSize: 14,
    marginTop: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: colors.text.secondary,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    color: colors.text.secondary,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: colors.status.error,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.text.onPrimary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  completedLabel: {
    color: colors.primary,
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 4,
  },
});

export default LibraryScreen;