import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, StatusBar, Alert, Platform, FlatList } from 'react-native';
import { Text } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../types';
import { quizService } from '../services/quizService';
import { QuizSet } from '../types';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../theme/colors';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { TAB_BAR_HEIGHT, BOTTOM_SPACING } from '../constants/layout';
import { useProgressCache } from '../hooks/useProgressCache';
import { useFocusEffect } from '@react-navigation/native';

type LibraryScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Main'>;

const LibraryScreen = () => {
  const navigation = useNavigation<LibraryScreenNavigationProp>();
  const insets = useSafeAreaInsets();
  const [activeFilter, setActiveFilter] = useState('all');
  const [quizSets, setQuizSets] = useState<QuizSet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Progress for completion tracking
  const { progress, refreshProgress } = useProgressCache();

  // Pagination state
  const PAGE_SIZE = 20;
  const [page, setPage] = useState(1);
  const [displayedQuizSets, setDisplayedQuizSets] = useState<QuizSet[]>([]);

  const filters = [
    { id: 'all', label: 'All' },
    { id: 'intermediate', label: 'Intermediate' },
    { id: 'advanced', label: 'Advanced' },
    { id: 'beginner', label: 'Beginner' },
    { id: 'general', label: 'General' },
  ];

  useEffect(() => {
    loadQuizSets();
  }, []);

  // Refresh progress when screen gains focus
  useFocusEffect(
    useCallback(() => {
      refreshProgress();
    }, [])
  );

  const loadQuizSets = async () => {
    try {
      setLoading(true);
      setError(null);
      const sets = await quizService.getAllQuizSets();
      // Filter out invalid quiz sets
      const validSets = sets.filter(quiz => 
        quiz && 
        quiz.id && 
        quiz.title && 
        Array.isArray(quiz.questions)
      );
      setQuizSets(validSets);
    } catch (error) {
      console.error('Error loading quiz sets:', error);
      setError('Failed to load quiz sets. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const filteredQuizSets = useMemo(() => {
    return quizSets.filter((quiz) => {
      if (!quiz || !quiz.difficulty || !quiz.category) return false;
      if (activeFilter === 'all') return true;
      return (
        quiz.difficulty.toLowerCase() === activeFilter ||
        quiz.category.toLowerCase() === activeFilter
      );
    });
  }, [quizSets, activeFilter]);

  // Update displayed items whenever filter or page changes
  useEffect(() => {
    setDisplayedQuizSets(filteredQuizSets.slice(0, page * PAGE_SIZE));
  }, [page, filteredQuizSets]);

  const handleLoadMore = () => {
    if (displayedQuizSets.length < filteredQuizSets.length) {
      setPage(prev => prev + 1);
    }
  };

  const renderQuizSetItem = (quizSet: QuizSet) => {
    if (!quizSet?.id || !quizSet?.title) return null;

    const questionCount = Array.isArray(quizSet.questions) ? quizSet.questions.length : 0;
    const difficulty = quizSet.difficulty || 'Beginner';

    const completed = progress?.history?.some(h => h.quizId === quizSet.id);

    return (
      <TouchableOpacity
        key={quizSet.id}
        style={styles.quizSetItem}
        onPress={() => {
          if (!quizSet.questions || quizSet.questions.length === 0) {
            Alert.alert('Error', 'This quiz set has no questions.');
            return;
          }

          const goToQuiz = () => navigation.navigate('Quiz', { quizSetId: quizSet.id });

          if (completed) {
            Alert.alert(
              'Reattempt Quiz',
              'You already completed this quiz. Do you want to reattempt?',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Reattempt', onPress: goToQuiz },
              ]
            );
          } else {
            goToQuiz();
          }
        }}
      >
        <View>
          <Text style={styles.quizSetTitle}>{quizSet.title}</Text>
          {completed && <Text style={styles.completedLabel}>Completed</Text>}
          <Text style={styles.quizSetDescription}>
            {questionCount} words • {difficulty}
          </Text>
        </View>
        <Icon name="chevron-right" size={24} color={colors.text.secondary} />
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Library</Text>
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading quizzes...</Text>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Library</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadQuizSets}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Filter Categories */}
      <View style={styles.filterSection}>
        <View style={styles.filterHeader}>
          <Icon name="filter-list" size={24} color={colors.text.primary} />
          <Text style={styles.filterTitle}>Filter Categories</Text>
        </View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
          {filters.map((filter) => (
            <TouchableOpacity
              key={filter.id}
              style={[
                styles.filterChip,
                activeFilter === filter.id && styles.activeFilterChip,
              ]}
              onPress={() => {
                setActiveFilter(filter.id);
                setPage(1); // reset pagination on filter change
              }}
            >
              <Text
                style={[
                  styles.filterLabel,
                  activeFilter === filter.id && styles.activeFilterLabel,
                ]}
              >
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Quiz Sets */}
      <FlatList
        data={displayedQuizSets}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => renderQuizSetItem(item)}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        contentContainerStyle={{
          paddingHorizontal: 16,
          paddingTop: 8,
          paddingBottom:
            TAB_BAR_HEIGHT +
            (Platform.OS === 'ios' ? insets.bottom : BOTTOM_SPACING) +
            16,
        }}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No quizzes available</Text>
          </View>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.secondary,
  },
  header: {
    padding: 16,
    backgroundColor: colors.dark.medium,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  filterSection: {
    padding: 16,
    backgroundColor: colors.dark.medium,
  },
  filterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginLeft: 8,
  },
  filterScroll: {
    flexGrow: 0,
  },
  filterChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: colors.dark.dark,
    marginRight: 8,
  },
  activeFilterChip: {
    backgroundColor: colors.primary,
  },
  filterLabel: {
    color: colors.text.secondary,
    fontSize: 14,
  },
  activeFilterLabel: {
    color: colors.text.onPrimary,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 16,
  },
  quizSetItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    backgroundColor: colors.dark.medium,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: colors.dark.border,
  },
  quizSetTitle: {
    color: colors.text.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  quizSetDescription: {
    color: colors.text.secondary,
    fontSize: 14,
    marginTop: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: colors.text.secondary,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    color: colors.text.secondary,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: colors.status.error,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.text.onPrimary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  completedLabel: {
    color: colors.primary,
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 4,
  },
});

export default LibraryScreen;