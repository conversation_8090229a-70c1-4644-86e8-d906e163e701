/**
 * 🚀 Optimized Progress Service for Production Scale
 * 
 * Features:
 * - Extended cache durations (2 hours vs 15 minutes)
 * - Background refresh for stale data
 * - Intelligent cache invalidation
 * - Offline-first with sync queue
 * - Batch operations for multiple updates
 */

import { getAuth } from 'firebase/auth';
import { collection, doc, getDocs, setDoc, query, where, serverTimestamp, writeBatch } from 'firebase/firestore';
import { db } from '../config/firebase';
import { cacheData, getCachedData, CACHE_KEYS, invalidateCachePattern } from '../utils/cache';
import { backgroundSyncService } from './backgroundSyncService';

export interface UserProgress {
  totalScore: number;
  quizzesTaken: number;
  history: QuizProgress[];
  _cacheTimestamp?: number;
  _lastSyncTimestamp?: number;
}

export interface QuizProgress {
  quizId: string;
  score: number;
  totalQuestions: number;
  completedAt: string;
  timeSpent: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

class OptimizedProgressService {
  private readonly CACHE_DURATION = 2 * 60 * 60 * 1000; // 2 hours
  private readonly BACKGROUND_REFRESH_THRESHOLD = 30 * 60 * 1000; // 30 minutes

  /**
   * Get user progress with optimized caching
   */
  async getUserProgress(userId?: string): Promise<UserProgress | null> {
    const auth = getAuth();
    const currentUserId = userId || auth.currentUser?.uid;

    if (!currentUserId) {
      return null;
    }

    try {
      // 🚀 Step 1: Check cache first (2-hour cache)
      const cachedProgress = await getCachedData(CACHE_KEYS.USER_PROGRESS, currentUserId);
      
      if (cachedProgress) {
        console.log('[OptimizedProgress] ✅ CACHE HIT: User progress');
        
        // Check if cache needs background refresh
        const cacheAge = Date.now() - (cachedProgress._cacheTimestamp || 0);
        if (cacheAge > this.BACKGROUND_REFRESH_THRESHOLD) {
          console.log('[OptimizedProgress] 🔄 Triggering background refresh');
          this.refreshProgressInBackground(currentUserId);
        }
        
        return cachedProgress;
      }

      // 🚀 Step 2: Cache miss - fetch from Firestore
      console.log('[OptimizedProgress] 📥 CACHE MISS: Fetching from Firestore (COST: 1 read)');
      return await this.fetchProgressFromFirestore(currentUserId);

    } catch (error: any) {
      // Handle permission errors gracefully for unauthenticated users
      if (error.code === 'permission-denied') {
        return null;
      }
      console.error('[OptimizedProgress] ❌ Error getting user progress:', error);
      throw error;
    }
  }

  /**
   * Fetch progress from Firestore and cache it
   */
  private async fetchProgressFromFirestore(userId: string): Promise<UserProgress> {
    try {
      const progressRef = collection(db, 'userProgress');
      const q = query(progressRef, where('userId', '==', userId));
      const querySnapshot = await getDocs(q);

    let progressData: UserProgress;

    if (querySnapshot.empty) {
      // Initialize new user progress
      progressData = {
        totalScore: 0,
        quizzesTaken: 0,
        history: [],
        _cacheTimestamp: Date.now(),
        _lastSyncTimestamp: Date.now()
      };
      console.log('[OptimizedProgress] 🆕 Initializing new user progress');
    } else {
      // Use existing progress
      const doc = querySnapshot.docs[0];
      progressData = {
        ...doc.data() as UserProgress,
        _cacheTimestamp: Date.now(),
        _lastSyncTimestamp: Date.now()
      };
      console.log('[OptimizedProgress] ✅ Fetched existing progress');
    }

      // Cache with extended duration
      await cacheData(CACHE_KEYS.USER_PROGRESS, progressData, userId);
      return progressData;
    } catch (error: any) {
      // Handle permission errors for unauthenticated users
      if (error.code === 'permission-denied') {
        // Return default progress for unauthenticated users
        const defaultProgress: UserProgress = {
          totalScore: 0,
          quizzesTaken: 0,
          history: [],
          _cacheTimestamp: Date.now(),
          _lastSyncTimestamp: Date.now()
        };
        return defaultProgress;
      }
      throw error;
    }
  }

  /**
   * Background refresh without blocking UI
   */
  private async refreshProgressInBackground(userId: string): Promise<void> {
    try {
      const freshProgress = await this.fetchProgressFromFirestore(userId);
      console.log('[OptimizedProgress] ✅ Background refresh completed');
    } catch (error) {
      console.warn('[OptimizedProgress] ⚠️ Background refresh failed:', error);
    }
  }

  /**
   * Update user progress with offline support
   */
  async updateProgress(
    userId: string,
    newQuizProgress: QuizProgress,
    immediate = false
  ): Promise<void> {
    // Skip progress updates for unauthenticated users
    if (!userId) {
      return;
    }

    try {
      // 🚀 Step 1: Update local cache immediately
      const currentProgress = await this.getUserProgress(userId) || {
        totalScore: 0,
        quizzesTaken: 0,
        history: []
      };

      const updatedProgress: UserProgress = {
        totalScore: currentProgress.totalScore + newQuizProgress.score,
        quizzesTaken: currentProgress.quizzesTaken + 1,
        history: [...currentProgress.history, newQuizProgress],
        _cacheTimestamp: Date.now(),
        _lastSyncTimestamp: currentProgress._lastSyncTimestamp
      };

      // Update cache immediately for instant UI feedback
      await cacheData(CACHE_KEYS.USER_PROGRESS, updatedProgress, userId);
      console.log('[OptimizedProgress] ✅ Local cache updated immediately');

      // 🚀 Step 2: Handle remote sync based on connectivity
      if (immediate) {
        // Immediate sync for critical updates
        await this.syncProgressToFirestore(userId, updatedProgress);
      } else {
        // Queue for background sync
        await backgroundSyncService.addPendingWrite(
          'user_progress',
          updatedProgress,
          'medium'
        );
        console.log('[OptimizedProgress] 📤 Queued for background sync');
      }

    } catch (error: any) {
      // Handle permission errors gracefully for unauthenticated users
      if (error.code === 'permission-denied') {
        // For unauthenticated users, just update local cache
        console.log('[OptimizedProgress] Updating local cache only (unauthenticated user)');
        return;
      }
      console.error('[OptimizedProgress] ❌ Error updating progress:', error);
      throw error;
    }
  }

  /**
   * Sync progress to Firestore
   */
  private async syncProgressToFirestore(userId: string, progressData: UserProgress): Promise<void> {
    try {
      const docRef = doc(db, 'userProgress', userId);
      
      const syncData = {
        ...progressData,
        userId,
        updatedAt: serverTimestamp(),
        _lastSyncTimestamp: Date.now()
      };

      // Remove cache metadata before syncing
      delete syncData._cacheTimestamp;

      await setDoc(docRef, syncData, { merge: true });
      console.log('[OptimizedProgress] ✅ Synced to Firestore (COST: 1 write)');

      // Update cache with sync timestamp
      await cacheData(CACHE_KEYS.USER_PROGRESS, {
        ...progressData,
        _lastSyncTimestamp: Date.now()
      }, userId);

    } catch (error) {
      console.error('[OptimizedProgress] ❌ Error syncing to Firestore:', error);
      throw error;
    }
  }

  /**
   * Batch update multiple quiz results (for bulk operations)
   */
  async batchUpdateProgress(
    userId: string,
    quizResults: QuizProgress[]
  ): Promise<void> {
    try {
      console.log(`[OptimizedProgress] 🚀 Batch updating ${quizResults.length} quiz results`);

      // Update local cache
      const currentProgress = await this.getUserProgress(userId) || {
        totalScore: 0,
        quizzesTaken: 0,
        history: []
      };

      const totalNewScore = quizResults.reduce((sum, result) => sum + result.score, 0);
      
      const updatedProgress: UserProgress = {
        totalScore: currentProgress.totalScore + totalNewScore,
        quizzesTaken: currentProgress.quizzesTaken + quizResults.length,
        history: [...currentProgress.history, ...quizResults],
        _cacheTimestamp: Date.now(),
        _lastSyncTimestamp: currentProgress._lastSyncTimestamp
      };

      await cacheData(CACHE_KEYS.USER_PROGRESS, updatedProgress, userId);

      // Queue for background sync
      await backgroundSyncService.addPendingWrite(
        'user_progress',
        updatedProgress,
        'high' // High priority for batch operations
      );

      console.log('[OptimizedProgress] ✅ Batch update completed');

    } catch (error) {
      console.error('[OptimizedProgress] ❌ Error in batch update:', error);
      throw error;
    }
  }

  /**
   * Force sync all pending progress updates
   */
  async forceSyncProgress(): Promise<void> {
    console.log('[OptimizedProgress] 🚀 Force syncing progress...');
    await backgroundSyncService.forcSync();
  }

  /**
   * Clear progress cache (useful for logout)
   */
  async clearProgressCache(userId?: string): Promise<void> {
    if (userId) {
      await cacheData(CACHE_KEYS.USER_PROGRESS, null, userId);
    } else {
      await invalidateCachePattern('user_progress');
    }
    console.log('[OptimizedProgress] 🗑️ Progress cache cleared');
  }

  /**
   * Get sync status for progress
   */
  async getProgressSyncStatus(userId: string): Promise<{
    lastSync: number;
    hasPendingUpdates: boolean;
    cacheAge: number;
  }> {
    const progress = await getCachedData(CACHE_KEYS.USER_PROGRESS, userId);
    const syncStatus = await backgroundSyncService.getSyncStatus();
    
    return {
      lastSync: progress?._lastSyncTimestamp || 0,
      hasPendingUpdates: syncStatus.pendingWrites > 0,
      cacheAge: progress?._cacheTimestamp ? Date.now() - progress._cacheTimestamp : 0
    };
  }
}

// Export singleton instance
export const optimizedProgressService = new OptimizedProgressService();
