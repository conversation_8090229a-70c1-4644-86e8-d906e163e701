/**
 * 🚀 Background Sync Service for Production Scale
 * 
 * Features:
 * - Offline-first architecture
 * - Background sync with intelligent scheduling
 * - Conflict resolution
 * - Retry mechanisms with exponential backoff
 * - Bandwidth optimization
 */

import { getAuth } from 'firebase/auth';
import { collection, doc, writeBatch, serverTimestamp, getDocs, query, where, orderBy, limit } from 'firebase/firestore';
import { db } from '../config/firebase';
import { cacheData, getCachedData, CACHE_KEYS, invalidateCachePattern } from '../utils/cache';
import NetInfo from '@react-native-community/netinfo';

interface PendingWrite {
  id: string;
  type: 'quiz_result' | 'user_progress' | 'user_activity';
  data: any;
  timestamp: number;
  retryCount: number;
  priority: 'high' | 'medium' | 'low';
}

interface SyncStatus {
  lastSync: number;
  pendingWrites: number;
  isOnline: boolean;
  isSyncing: boolean;
}

class BackgroundSyncService {
  private syncInterval: NodeJS.Timeout | null = null;
  private isOnline = true;
  private isSyncing = false;
  private maxRetries = 3;
  private baseRetryDelay = 1000; // 1 second

  constructor() {
    this.initializeNetworkListener();
    this.startPeriodicSync();
  }

  /**
   * Initialize network state monitoring
   */
  private initializeNetworkListener() {
    NetInfo.addEventListener(state => {
      const wasOffline = !this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      console.log(`[BackgroundSync] Network status: ${this.isOnline ? 'Online' : 'Offline'}`);
      
      // If we just came back online, trigger sync
      if (wasOffline && this.isOnline) {
        console.log('[BackgroundSync] 🌐 Back online - triggering sync');
        this.syncPendingWrites();
      }
    });
  }

  /**
   * Start periodic background sync
   */
  private startPeriodicSync() {
    // Sync every 5 minutes when online
    this.syncInterval = setInterval(() => {
      if (this.isOnline && !this.isSyncing) {
        this.syncPendingWrites();
      }
    }, 5 * 60 * 1000);
  }

  /**
   * Add a write operation to the pending queue
   */
  async addPendingWrite(
    type: PendingWrite['type'],
    data: any,
    priority: PendingWrite['priority'] = 'medium'
  ): Promise<void> {
    const pendingWrite: PendingWrite = {
      id: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      priority
    };

    try {
      const existingWrites = await this.getPendingWrites();
      existingWrites.push(pendingWrite);
      
      await cacheData(CACHE_KEYS.PENDING_WRITES, existingWrites);
      console.log(`[BackgroundSync] ➕ Added pending write: ${type} (Priority: ${priority})`);
      
      // If online and high priority, sync immediately
      if (this.isOnline && priority === 'high') {
        this.syncPendingWrites();
      }
    } catch (error) {
      console.error('[BackgroundSync] ❌ Error adding pending write:', error);
    }
  }

  /**
   * Get all pending writes from cache
   */
  private async getPendingWrites(): Promise<PendingWrite[]> {
    try {
      const writes = await getCachedData(CACHE_KEYS.PENDING_WRITES);
      return writes || [];
    } catch (error) {
      console.error('[BackgroundSync] ❌ Error getting pending writes:', error);
      return [];
    }
  }

  /**
   * Sync all pending writes to Firebase
   */
  async syncPendingWrites(): Promise<void> {
    if (!this.isOnline || this.isSyncing) {
      return;
    }

    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      console.log('[BackgroundSync] ⏸️ No user logged in - skipping sync');
      return;
    }

    this.isSyncing = true;
    console.log('[BackgroundSync] 🔄 Starting sync...');

    try {
      const pendingWrites = await this.getPendingWrites();
      
      if (pendingWrites.length === 0) {
        console.log('[BackgroundSync] ✅ No pending writes to sync');
        return;
      }

      console.log(`[BackgroundSync] 📤 Syncing ${pendingWrites.length} pending writes`);
      
      // Sort by priority and timestamp
      const sortedWrites = pendingWrites.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        }
        return a.timestamp - b.timestamp;
      });

      // Process writes in batches
      const batchSize = 10;
      const batches = [];
      
      for (let i = 0; i < sortedWrites.length; i += batchSize) {
        batches.push(sortedWrites.slice(i, i + batchSize));
      }

      let successCount = 0;
      const failedWrites: PendingWrite[] = [];

      for (const batch of batches) {
        try {
          await this.processBatch(batch, user.uid);
          successCount += batch.length;
        } catch (error) {
          console.error('[BackgroundSync] ❌ Batch failed:', error);
          
          // Add failed writes back to queue with incremented retry count
          batch.forEach(write => {
            if (write.retryCount < this.maxRetries) {
              failedWrites.push({
                ...write,
                retryCount: write.retryCount + 1
              });
            } else {
              console.warn(`[BackgroundSync] ⚠️ Dropping write after ${this.maxRetries} retries:`, write.id);
            }
          });
        }
      }

      // Update pending writes cache
      await cacheData(CACHE_KEYS.PENDING_WRITES, failedWrites);
      
      console.log(`[BackgroundSync] ✅ Sync completed: ${successCount} successful, ${failedWrites.length} failed`);
      
      // Invalidate relevant caches after successful sync
      if (successCount > 0) {
        await invalidateCachePattern('user_progress');
        await invalidateCachePattern('user_activity');
      }

    } catch (error) {
      console.error('[BackgroundSync] ❌ Sync error:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Process a batch of writes
   */
  private async processBatch(writes: PendingWrite[], userId: string): Promise<void> {
    const batch = writeBatch(db);
    
    writes.forEach(write => {
      switch (write.type) {
        case 'quiz_result':
          const resultRef = doc(collection(db, 'quizResults'));
          batch.set(resultRef, {
            ...write.data,
            userId,
            syncedAt: serverTimestamp()
          });
          
          const userQuizRef = doc(db, 'userQuizzes', `${userId}_${write.data.quizId}`);
          batch.set(userQuizRef, {
            userId,
            quizId: write.data.quizId,
            completed: true,
            completedAt: serverTimestamp(),
            score: write.data.score
          }, { merge: true });
          break;
          
        case 'user_progress':
          const progressRef = doc(db, 'userProgress', userId);
          batch.set(progressRef, {
            ...write.data,
            userId,
            syncedAt: serverTimestamp()
          }, { merge: true });
          break;
          
        case 'user_activity':
          const activityRef = doc(collection(db, 'userActivity'));
          batch.set(activityRef, {
            ...write.data,
            userId,
            syncedAt: serverTimestamp()
          });
          break;
      }
    });

    await batch.commit();
    console.log(`[BackgroundSync] ✅ Batch of ${writes.length} writes committed`);
  }

  /**
   * Get sync status
   */
  async getSyncStatus(): Promise<SyncStatus> {
    const pendingWrites = await this.getPendingWrites();
    const lastSyncStr = await getCachedData('last_sync_timestamp');
    
    return {
      lastSync: lastSyncStr ? parseInt(lastSyncStr) : 0,
      pendingWrites: pendingWrites.length,
      isOnline: this.isOnline,
      isSyncing: this.isSyncing
    };
  }

  /**
   * Force immediate sync
   */
  async forcSync(): Promise<void> {
    console.log('[BackgroundSync] 🚀 Force sync requested');
    await this.syncPendingWrites();
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }
}

// Export singleton instance
export const backgroundSyncService = new BackgroundSyncService();
