import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Button } from 'react-native-paper';
import { getAuth } from 'firebase/auth';
import { useNavigation } from '@react-navigation/native';
import { colors } from '../theme/colors';
import AuthModal from './AuthModal';

interface ProtectedScreenProps {
  children: React.ReactNode;
  screenName: string;
  onAuthSuccess?: () => void;
}

const ProtectedScreen = ({ children, screenName, onAuthSuccess }: ProtectedScreenProps) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const navigation = useNavigation();
  const auth = getAuth();

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      setIsAuthenticated(!!user);
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const handleAuthSuccess = () => {
    setShowAuthModal(false);
    setIsAuthenticated(true);
    if (onAuthSuccess) {
      onAuthSuccess();
    }
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text>Loading...</Text>
      </View>
    );
  }

  if (!isAuthenticated) {
    return (
      <View style={styles.container}>
        <Text style={styles.message}>
          Please log in to access your {screenName.toLowerCase()}.
        </Text>
        <Button
          mode="contained"
          onPress={() => setShowAuthModal(true)}
          style={styles.button}
        >
          Login / Sign Up
        </Button>
        <Button
          mode="outlined"
          onPress={() => navigation.goBack()}
          style={styles.button}
        >
          Go Back
        </Button>
        <AuthModal
          visible={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          onSuccess={handleAuthSuccess}
          title={`Login to view ${screenName}`}
        />
      </View>
    );
  }

  return <>{children}</>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: colors.secondary,
  },
  message: {
    color: colors.text.primary,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    marginVertical: 8,
    width: '100%',
  },
});

export default ProtectedScreen; 