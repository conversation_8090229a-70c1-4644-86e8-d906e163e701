# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "com.facebook.react"
    artifactId: "react-android"
    version: "0.72.6"
  }
  digests {
    sha256: "\v\375\372\3504?S\f\353zbir$\253\233\261\347\362\'\030\341\377j\000\217X\260\255\vX\370"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.3.0"
  }
  digests {
    sha256: "\227\334E\257\357\343\241\344!\332B\270\266\351\371\004\221G|E\374ax >:^\212\005\356\205S"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.9.0"
  }
  digests {
    sha256: "\213\332>\343\250\210\207\325Ofy\373kl\327\210b\237s#J\311\034\213\276\331$\347!\354\205\270"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.3.0"
  }
  digests {
    sha256: "\253\375)\310Un[\3202Z\237v\232\271\351\321T\377JU\025\304v\315\325\242\250([\033\031\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.8.10"
  }
  digests {
    sha256: "\027\341\aa1\315\a\311X\251B\377\212\b|\370e\261\357=\345\204c\341\345\335o\327QT\006\260"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.8.10"
  }
  digests {
    sha256: "8P1\336Lm\331\351\220\326\244DU\327\225\203W*I\n4d\327\376\177\020\245\354\3122%\250"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.6.4"
  }
  digests {
    sha256: "?\334\016\355[\304\270>\351b\'tR\n-\262Tp7\016\254\321X\034\254\0367pO\t[\000"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.6.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.6.4"
  }
  digests {
    sha256: "\302L\213\262{\263 \304\2518qP\032~^\fa`v8\220{\031z\357gU\023\324\310 \276"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.6.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.10"
  }
  digests {
    sha256: "\226\"\366|\276A\350\212\322W\301\270\001\200T\335\002\032`D\026$t\247\213\033\354\324O\025\241i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.10"
  }
  digests {
    sha256: "\250C\252\340\267#$H\257\306\257\005t|\324\336Ak\234+v\341\000\241\260{\037x\2037\262\355"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.1"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.1"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\237,\026\343:U\272\215g\243b:U\276\377\362\354\200d>?\n\222g`\337\035\225|?\212\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.1"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.1.0"
  }
  digests {
    sha256: "[x\342\306\030\374\020\263\321M\354\300\035\367aX\361YT\255tj\254\360`wfr\035\240\201\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "(\240\2704\364\016\335R\342\370\001\v\3340W\322\240\314v\252\245\254\223\021\255\260\271\316\221\234\251\314"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.6.1"
  }
  digests {
    sha256: "\212\316\2311?\n\346\257G\031K\312\376(\363D\343c\364\322\223\370K+\227\264\201s[\004\336\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.1"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\177\300\372#J3!\261\363M\265\206+\027\332\203\321\306,\033\306n\302\375O\033\260\247q\254\372\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.1"
  }
  digests {
    sha256: "\310/\211\"\032\333\341\235\367\307\255\272\266?N\314\205\177\307F\343\311IBV\253\217\245\302\004\221\262"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.9.0"
  }
  digests {
    sha256: "\025B\241\337{\351\b\311_\356\221\270\333\300?\331t\365?\021\330J\205\330\201\371ZRU\034@Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.0"
  }
  digests {
    sha256: "4\350\262\277\307N#\301R^=\251\003\256D\233\177\033D\n\357E\341\201Y\356G\016\221\231\177H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.7.2"
  }
  digests {
    sha256: "*\032\277\216\nY\215$k\036\334\267\036#\225\343\23723!\225\353\345\352@\243\335!5[\243\256"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.7.2"
  }
  digests {
    sha256: "\260\264 n\316\222\221\231%\006\037\337W\204\335!\360\021\2054`\236\217m\224\004\275\320\365\313Z="
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.2.0"
  }
  digests {
    sha256: "\363\032\006\301P\354\2600s\365Zo{\vt\242@\246\250\327\'\301L\347g&\320 W\r\372\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.2.0"
  }
  digests {
    sha256: "\177\372MFM\235\262Y\374\240\315\265\017\275J\266=hr\274\332YF\213\237uUPL}Z\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.7"
  }
  digests {
    sha256: "Y\307A\020\271\223x\210^\320bB\370\242m\243\005\352M\312S\355`\0265@\335\016.<fu"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.1.0"
  }
  digests {
    sha256: "g\316\270&|\'EW\303WX\353\276D\211\n :Uy\275+\bM\363Mpv<r\370\017"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\227a\263\250\t\311\260\223\375\006\243\304\273\306Eum\354\016\225\265\311\332A\233\311\362\243\363\002n\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.facebook.fbjni"
    artifactId: "fbjni"
    version: "0.3.0"
  }
  digests {
    sha256: "\345s\3311\032\340\261\245X\353M\262h\006\333\030Sf\211$AO\274\237?\205@\3172\363u\375"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "nativeloader"
    version: "0.10.5"
  }
  digests {
    sha256: "\227\035\355\206\000\234\n\305o\262\'^\022\217\205.}\313\304\032@\315y\226\210\370\241\256\275\345\031]"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "fresco"
    version: "2.5.0"
  }
  digests {
    sha256: "\003\3322!6\362\340\r\373p02^\315\3430\332_Hs#\313\203w\246\002\b\205$\240\\\b"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "soloader"
    version: "2.5.0"
  }
  digests {
    sha256: "\242_\377\307T7\357\2346\276$\215\360K\023q\344\260\022)\371D\001Oul^\030,Q\232\202"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "fbcore"
    version: "2.5.0"
  }
  digests {
    sha256: "\247\204bk\a\212qY\357\366\276(\377\351jW\347a\227[\254\345\225e\240\325u{\266\314\274\304"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "soloader"
    version: "0.10.5"
  }
  digests {
    sha256: "\254\035m\227\345,?\344\017\366\337\367\270\253\216\327\313X\022+?#+\352\340\005\214\277j!\351\316"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "annotation"
    version: "0.10.5"
  }
  digests {
    sha256: "\255\f\207\256\367\002\233d\311\364\177\320\220K\024;-]\005\364\224S\262\370\001\2425\031C\261\324\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "ui-common"
    version: "2.5.0"
  }
  digests {
    sha256: "N\333\242\325\244\373\333\206/\330\235!\016d\006\003\324\254\341\3745\323\240\3309\361n{\360\245\"-"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "drawee"
    version: "2.5.0"
  }
  digests {
    sha256: "\344\315\035\312\207y;\350P \274\220\203P|\351\271*\377\274\211\210-vt\300\370;\240F\360\336"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline"
    version: "2.5.0"
  }
  digests {
    sha256: "\242\327\t,\233\302\025\001Y\316{\347\227\355\366\252\300\246\221<\345\253\213\002I!o\375F\267S\'"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.parse.bolts"
    artifactId: "bolts-tasks"
    version: "1.4.0"
  }
  digests {
    sha256: "\233\305\036>\312\205:\235\362\373\254b\3021}\262\311\352a\233\225\265S\0340D\002C\332\254\316\342"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-base"
    version: "2.5.0"
  }
  digests {
    sha256: "\223\306\320\277\315\216\271Y(\221\361\263*C\024\251\202\362%\020\337D\2331q.\227#\320\227\265\355"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.infer.annotation"
    artifactId: "infer-annotation"
    version: "0.18.0"
  }
  digests {
    sha256: "\312\357\272\223VC\334\226\'X\247`\361bz@\"\025\257\003M\226\300\364\204\326;\177\335\312\231/"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-annotations-jvm"
    version: "1.3.72"
  }
  digests {
    sha256: ">\343\245m\324Q\343?\203R\340\322\200:U\312\033\221V\327\370\250\340u\233\370\341\207\377\215\262J"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-native"
    version: "2.5.0"
  }
  digests {
    sha256: "E@\227g%\000\335\344\003\300\2154Z\331k0\021\254u_\270\017\2557/\242\270\350*\263\357\352"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-ashmem"
    version: "2.5.0"
  }
  digests {
    sha256: "\355\200\304 \030\304\3751\222\030\274#\226\343\213\'3{T\034E\301\357\352\253\316\266\302\254\341\367\350"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-native"
    version: "2.5.0"
  }
  digests {
    sha256: "\253X\207\274\201\262\0025 Jz;\006\0060\324\352f\216\366\377\253\361\352\247\231\307j\263\312)\335"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-java"
    version: "2.5.0"
  }
  digests {
    sha256: "\213\fi2\247\213\025\367[\217\024\3405\330?\370b\033\204\376{3$ZM5|\032\245\253\257\220"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "middleware"
    version: "2.5.0"
  }
  digests {
    sha256: "\004\2219\232\0375\026S\207R\027\245\000\343\261\3435n\251{\a\233\321#\345\212,p\305\264J\024"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "nativeimagefilters"
    version: "2.5.0"
  }
  digests {
    sha256: "\200\310\314\f\375\231\240\362ns}\t\307\251\272m\fI\315\300\v\342\334\366\255f\255\027K\2743\374"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "nativeimagetranscoder"
    version: "2.5.0"
  }
  digests {
    sha256: "\265\031H\354*\256s\253\3527\315\376\205F\037\362\304\263\322\\\242+\023\256\212V+\203&\304%R"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-okhttp3"
    version: "2.5.0"
  }
  digests {
    sha256: "\n\2112cU|M\312KEk6T}&t\301v\225\201f\r\316\021\333\260@q\b7\306\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.9.2"
  }
  digests {
    sha256: ";.\341\267h\301\337(\303\r/\346\263\215\332M,Q\222\020\343\f\243\322yPa\214V<\222\336"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "2.9.0"
  }
  digests {
    sha256: "\271%\\\026;~\334\v\204\006U\235fW\234l2\336\240\037i\031C\272\305\323\375\275\020\366\233D"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.yoga"
    artifactId: "proguard-annotations"
    version: "1.19.0"
  }
  digests {
    sha256: "\373e\367\006\356\240[V@\373k\223\322\035lA\371\342\264\347\246\346\331\362\214\351\322\245tGI\225"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp-urlconnection"
    version: "4.9.2"
  }
  digests {
    sha256: "\016\0349\252\211f\217\226%\311 z\315J\207\204\334\206\212\320\325\242\001\035\236\025\340gO;}\021"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "animated-gif"
    version: "2.5.0"
  }
  digests {
    sha256: "\304e\314Y\223\350#<9\335\305\343/\335/1j\270/RJ_\340\345\304X\341B\200Fq\270"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "animated-base"
    version: "2.5.0"
  }
  digests {
    sha256: "\241h~u\264}\341\345n\320P\360\352\220\306\303\2364\326Y\326\3776\330\201\302bl\310Y\272R"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "animated-drawable"
    version: "2.5.0"
  }
  digests {
    sha256: "G\b\301\231.Pk\22780\006]%Kc\334?L\240\003b\307\314\363\017{pY\231w\021r"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "webpsupport"
    version: "2.5.0"
  }
  digests {
    sha256: "\017-\3377\230\242\251\245\314.1^\251!\306~\024v\036>\311\251\271Hz\332\323v\376\227\235v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.react"
    artifactId: "hermes-android"
    version: "0.72.6"
  }
  digests {
    sha256: "\313\004\222\2205\031\372O(\177/gH\300\251\373m\211\227\272\330\263I[Z\255a\004\002U\231\262"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-reflect"
    version: "1.8.10"
  }
  digests {
    sha256: "\231g$\020E\220E\t\r\006*\001\224\355\207\000\216#q\360iF\266\277\247(|i\177\222K\352"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.5.7"
  }
  digests {
    sha256: "\335\363\026\f\r\254\r\002\200F\200\333\253\305\304\030f\311)P+\202\260\0244\301\374?\005+7\001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "+\374TG\\\004q1\2213a\365m\017\177\001\234n[\356S\356\260\353}\224\247\304\231\240R\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "\221M\214\203\252\026E]C\033[:\363\006\316\360\353\n\v\375eP\320\227\343\232\324uC[\365l"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.android.installreferrer"
    artifactId: "installreferrer"
    version: "1.0"
  }
  digests {
    sha256: "o\200\357\221$4\250D%\227z\220\264\032\262bY\367\320\311S\355\257\220\003\236}\001\324C\203\253"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "commons-io"
    artifactId: "commons-io"
    version: "2.6"
  }
  digests {
    sha256: "\370w\323\004f\n\302\241B\363\206[\255\374\227\035\354~\327<t|\177\215]/Q9\312se\023"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "7.4.2"
  }
  digests {
    sha256: "\276u{5%3\313\230\276\225-\342_\223\361\251\277\001s(n\n\343#\035[H\322\032\347\312\265"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-extensions"
    version: "2.2.0"
  }
  digests {
    sha256: "d\214\215\341\321\v\002]RJ.F\254\231O\303\366\277\030h&\300\236\301\246-%\v\361\270w\256"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.insert-koin"
    artifactId: "koin-core"
    version: "3.4.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.insert-koin"
    artifactId: "koin-core-jvm"
    version: "3.4.0"
  }
  digests {
    sha256: "o\311S\217\256A\036\255\356\333\024\221\375iN\274c5r\025~\333\027o+,\237h^\"\231<"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.2.0"
  }
  digests {
    sha256: "\212(\307\020p\'\033o\200/Z(Z\271\256[BN`\244\364\273Ns\300\306\365<\023uPm"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.2.0"
  }
  digests {
    sha256: "w\224\b\261\2523\f\324\247\255\361\262\350_\322\211\303\016~\335.\216B\204{}\006X\232\367\025\372"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.2.1"
  }
  digests {
    sha256: "\323\320\314wo#A\332\216W%\206\307\323\220\245\263V\3169\240\336\262v\200q\334@\263d\254\200"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.8.6"
  }
  digests {
    sha256: "\310\373H9\005M(\v03\370\000\321\365\251}\342\360(\353\213\242\353E\212\322\207\3456\363\362_"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-codec"
    artifactId: "commons-codec"
    version: "1.10"
  }
  digests {
    sha256: "BA\337\251Nq\035C_)\244`J>-\345\304\252<\026^#\275\006k\346\374\037\3040\225i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-v4"
    version: "1.0.0"
  }
  digests {
    sha256: "x\376\301H_\0178\212GI\002-\325\024\026\205q\'\315%D\256\034?\320\261e\211\005T\200\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.0.0"
  }
  digests {
    sha256: "\262;R{+\254\207\fJtQ\346\230-q2\344\023\350\215\177\'\333\353\037\307d\nr\f\331\356"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.0.0"
  }
  digests {
    sha256: "v\277\373|\357\277x\a\224\330\201p\002\332\321V/>\'\300\251\367F\326$\001\310\355\263\n\356\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.13.2"
  }
  digests {
    sha256: "\277\2128\224\347\272\210D\324c4\227\332nwC\"\341\016\260\225k\345\201D\227\275\323\201\355\201\023"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.13.2"
  }
  digests {
    sha256: "\361\345\252\207Ie\177\271}\355\321C\3539m\027\026\354)\023\306\3506O\323\361\2227\332\\\t7"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.13.2"
  }
  digests {
    sha256: "\353\337\327<\tf0a\376br}<\312L#\363\203\351f\355\006L\321\352\016\b].\036r\334"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.13.2"
  }
  digests {
    sha256: "\005QK\261bL\257\3438\'\346\360 k==\205\223B\312\311K\315\261R\334\030\352X(3\300"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.3"
  }
  digests {
    sha256: "\231h\024\230L\263=\220\222\020d\310g\320\254A\337\372\020\241\004\212\332e(\201\302\023&`#\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.CanHub"
    artifactId: "Android-Image-Cropper"
    version: "4.3.1"
  }
  digests {
    sha256: "\324\212r&\354\231\223j\347wo\267\2145\251\246\202\264\366\264WI\242U\233_\031@\233\f\206\343"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "1.7.0"
  }
  digests {
    sha256: "\231\375l{\317\350\327\177\245\306,qeeM+\205\372\210\031\246=\263\251\324\321k\240\367\315@}"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "1.7.0"
  }
  digests {
    sha256: "\230\261\365C\370\350\"\230\236h\232I\006\221\237_?\3677\260\200\272\365C5\204\265\277r\210&\246"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.devtools.ksp"
    artifactId: "symbol-processing-api"
    version: "1.8.10-1.0.9"
  }
  digests {
    sha256: "\020\342\260\264\203\273X\266X\244\325\263\331g0\216\322\311\310\212\341\'oL\"\002\323\215\357\254\231\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup"
    artifactId: "kotlinpoet"
    version: "1.12.0"
  }
  digests {
    sha256: "\216?xI\315\373Sv\310z\312L\324\nk\226\373\260-\337\006\vb\021\t\237]\025!\021q\316"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup"
    artifactId: "kotlinpoet-ksp"
    version: "1.12.0"
  }
  digests {
    sha256: "\324\267\001W\002us\370\217\204\016\036\205\230.J\177\373FG\322\341\030h\274\177\217\227\003X\312q"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 52
  library_dep_index: 53
  library_dep_index: 26
  library_dep_index: 54
  library_dep_index: 56
  library_dep_index: 76
  library_dep_index: 61
  library_dep_index: 66
  library_dep_index: 59
  library_dep_index: 79
  library_dep_index: 67
  library_dep_index: 80
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 81
  library_dep_index: 19
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 40
}
library_dependencies {
  library_index: 3
  library_dep_index: 2
}
library_dependencies {
  library_index: 4
  library_dep_index: 2
  library_dep_index: 5
  library_dep_index: 3
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 36
  library_dep_index: 32
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
  library_dep_index: 8
}
library_dependencies {
  library_index: 9
  library_dep_index: 2
  library_dep_index: 10
}
library_dependencies {
  library_index: 11
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 35
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 12
  library_dep_index: 2
}
library_dependencies {
  library_index: 13
  library_dep_index: 2
  library_dep_index: 12
}
library_dependencies {
  library_index: 14
  library_dep_index: 2
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 11
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 15
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 16
  library_dep_index: 17
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 7
}
library_dependencies {
  library_index: 18
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
  library_dep_index: 20
}
library_dependencies {
  library_index: 20
  library_dep_index: 6
}
library_dependencies {
  library_index: 21
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 22
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 11
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 22
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 11
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 23
  library_dep_index: 22
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 11
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 24
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 25
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 11
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 25
  library_dep_index: 2
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 2
}
library_dependencies {
  library_index: 27
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 11
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 28
  library_dep_index: 11
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 11
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 29
  library_dep_index: 2
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 11
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 30
  library_dep_index: 29
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 11
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 31
}
library_dependencies {
  library_index: 31
  library_dep_index: 2
  library_dep_index: 32
  library_dep_index: 22
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 11
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 32
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 4
}
library_dependencies {
  library_index: 33
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 6
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 33
  library_dep_index: 6
  library_dep_index: 33
}
library_dependencies {
  library_index: 35
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 25
  library_dep_index: 10
}
library_dependencies {
  library_index: 36
  library_dep_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 37
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 38
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 3
}
library_dependencies {
  library_index: 39
  library_dep_index: 2
}
library_dependencies {
  library_index: 40
  library_dep_index: 41
  library_dep_index: 2
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 32
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 48
  library_dep_index: 11
  library_dep_index: 29
  library_dep_index: 51
  library_dep_index: 33
  library_dep_index: 6
  library_dep_index: 1
}
library_dependencies {
  library_index: 41
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 11
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 26
  library_dep_index: 6
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 41
  library_dep_index: 32
  library_dep_index: 27
  library_dep_index: 30
  library_dep_index: 34
  library_dep_index: 6
  library_dep_index: 41
}
library_dependencies {
  library_index: 43
  library_dep_index: 2
}
library_dependencies {
  library_index: 44
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 45
}
library_dependencies {
  library_index: 45
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 46
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 25
}
library_dependencies {
  library_index: 47
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 46
}
library_dependencies {
  library_index: 48
  library_dep_index: 41
  library_dep_index: 2
  library_dep_index: 5
  library_dep_index: 3
  library_dep_index: 32
  library_dep_index: 22
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 49
  library_dep_index: 33
  library_dep_index: 50
  library_dep_index: 6
}
library_dependencies {
  library_index: 49
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 21
  library_dep_index: 29
}
library_dependencies {
  library_index: 50
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 45
}
library_dependencies {
  library_index: 51
  library_dep_index: 2
}
library_dependencies {
  library_index: 52
  library_dep_index: 4
}
library_dependencies {
  library_index: 53
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 39
}
library_dependencies {
  library_index: 54
  library_dep_index: 55
}
library_dependencies {
  library_index: 56
  library_dep_index: 57
  library_dep_index: 55
  library_dep_index: 61
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 74
  library_dep_index: 75
}
library_dependencies {
  library_index: 57
  library_dep_index: 58
  library_dep_index: 59
}
library_dependencies {
  library_index: 59
  library_dep_index: 60
  library_dep_index: 55
}
library_dependencies {
  library_index: 61
  library_dep_index: 58
}
library_dependencies {
  library_index: 62
  library_dep_index: 58
  library_dep_index: 63
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 61
  library_dep_index: 73
}
library_dependencies {
  library_index: 63
  library_dep_index: 55
  library_dep_index: 60
  library_dep_index: 64
  library_dep_index: 58
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 66
  library_dep_index: 60
  library_dep_index: 64
  library_dep_index: 58
}
library_dependencies {
  library_index: 66
  library_dep_index: 67
  library_dep_index: 68
}
library_dependencies {
  library_index: 69
  library_dep_index: 63
  library_dep_index: 58
  library_dep_index: 59
}
library_dependencies {
  library_index: 70
  library_dep_index: 58
  library_dep_index: 63
}
library_dependencies {
  library_index: 71
  library_dep_index: 58
  library_dep_index: 63
  library_dep_index: 69
  library_dep_index: 55
}
library_dependencies {
  library_index: 72
  library_dep_index: 58
  library_dep_index: 63
  library_dep_index: 69
}
library_dependencies {
  library_index: 73
  library_dep_index: 58
  library_dep_index: 61
}
library_dependencies {
  library_index: 74
  library_dep_index: 63
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 55
  library_dep_index: 64
  library_dep_index: 58
}
library_dependencies {
  library_index: 75
  library_dep_index: 65
  library_dep_index: 55
  library_dep_index: 64
  library_dep_index: 58
}
library_dependencies {
  library_index: 76
  library_dep_index: 58
  library_dep_index: 63
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 77
}
library_dependencies {
  library_index: 77
  library_dep_index: 78
  library_dep_index: 6
}
library_dependencies {
  library_index: 78
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 80
  library_dep_index: 77
  library_dep_index: 19
}
library_dependencies {
  library_index: 82
  library_dep_index: 64
  library_dep_index: 55
  library_dep_index: 58
  library_dep_index: 83
}
library_dependencies {
  library_index: 83
  library_dep_index: 64
  library_dep_index: 58
  library_dep_index: 65
  library_dep_index: 63
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 84
}
library_dependencies {
  library_index: 84
  library_dep_index: 65
  library_dep_index: 62
  library_dep_index: 58
}
library_dependencies {
  library_index: 85
  library_dep_index: 55
  library_dep_index: 64
  library_dep_index: 58
  library_dep_index: 65
}
library_dependencies {
  library_index: 86
  library_dep_index: 54
  library_dep_index: 59
  library_dep_index: 79
  library_dep_index: 2
}
library_dependencies {
  library_index: 87
  library_dep_index: 6
}
library_dependencies {
  library_index: 88
  library_dep_index: 42
  library_dep_index: 89
  library_dep_index: 32
  library_dep_index: 48
  library_dep_index: 23
  library_dep_index: 30
  library_dep_index: 34
  library_dep_index: 6
}
library_dependencies {
  library_index: 89
  library_dep_index: 6
  library_dep_index: 3
}
library_dependencies {
  library_index: 90
  library_dep_index: 26
  library_dep_index: 6
}
library_dependencies {
  library_index: 93
  library_dep_index: 2
}
library_dependencies {
  library_index: 94
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 48
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 24
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
}
library_dependencies {
  library_index: 96
  library_dep_index: 19
  library_dep_index: 7
}
library_dependencies {
  library_index: 97
  library_dep_index: 4
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 3
  library_dep_index: 9
  library_dep_index: 39
}
library_dependencies {
  library_index: 98
  library_dep_index: 3
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 45
}
library_dependencies {
  library_index: 99
  library_dep_index: 2
  library_dep_index: 40
  library_dep_index: 100
  library_dep_index: 98
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 48
  library_dep_index: 11
  library_dep_index: 101
  library_dep_index: 102
  library_dep_index: 37
  library_dep_index: 103
}
library_dependencies {
  library_index: 100
  library_dep_index: 2
}
library_dependencies {
  library_index: 101
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 45
  library_dep_index: 3
}
library_dependencies {
  library_index: 102
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 103
  library_dep_index: 2
  library_dep_index: 48
  library_dep_index: 101
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 106
  library_dep_index: 4
  library_dep_index: 107
  library_dep_index: 108
  library_dep_index: 112
  library_dep_index: 48
}
library_dependencies {
  library_index: 107
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 36
}
library_dependencies {
  library_index: 108
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 109
  library_dep_index: 49
  library_dep_index: 110
  library_dep_index: 111
}
library_dependencies {
  library_index: 109
  library_dep_index: 2
}
library_dependencies {
  library_index: 110
  library_dep_index: 2
}
library_dependencies {
  library_index: 111
  library_dep_index: 2
}
library_dependencies {
  library_index: 112
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 108
  library_dep_index: 45
  library_dep_index: 50
  library_dep_index: 98
  library_dep_index: 44
  library_dep_index: 113
  library_dep_index: 39
  library_dep_index: 53
  library_dep_index: 114
  library_dep_index: 43
}
library_dependencies {
  library_index: 113
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 45
}
library_dependencies {
  library_index: 114
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 115
  library_dep_index: 116
  library_dep_index: 117
  library_dep_index: 118
  library_dep_index: 48
  library_dep_index: 38
  library_dep_index: 119
  library_dep_index: 26
}
library_dependencies {
  library_index: 116
  library_dep_index: 2
}
library_dependencies {
  library_index: 119
  library_dep_index: 2
}
library_dependencies {
  library_index: 120
  library_dep_index: 121
  library_dep_index: 93
  library_dep_index: 19
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 119
  library_dep_index: 32
  library_dep_index: 16
  library_dep_index: 15
}
library_dependencies {
  library_index: 121
  library_dep_index: 6
  library_dep_index: 122
}
library_dependencies {
  library_index: 122
  library_dep_index: 6
}
library_dependencies {
  library_index: 123
  library_dep_index: 6
}
library_dependencies {
  library_index: 124
  library_dep_index: 87
  library_dep_index: 19
}
library_dependencies {
  library_index: 125
  library_dep_index: 124
  library_dep_index: 19
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 56
  dependency_index: 76
  dependency_index: 82
  dependency_index: 85
  dependency_index: 86
  dependency_index: 19
  dependency_index: 16
  dependency_index: 15
  dependency_index: 32
  dependency_index: 20
  dependency_index: 87
  dependency_index: 2
  dependency_index: 40
  dependency_index: 42
  dependency_index: 88
  dependency_index: 90
  dependency_index: 91
  dependency_index: 92
  dependency_index: 93
  dependency_index: 94
  dependency_index: 95
  dependency_index: 104
  dependency_index: 77
  dependency_index: 97
  dependency_index: 98
  dependency_index: 99
  dependency_index: 102
  dependency_index: 105
  dependency_index: 80
  dependency_index: 78
  dependency_index: 106
  dependency_index: 115
  dependency_index: 119
  dependency_index: 120
  dependency_index: 123
  dependency_index: 124
  dependency_index: 125
  dependency_index: 6
  dependency_index: 48
  dependency_index: 53
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://www.jitpack.io"
  }
}
repositories {
  maven_repo {
    url: "https://oss.sonatype.org/content/repositories/snapshots/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://www.jitpack.io"
  }
}
