1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.cadetvocab.quiz"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:2:3-64
11-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:2:20-62
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:3:3-77
12-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:3:20-75
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:4:3-68
13-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:4:20-66
14    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
14-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:5:3-75
14-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:5:20-73
15    <uses-permission android:name="android.permission.VIBRATE" />
15-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:6:3-63
15-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:6:20-61
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:7:3-78
16-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:7:20-76
17
18    <queries>
18-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:8:3-14:13
19        <intent>
19-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:9:5-13:14
20            <action android:name="android.intent.action.VIEW" />
20-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:10:7-58
20-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:10:15-56
21
22            <category android:name="android.intent.category.BROWSABLE" />
22-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:11:7-67
22-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:11:17-65
23
24            <data android:scheme="https" />
24-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:12:7-37
24-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:12:13-35
25        </intent>
26        <!-- Query open documents -->
27        <intent>
27-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:9-17:18
28            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
28-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:13-79
28-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:21-76
29        </intent>
30        <intent>
30-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:17:9-21:18
31
32            <!-- Required for picking images from the camera roll if targeting API 30 -->
33            <action android:name="android.media.action.IMAGE_CAPTURE" />
33-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:20:13-73
33-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:20:21-70
34        </intent>
35        <intent>
35-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:22:9-26:18
36
37            <!-- Required for picking images from the camera if targeting API 30 -->
38            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
38-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:13-80
38-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:21-77
39        </intent>
40        <intent>
40-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
41            <action android:name="android.intent.action.GET_CONTENT" />
41-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
41-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
42
43            <category android:name="android.intent.category.OPENABLE" />
43-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
43-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
44
45            <data android:mimeType="*/*" />
45-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:12:7-37
46        </intent>
47    </queries>
48
49    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
49-->[:react-native-community_netinfo] D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:7:5-79
49-->[:react-native-community_netinfo] D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:7:22-76
50    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- Required for picking images from camera directly -->
50-->[:react-native-community_netinfo] D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:5-76
50-->[:react-native-community_netinfo] D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:22-73
51    <uses-permission android:name="android.permission.CAMERA" />
51-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:5-65
51-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:22-62
52    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
52-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:5-76
52-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:22-73
53    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
53-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:5-75
53-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:22-72
54
55    <permission
55-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
56        android:name="com.cadetvocab.quiz.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
56-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
57        android:protectionLevel="signature" />
57-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
58
59    <uses-permission android:name="com.cadetvocab.quiz.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
59-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
59-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
60    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
60-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4fa6ead3d9a5c128fcee92bb95f998fc\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
60-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4fa6ead3d9a5c128fcee92bb95f998fc\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
61
62    <application
62-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:3-34:17
63        android:name="com.cadetvocab.quiz.MainApplication"
63-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:16-47
64        android:allowBackup="true"
64-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:162-188
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
66        android:icon="@mipmap/ic_launcher"
66-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:81-115
67        android:label="@string/app_name"
67-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:48-80
68        android:roundIcon="@mipmap/ic_launcher_round"
68-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:116-161
69        android:theme="@style/AppTheme" >
69-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:189-220
70        <meta-data
70-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:16:5-83
71            android:name="expo.modules.updates.ENABLED"
71-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:16:16-59
72            android:value="false" />
72-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:16:60-81
73        <meta-data
73-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:17:5-93
74            android:name="expo.modules.updates.EXPO_SDK_VERSION"
74-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:17:16-68
75            android:value="49.0.0" />
75-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:17:69-91
76        <meta-data
76-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:18:5-105
77            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
77-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:18:16-80
78            android:value="ALWAYS" />
78-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:18:81-103
79        <meta-data
79-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:19:5-99
80            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
80-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:19:16-79
81            android:value="0" />
81-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:19:80-97
82
83        <activity
83-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:5-32:16
84            android:name="com.cadetvocab.quiz.MainActivity"
84-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:15-43
85            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
85-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:77-154
86            android:exported="true"
86-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:276-299
87            android:label="@string/app_name"
87-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:44-76
88            android:launchMode="singleTask"
88-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:155-186
89            android:screenOrientation="portrait"
89-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:300-336
90            android:theme="@style/Theme.App.SplashScreen"
90-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:230-275
91            android:windowSoftInputMode="adjustResize" >
91-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:187-229
92            <intent-filter>
92-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:21:7-24:23
93                <action android:name="android.intent.action.MAIN" />
93-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:22:9-60
93-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:22:17-58
94
95                <category android:name="android.intent.category.LAUNCHER" />
95-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:23:9-68
95-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:23:19-66
96            </intent-filter>
97            <intent-filter>
97-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:25:7-31:23
98                <action android:name="android.intent.action.VIEW" />
98-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:10:7-58
98-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:10:15-56
99
100                <category android:name="android.intent.category.DEFAULT" />
100-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:27:9-67
100-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:27:19-65
101                <category android:name="android.intent.category.BROWSABLE" />
101-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:11:7-67
101-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:11:17-65
102
103                <data android:scheme="com.cadetvocab.quiz" />
103-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:12:7-37
103-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:12:13-35
104                <data android:scheme="exp+cadetvocabquiz" />
104-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:12:7-37
104-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:12:13-35
105            </intent-filter>
106        </activity>
107        <activity
107-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:33:5-106
108            android:name="com.facebook.react.devsupport.DevSettingsActivity"
108-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:33:15-79
109            android:exported="false" />
109-->D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:33:80-104
110
111        <provider
111-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:21:9-30:20
112            android:name="expo.modules.filesystem.FileSystemFileProvider"
112-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:22:13-74
113            android:authorities="com.cadetvocab.quiz.FileSystemFileProvider"
113-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:23:13-74
114            android:exported="false"
114-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:24:13-37
115            android:grantUriPermissions="true" >
115-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:13-47
116            <meta-data
116-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:27:13-29:70
117                android:name="android.support.FILE_PROVIDER_PATHS"
117-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:28:17-67
118                android:resource="@xml/file_system_provider_paths" />
118-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:29:17-67
119        </provider>
120
121        <service
121-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:30:9-42:19
122            android:name="com.google.android.gms.metadata.ModuleDependencies"
122-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:31:13-78
123            android:enabled="false"
123-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:32:13-36
124            android:exported="false" >
124-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:33:13-37
125            <intent-filter>
125-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:35:13-37:29
126                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
126-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:36:17-94
126-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:36:25-91
127            </intent-filter>
128
129            <meta-data
129-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:39:13-41:36
130                android:name="photopicker_activity:0:required"
130-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:40:17-63
131                android:value="" />
131-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:41:17-33
132        </service>
133
134        <activity
134-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:44:9-46:59
135            android:name="com.canhub.cropper.CropImageActivity"
135-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:45:13-64
136            android:exported="true"
136-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
137            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
137-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:46:13-56
138        <provider
138-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:48:9-56:20
139            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
139-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:49:13-89
140            android:authorities="com.cadetvocab.quiz.ImagePickerFileProvider"
140-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:50:13-75
141            android:exported="false"
141-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:51:13-37
142            android:grantUriPermissions="true" >
142-->[:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:52:13-47
143            <meta-data
143-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:27:13-29:70
144                android:name="android.support.FILE_PROVIDER_PATHS"
144-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:28:17-67
145                android:resource="@xml/image_picker_provider_paths" />
145-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:29:17-67
146        </provider>
147
148        <meta-data
148-->[:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:9-11:89
149            android:name="org.unimodules.core.AppLoader#react-native-headless"
149-->[:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:10:13-79
150            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
150-->[:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:13-86
151        <meta-data
151-->[:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:9-15:45
152            android:name="com.facebook.soloader.enabled"
152-->[:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:13-57
153            android:value="true" />
153-->[:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:13-33
154
155        <provider
155-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
156            android:name="com.canhub.cropper.CropFileProvider"
156-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
157            android:authorities="com.cadetvocab.quiz.cropper.fileprovider"
157-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
158            android:exported="false"
158-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
159            android:grantUriPermissions="true" >
159-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
160            <meta-data
160-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:27:13-29:70
161                android:name="android.support.FILE_PROVIDER_PATHS"
161-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:28:17-67
162                android:resource="@xml/library_file_paths" />
162-->[:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:29:17-67
163        </provider>
164        <provider
164-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
165            android:name="androidx.startup.InitializationProvider"
165-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
166            android:authorities="com.cadetvocab.quiz.androidx-startup"
166-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
167            android:exported="false" >
167-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
168            <meta-data
168-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
169                android:name="androidx.emoji2.text.EmojiCompatInitializer"
169-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
170                android:value="androidx.startup" />
170-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
171            <meta-data
171-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
172                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
172-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
173                android:value="androidx.startup" />
173-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
174            <meta-data
174-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
175                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
175-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
176                android:value="androidx.startup" />
176-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
177        </provider>
178
179        <receiver
179-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
180            android:name="androidx.profileinstaller.ProfileInstallReceiver"
180-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
181            android:directBootAware="false"
181-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
182            android:enabled="true"
182-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
183            android:exported="true"
183-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
184            android:permission="android.permission.DUMP" >
184-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
185            <intent-filter>
185-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
186                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
186-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
186-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
187            </intent-filter>
188            <intent-filter>
188-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
189                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
189-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
189-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
190            </intent-filter>
191            <intent-filter>
191-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
192                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
192-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
192-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
193            </intent-filter>
194            <intent-filter>
194-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
195                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
195-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
195-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
196            </intent-filter>
197        </receiver>
198    </application>
199
200</manifest>
