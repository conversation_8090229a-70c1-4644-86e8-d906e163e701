import AsyncStorage from "@react-native-async-storage/async-storage";
import { QuizQuestion, QuizSet } from "../types";
import { quizService } from "./quizService";
import { userService } from "./userService";
import { getAuth } from "firebase/auth";

const STORAGE_KEY = "daily_challenge_cache";

export interface DailyChallenge {
  date: string; // YYYY-MM-DD format
  question: QuizQuestion;
  quizSetId: string;
  completed: boolean;
}

/**
 * Gets the current day of the year (1-366).
 * @returns {number} The current day of the year.
 */
const getDayOfYear = (): number => {
  const now = new Date();
  const start = new Date(now.getFullYear(), 0, 0);
  const diff = now.getTime() - start.getTime();
  const oneDay = 1000 * 60 * 60 * 24;
  return Math.floor(diff / oneDay);
};

export const dailyChallengeService = {
  /**
   * Generates or retrieves the daily challenge.
   * Ensures the challenge is the same for all users for a given day.
   */
  async getDailyChallenge(): Promise<DailyChallenge | null> {
    const today = new Date().toISOString().split("T")[0];
    const userId = getAuth().currentUser?.uid;

    try {
      // 1. Check local cache first for today's challenge
      const cachedChallenge = await AsyncStorage.getItem(STORAGE_KEY);
      if (cachedChallenge) {
        const challenge = JSON.parse(cachedChallenge) as DailyChallenge;
        if (challenge.date === today) {
          console.log("[dailyChallengeService] CACHE HIT: Returning daily challenge from local cache.");
          return challenge;
        }
      }

      console.log("[dailyChallengeService] CACHE MISS: Generating new daily challenge.");

      // 2. Fetch all quiz sets (this is cached in quizService, so it's fast)
      const quizSets = await quizService.getAllQuizSets();
      if (!quizSets || quizSets.length === 0) {
        throw new Error("No quiz sets available to generate a challenge.");
      }

      // 3. Deterministically select a quiz and question based on the day of the year
      const dayOfYear = getDayOfYear();
      const quizSetIndex = dayOfYear % quizSets.length;
      const selectedQuizSet = quizSets[quizSetIndex];

      if (!selectedQuizSet.questions || selectedQuizSet.questions.length === 0) {
        // Fallback for an empty quiz set
        throw new Error(`Selected quiz set (${selectedQuizSet.id}) has no questions.`);
      }

      const questionIndex = dayOfYear % selectedQuizSet.questions.length;
      const selectedQuestion = selectedQuizSet.questions[questionIndex];

      // 4. Check if the user has already completed this challenge
      let isCompleted = false;
      if (userId) {
        const userProfile = await userService.getUserProfile(userId);
        isCompleted = userProfile?.stats?.completedDailyChallenges?.includes(today) ?? false;
      }

      // 5. Construct and cache the new challenge object
      const newChallenge: DailyChallenge = {
        date: today,
        question: selectedQuestion,
        quizSetId: selectedQuizSet.id,
        completed: isCompleted,
      };

      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(newChallenge));
      console.log("[dailyChallengeService] Generated and cached new daily challenge.");

      return newChallenge;
    } catch (error) {
      console.error("Error getting daily challenge:", error);
      return null;
    }
  },

  /**
   * Marks the current daily challenge as completed for the logged-in user.
   */
  async markChallengeCompleted(): Promise<void> {
    const userId = getAuth().currentUser?.uid;
    if (!userId) {
      console.warn("Cannot mark challenge completed. User not authenticated.");
      return;
    }

    const today = new Date().toISOString().split("T")[0];

    try {
      // Update local cache immediately for instant UI feedback
      const cachedChallenge = await AsyncStorage.getItem(STORAGE_KEY);
      if (cachedChallenge) {
        const challenge = JSON.parse(cachedChallenge) as DailyChallenge;
        if (challenge.date === today && !challenge.completed) {
          challenge.completed = true;
          await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(challenge));
        }
      }

      // Update the user's profile in Firestore
      await userService.addCompletedDailyChallenge(userId, today);
      console.log(`[dailyChallengeService] Marked challenge for date ${today} as completed for user ${userId}.`);
    } catch (error) {
      console.error("Error marking daily challenge completed:", error);
    }
  },
}; 