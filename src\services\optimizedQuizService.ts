/**
 * Optimized Quiz Service for Minimal Firebase Reads/Writes
 * 
 * Strategy:
 * 1. Hierarchical caching (metadata vs full data)
 * 2. Type-based data organization
 * 3. Incremental updates
 * 4. Smart cache invalidation
 */

import { collection, getDocs, doc, getDoc, query, where, orderBy } from 'firebase/firestore';
import { db } from '../config/firebase';
import { QuizSet } from '../types';
import { getCachedData, cacheData } from '../utils/cache';

const COLLECTION_NAME = 'quizSets';
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

// Hierarchical cache keys
const CACHE_KEYS = {
  TYPES_METADATA: 'quiz_types_metadata_v1',        // Just type names and counts
  TYPE_QUIZ_SETS: (type: string) => `type_${type}_quizsets_v1`, // Quiz sets by type
  QUIZ_METADATA: 'all_quiz_metadata_v1',           // All quiz metadata (no questions)
  QUIZ_DETAILS: (id: string) => `quiz_${id}_details_v1`, // Full quiz with questions
};

interface QuizMetadata {
  id: string;
  title: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  type: string;
  category: string;
  wordCount: number;
  questionCount: number;
  icon?: string;
}

interface TypeMetadata {
  type: string;
  count: number;
  lastUpdated: number;
}

export const optimizedQuizService = {
  
  /**
   * Get all quiz types with counts (minimal Firebase read)
   * Perfect for Library screen initial load
   */
  getQuizTypesMetadata: async (): Promise<TypeMetadata[]> => {
    const cached = await getCachedData(CACHE_KEYS.TYPES_METADATA);
    if (cached) return cached;

    try {
      // Single query to get all quiz sets metadata only
      const querySnapshot = await getDocs(collection(db, COLLECTION_NAME));
      const typeMap = new Map<string, number>();
      
      querySnapshot.docs.forEach(doc => {
        const data = doc.data();
        const type = data.type || 'Hunter';
        typeMap.set(type, (typeMap.get(type) || 0) + 1);
      });

      const typesMetadata: TypeMetadata[] = Array.from(typeMap.entries()).map(([type, count]) => ({
        type,
        count,
        lastUpdated: Date.now()
      }));

      await cacheData(CACHE_KEYS.TYPES_METADATA, typesMetadata, CACHE_DURATION);
      return typesMetadata;
    } catch (error) {
      console.error('Error getting quiz types metadata:', error);
      throw error;
    }
  },

  /**
   * Get quiz metadata for a specific type (no questions loaded)
   * Perfect for when user expands a category
   */
  getQuizMetadataByType: async (type: string): Promise<QuizMetadata[]> => {
    const cacheKey = CACHE_KEYS.TYPE_QUIZ_SETS(type);
    const cached = await getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const q = query(
        collection(db, COLLECTION_NAME),
        where('type', '==', type),
        orderBy('title')
      );
      
      const querySnapshot = await getDocs(q);
      const metadata: QuizMetadata[] = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          title: data.title || '',
          description: data.description || '',
          difficulty: data.difficulty || 'intermediate',
          type: data.type || 'Hunter',
          category: data.category || '',
          wordCount: data.wordCount || 0,
          questionCount: Array.isArray(data.questions) ? data.questions.length : 0,
          icon: data.icon
        };
      });

      await cacheData(cacheKey, metadata, CACHE_DURATION);
      return metadata;
    } catch (error) {
      console.error(`Error getting quiz metadata for type ${type}:`, error);
      throw error;
    }
  },

  /**
   * Get full quiz details with questions (only when needed)
   * Perfect for when user actually starts a quiz
   */
  getQuizDetails: async (id: string): Promise<QuizSet | null> => {
    const cacheKey = CACHE_KEYS.QUIZ_DETAILS(id);
    const cached = await getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) return null;

      const data = docSnap.data();
      const quizSet: QuizSet = {
        id: docSnap.id,
        title: data.title || '',
        description: data.description || '',
        difficulty: data.difficulty || 'intermediate',
        type: data.type || 'Hunter',
        category: data.category || '',
        wordCount: data.wordCount || 0,
        questions: data.questions || [],
        icon: data.icon,
        progress: data.progress || 0
      };

      await cacheData(cacheKey, quizSet, CACHE_DURATION);
      return quizSet;
    } catch (error) {
      console.error(`Error getting quiz details for ${id}:`, error);
      throw error;
    }
  },

  /**
   * Smart cache invalidation - only invalidate affected caches
   */
  invalidateCache: async (quizSet: Partial<QuizSet>, operation: 'create' | 'update' | 'delete') => {
    const type = quizSet.type || 'Hunter';
    
    // Always invalidate type metadata
    await cacheData(CACHE_KEYS.TYPES_METADATA, null);
    
    // Invalidate specific type cache
    await cacheData(CACHE_KEYS.TYPE_QUIZ_SETS(type), null);
    
    // For updates/deletes, invalidate specific quiz cache
    if (quizSet.id && (operation === 'update' || operation === 'delete')) {
      await cacheData(CACHE_KEYS.QUIZ_DETAILS(quizSet.id), null);
    }
  }
};
