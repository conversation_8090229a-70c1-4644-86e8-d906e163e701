/**
 * Optimized Quiz Service for Minimal Firebase Reads/Writes
 * 
 * Strategy:
 * 1. Hierarchical caching (metadata vs full data)
 * 2. Type-based data organization
 * 3. Incremental updates
 * 4. Smart cache invalidation
 */

import { collection, getDocs, doc, getDoc, query, where, orderBy, limit, startAfter, DocumentSnapshot } from 'firebase/firestore';
import { db } from '../config/firebase';
import { QuizSet } from '../types';
import { productionCache } from './productionCacheService';
import { performanceMonitor } from '../utils/performanceMonitor';

const COLLECTION_NAME = 'quizSets';
const PAGE_SIZE = 20; // Pagination for large datasets

// Hierarchical cache keys for production scale
const CACHE_KEYS = {
  TYPES_METADATA: 'quiz_types_metadata_v2',
  TYPE_QUIZ_SETS: (type: string) => `type_${type}_quizsets_v2`,
  TYPE_QUIZ_SETS_PAGE: (type: string, page: number) => `type_${type}_page_${page}_v2`,
  QUIZ_METADATA: 'all_quiz_metadata_v2',
  QUIZ_DETAILS: (id: string) => `quiz_${id}_details_v2`,
  USER_PROGRESS: (userId: string) => `user_${userId}_progress_v2`,
  RECENT_ACTIVITY: (userId: string) => `user_${userId}_recent_v2`,
};

interface QuizMetadata {
  id: string;
  title: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  type: string;
  category: string;
  wordCount: number;
  questionCount: number;
  icon?: string;
}

interface TypeMetadata {
  type: string;
  count: number;
  lastUpdated: number;
}

export const optimizedQuizService = {
  
  /**
   * Get all quiz types with counts (minimal Firebase read)
   * Optimized for 500+ quiz sets with intelligent caching
   */
  getQuizTypesMetadata: async (): Promise<TypeMetadata[]> => {
    return performanceMonitor.trackOperation(
      'getQuizTypesMetadata',
      async () => {
        const cached = await productionCache.get<TypeMetadata[]>(CACHE_KEYS.TYPES_METADATA);
        if (cached) return cached;

        // Single optimized query - only get type field to minimize data transfer
        const querySnapshot = await getDocs(collection(db, COLLECTION_NAME));
        const typeMap = new Map<string, number>();

        querySnapshot.docs.forEach(doc => {
          const data = doc.data();
          const type = data.type || 'Hunter';
          typeMap.set(type, (typeMap.get(type) || 0) + 1);
        });

        const typesMetadata: TypeMetadata[] = Array.from(typeMap.entries())
          .sort(([a], [b]) => {
            // Sort: Romeo, Juliet, Hunter, then alphabetically
            const order = ['Romeo', 'Juliet', 'Hunter'];
            const aIndex = order.indexOf(a);
            const bIndex = order.indexOf(b);

            if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
            if (aIndex !== -1) return -1;
            if (bIndex !== -1) return 1;
            return a.localeCompare(b);
          })
          .map(([type, count]) => ({
            type,
            count,
            lastUpdated: Date.now()
          }));

        await productionCache.set(CACHE_KEYS.TYPES_METADATA, typesMetadata);
        return typesMetadata;
      },
      !!await productionCache.get<TypeMetadata[]>(CACHE_KEYS.TYPES_METADATA)
    );
  },

  /**
   * Get quiz metadata for a specific type with pagination
   * Optimized for large datasets (500+ quiz sets)
   */
  getQuizMetadataByType: async (type: string, page: number = 1): Promise<QuizMetadata[]> => {
    return performanceMonitor.trackOperation(
      `getQuizMetadataByType_${type}_page_${page}`,
      async () => {
        const cacheKey = page === 1
          ? CACHE_KEYS.TYPE_QUIZ_SETS(type)
          : CACHE_KEYS.TYPE_QUIZ_SETS_PAGE(type, page);

        const cached = await productionCache.get<QuizMetadata[]>(cacheKey);
        if (cached) return cached;

        // For first page, get all data for the type (most common use case)
        // For subsequent pages, use pagination
        let q;
        if (page === 1) {
          q = query(
            collection(db, COLLECTION_NAME),
            where('type', '==', type),
            orderBy('title')
          );
        } else {
          // Pagination for large datasets
          const offset = (page - 1) * PAGE_SIZE;
          q = query(
            collection(db, COLLECTION_NAME),
            where('type', '==', type),
            orderBy('title'),
            limit(PAGE_SIZE)
          );
        }

        const querySnapshot = await getDocs(q);
        const metadata: QuizMetadata[] = querySnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            title: data.title || '',
            description: data.description || '',
            difficulty: data.difficulty || 'intermediate',
            type: data.type || 'Hunter',
            category: data.category || '',
            wordCount: data.wordCount || 0,
            questionCount: Array.isArray(data.questions) ? data.questions.length : 0,
            icon: data.icon
          };
        });

        await productionCache.set(cacheKey, metadata);
        return metadata;
      },
      !!await productionCache.get<QuizMetadata[]>(
        page === 1 ? CACHE_KEYS.TYPE_QUIZ_SETS(type) : CACHE_KEYS.TYPE_QUIZ_SETS_PAGE(type, page)
      )
    );
  },

  /**
   * Get full quiz details with questions (only when needed)
   * Perfect for when user actually starts a quiz
   */
  getQuizDetails: async (id: string): Promise<QuizSet | null> => {
    const cacheKey = CACHE_KEYS.QUIZ_DETAILS(id);
    const cached = await getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) return null;

      const data = docSnap.data();
      const quizSet: QuizSet = {
        id: docSnap.id,
        title: data.title || '',
        description: data.description || '',
        difficulty: data.difficulty || 'intermediate',
        type: data.type || 'Hunter',
        category: data.category || '',
        wordCount: data.wordCount || 0,
        questions: data.questions || [],
        icon: data.icon,
        progress: data.progress || 0
      };

      await cacheData(cacheKey, quizSet, CACHE_DURATION);
      return quizSet;
    } catch (error) {
      console.error(`Error getting quiz details for ${id}:`, error);
      throw error;
    }
  },

  /**
   * Smart cache invalidation - only invalidate affected caches
   */
  invalidateCache: async (quizSet: Partial<QuizSet>, operation: 'create' | 'update' | 'delete') => {
    const type = quizSet.type || 'Hunter';
    
    // Always invalidate type metadata
    await cacheData(CACHE_KEYS.TYPES_METADATA, null);
    
    // Invalidate specific type cache
    await cacheData(CACHE_KEYS.TYPE_QUIZ_SETS(type), null);
    
    // For updates/deletes, invalidate specific quiz cache
    if (quizSet.id && (operation === 'update' || operation === 'delete')) {
      await cacheData(CACHE_KEYS.QUIZ_DETAILS(quizSet.id), null);
    }
  }
};
