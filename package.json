{"name": "cadetvocabquiz", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~1.0.0", "@expo/webpack-config": "^19.0.1", "@hookform/resolvers": "^5.0.1", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "4.4.2", "@react-navigation/bottom-tabs": "^6.5.16", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "expo": "~49.0.0", "expo-dev-client": "~2.4.13", "expo-image-picker": "~14.3.2", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "firebase": "^10.7.1", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.56.4", "react-native": "0.72.6", "react-native-paper": "^5.11.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-vector-icons": "^10.0.3", "react-native-web": "~0.19.6", "zod": "^3.25.39"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native-vector-icons": "^6.4.18", "babel-plugin-module-resolver": "^5.0.2", "crypto-browserify": "^3.12.1", "sharp": "^0.34.2", "stream-browserify": "^3.0.0", "typescript": "^5.1.3"}, "private": true}