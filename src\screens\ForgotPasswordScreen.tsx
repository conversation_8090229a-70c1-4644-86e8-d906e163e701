import React, { useState } from 'react';
import { View, StyleSheet, Alert, StatusBar } from 'react-native';
import { Text, TextInput, Button, Appbar, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { RootStackParamList } from '@/types/index';
import { authService } from '../services/authService';
import { colors } from '../theme/colors';

type ForgotPasswordScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'ForgotPassword'>;

const formSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type FormValues = z.infer<typeof formSchema>;

const ForgotPasswordScreen = () => {
  const navigation = useNavigation<ForgotPasswordScreenNavigationProp>();
  const theme = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data: FormValues) => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccessMessage(null);
      
      await authService.sendPasswordReset(data.email);
      
      setSuccessMessage('If an account with that email exists, a password reset link has been sent.');
      // Optionally, navigate back to Login or show a success state
      // navigation.goBack(); 
    } catch (err: any) {
      setError(err.message || 'Failed to send reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <Appbar.Header
        style={{ backgroundColor: colors.dark.medium }}
        statusBarHeight={0}
      >
        <Appbar.BackAction onPress={() => navigation.goBack()} color={colors.text.primary} />
        <Appbar.Content title="Reset Password" titleStyle={{ color: colors.text.primary, fontWeight: 'bold' }} />
      </Appbar.Header>

      <View style={styles.content}>
        <Text style={styles.infoText}>
          Enter your email address and we'll send you a link to reset your password.
        </Text>

        {error && (
          <View style={styles.messageContainerError}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}
        {successMessage && (
          <View style={styles.messageContainerSuccess}>
            <Text style={styles.successText}>{successMessage}</Text>
          </View>
        )}

        <Controller
          control={control}
          name="email"
          render={({ field: { onChange, value } }) => (
            <View style={styles.inputContainer}>
              <TextInput
                label="Email"
                value={value}
                onChangeText={onChange}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                error={!!errors.email}
                disabled={isLoading}
                textColor={colors.primary}
                theme={{
                  colors: {
                    primary: colors.primary,
                    placeholder: colors.text.secondary,
                    background: colors.dark.medium,
                  },
                }}
              />
              {errors.email && (
                <Text style={styles.errorTextForm}>{errors.email.message}</Text>
              )}
            </View>
          )}
        />

        <Button
          mode="contained"
          onPress={handleSubmit(onSubmit)}
          loading={isLoading}
          disabled={isLoading}
          style={[styles.button, { backgroundColor: colors.primary }]}
          labelStyle={{ color: colors.text.onPrimary }}
        >
          Send Reset Link
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.secondary,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  infoText: {
    color: colors.text.secondary,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 20,
  },
  button: {
    marginTop: 8,
    paddingVertical: 8,
    borderRadius: 8,
  },
  messageContainerError: {
    backgroundColor: '#fee2e2', // Light red
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  messageContainerSuccess: {
    backgroundColor: '#d1fae5', // Light green
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: '#dc2626', // Darker red
    fontSize: 14,
    textAlign: 'center',
  },
  successText: {
    color: '#065f46', // Darker green
    fontSize: 14,
    textAlign: 'center',
  },
  errorTextForm: { // For form validation errors under input
    color: colors.status.error,
    fontSize: 14,
    marginTop: 4,
  }
});

export default ForgotPasswordScreen; 