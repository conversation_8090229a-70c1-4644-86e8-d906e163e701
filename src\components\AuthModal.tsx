import React, { useState, useRef, useEffect } from 'react';
import { View, StyleSheet, Modal, TouchableOpacity } from 'react-native';
import { Text, TextInput, Button } from 'react-native-paper';
import { colors } from '../theme/colors';
import { authService } from '../services/authService';
import { getAuth, reload } from 'firebase/auth';

interface AuthModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  title?: string;
}

const AuthModal = ({ visible, onClose, onSuccess, title = 'Login to Submit Quiz' }: AuthModalProps) => {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [waitingVerification, setWaitingVerification] = useState(false);
  const [verificationMessage, setVerificationMessage] = useState('');
  const [resendDisabled, setResendDisabled] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const resendTimerRef = useRef<NodeJS.Timeout | null>(null);

  const auth = getAuth();

  useEffect(() => {
    // Cleanup on unmount
    return () => {
      if (pollingRef.current) clearInterval(pollingRef.current);
      if (resendTimerRef.current) clearInterval(resendTimerRef.current);
    };
  }, []);

  useEffect(() => {
    if (!visible) {
      // Reset state when modal closes
      setEmail('');
      setPassword('');
      setFirstName('');
      setLastName('');
      setError('');
      setWaitingVerification(false);
      setVerificationMessage('');
      setResendDisabled(false);
      setResendTimer(0);
      if (pollingRef.current) clearInterval(pollingRef.current);
      if (resendTimerRef.current) clearInterval(resendTimerRef.current);
    }
  }, [visible]);

  const startResendTimer = () => {
    setResendDisabled(true);
    setResendTimer(60);
    if (resendTimerRef.current) clearInterval(resendTimerRef.current);
    resendTimerRef.current = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          setResendDisabled(false);
          if (resendTimerRef.current) clearInterval(resendTimerRef.current);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const pollEmailVerification = async () => {
    if (!auth.currentUser) return;
    try {
      await reload(auth.currentUser);
      if (auth.currentUser.emailVerified) {
        setWaitingVerification(false);
        setVerificationMessage('Email verified! Logging you in...');
        if (pollingRef.current) clearInterval(pollingRef.current);
        
        // Create session and complete login
        await authService.signInWithEmail(email, password, true);
        
        setTimeout(() => {
          setVerificationMessage('');
          onSuccess();
          onClose();
        }, 1500);
      }
    } catch (error) {
      console.error('Error polling verification:', error);
    }
  };

  const handleResendVerification = async () => {
    if (!auth.currentUser || resendDisabled) return;
    
    try {
      setLoading(true);
      await authService.resendVerificationEmail(auth.currentUser);
      setVerificationMessage('Verification email resent! Please check your inbox.');
      startResendTimer();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAuth = async () => {
    if (!email || !password || (!isLogin && (!firstName || !lastName))) {
      setError('Please fill in all fields');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      if (isLogin) {
        await authService.signInWithEmail(email, password, true);
        onSuccess();
        onClose();
      } else {
        const user = await authService.registerWithEmail(email, password, firstName, lastName);
        setWaitingVerification(true);
        setVerificationMessage('Please check your email to verify your account.');
        startResendTimer();
        pollingRef.current = setInterval(pollEmailVerification, 2000);
      }
    } catch (err: any) {
      const errorMessage = err.message;
      setError(errorMessage);
      
      // If the error is about email verification during login,
      // start the verification polling
      if (errorMessage.includes('verify your email')) {
        setWaitingVerification(true);
        startResendTimer();
        pollingRef.current = setInterval(pollEmailVerification, 2000);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.title}>
            {isLogin ? title : title.replace('Login', 'Sign Up')}
          </Text>

          {!isLogin && (
            <>
              <TextInput
                label="First Name"
                value={firstName}
                onChangeText={setFirstName}
                mode="outlined"
                style={styles.input}
                autoCapitalize="words"
                editable={!waitingVerification}
              />
              <TextInput
                label="Last Name"
                value={lastName}
                onChangeText={setLastName}
                mode="outlined"
                style={styles.input}
                autoCapitalize="words"
                editable={!waitingVerification}
              />
            </>
          )}

          <TextInput
            label="Email"
            value={email}
            onChangeText={setEmail}
            mode="outlined"
            style={styles.input}
            autoCapitalize="none"
            keyboardType="email-address"
            editable={!waitingVerification}
          />

          <TextInput
            label="Password"
            value={password}
            onChangeText={setPassword}
            mode="outlined"
            style={styles.input}
            secureTextEntry
            editable={!waitingVerification}
          />

          {error ? <Text style={styles.errorText}>{error}</Text> : null}
          {verificationMessage ? <Text style={styles.infoText}>{verificationMessage}</Text> : null}

          {waitingVerification && (
            <Button
              mode="outlined"
              onPress={handleResendVerification}
              disabled={resendDisabled}
              style={styles.resendButton}
            >
              {resendDisabled 
                ? `Resend email in ${resendTimer}s` 
                : 'Resend verification email'}
            </Button>
          )}

          <Button
            mode="contained"
            onPress={handleAuth}
            loading={loading}
            style={styles.button}
            disabled={waitingVerification}
          >
            {isLogin ? 'Login' : 'Sign Up'}
          </Button>

          {!waitingVerification && (
            <TouchableOpacity
              onPress={() => {
                setIsLogin(!isLogin);
                setError('');
              }}
              style={styles.switchButton}
            >
              <Text style={styles.switchText}>
                {isLogin
                  ? "Don't have an account? Sign Up"
                  : 'Already have an account? Login'}
              </Text>
            </TouchableOpacity>
          )}

          {!waitingVerification && (
            <TouchableOpacity
              onPress={onClose}
              style={styles.closeButton}
            >
              <Text style={styles.closeText}>Cancel</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.secondary,
    borderRadius: 10,
    padding: 20,
    width: '90%',
    maxWidth: 400,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: colors.text.primary,
  },
  input: {
    marginBottom: 15,
  },
  button: {
    marginTop: 10,
  },
  resendButton: {
    marginBottom: 10,
  },
  errorText: {
    color: colors.status.error,
    marginBottom: 10,
    textAlign: 'center',
  },
  infoText: {
    color: colors.primary,
    marginBottom: 10,
    textAlign: 'center',
  },
  switchButton: {
    marginTop: 15,
    alignItems: 'center',
  },
  switchText: {
    color: colors.primary,
  },
  closeButton: {
    marginTop: 15,
    alignItems: 'center',
  },
  closeText: {
    color: colors.text.secondary,
  },
});

export default AuthModal; 