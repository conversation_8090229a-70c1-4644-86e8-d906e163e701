export type RootStackParamList = {
  Login: undefined;
  Main: undefined;
  Admin: undefined;
  Quiz: {
    quizSetId: string;
    isBookmarkQuestion?: boolean;
    bookmarkedQuestion?: QuizQuestion;
    isDailyChallenge?: boolean;
    dailyQuestion?: QuizQuestion;
  };
  QuizComplete: {
    score: number;
    totalQuestions: number;
    quizTitle: string;
    timeSpent: number;
    difficulty?: 'beginner' | 'intermediate' | 'advanced';
  };
  EditProfile: undefined;
  ChangePassword: undefined;
};

export type QuizQuestion = {
  id: string;
  question: string;
  options: string[];
  correctAnswer: string;
  solution: string;
};

export type QuizSet = {
  id: string;
  title: string;
  description: string;
  questions: QuizQuestion[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  type: string; // <PERSON>, <PERSON>, <PERSON>, or custom types
  wordCount?: number;
  icon?: string;
  progress?: number;
};