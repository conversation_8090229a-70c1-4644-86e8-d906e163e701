import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, getAuth, onAuthStateChanged, signInWithEmailAndPassword } from 'firebase/auth';
import { auth } from '../config/firebase';

interface SessionData {
  uid: string;
  email: string;
  displayName: string;
  emailVerified: boolean;
  lastLoginTime: number;
  sessionExpiry: number;
  refreshToken?: string;
}

interface PersistentSession {
  sessionData: SessionData;
  isActive: boolean;
  createdAt: number;
  lastAccessed: number;
}

const SESSION_KEYS = {
  PERSISTENT_SESSION: 'persistent_session',
  REMEMBER_ME: 'remember_me_credentials',
  SESSION_EXPIRY: 30 * 24 * 60 * 60 * 1000, // 30 days
  REMEMBER_ME_EXPIRY: 90 * 24 * 60 * 60 * 1000, // 90 days
};

export const sessionService = {
  // Initialize session management
  async initializeSession(): Promise<User | null> {
    try {
      const persistentSession = await this.getPersistentSession();
      
      if (persistentSession && persistentSession.isActive) {
        const now = Date.now();
        
        // Check if session is still valid
        if (now < persistentSession.sessionData.sessionExpiry) {
          // Update last accessed time
          await this.updateLastAccessed();
          
          // If Firebase auth is not restored yet (e.g. first launch after install),
          // attempt a silent sign-in using stored credentials so that auth.currentUser is populated.
          const auth = getAuth();
          if (!auth.currentUser) {
            try {
              const credRaw = await AsyncStorage.getItem('userCredentials');
              if (credRaw) {
                const { email: storedEmail, password } = JSON.parse(credRaw);
                if (storedEmail && password) {
                  try {
                    await signInWithEmailAndPassword(auth, storedEmail, password);
                    if (auth.currentUser) {
                      await this.createSession(auth.currentUser as User, true);
                    }
                  } catch {}
                }
              }
            } catch (e) {
              console.warn('Silent sign-in failed:', (e as Error).message);
            }
          }
          
          // Return the user data
          return {
            uid: persistentSession.sessionData.uid,
            email: persistentSession.sessionData.email,
            displayName: persistentSession.sessionData.displayName,
            emailVerified: persistentSession.sessionData.emailVerified,
          } as User;
        } else {
          // Session expired, clear it
          await this.clearSession();
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error initializing session:', error);
      return null;
    }
  },

  // Create persistent session
  async createSession(user: User, rememberMe: boolean = false): Promise<void> {
    try {
      const now = Date.now();
      const expiryTime = rememberMe ? SESSION_KEYS.REMEMBER_ME_EXPIRY : SESSION_KEYS.SESSION_EXPIRY;
      
      const sessionData: SessionData = {
        uid: user.uid,
        email: user.email || '',
        displayName: user.displayName || '',
        emailVerified: user.emailVerified,
        lastLoginTime: now,
        sessionExpiry: now + expiryTime,
        refreshToken: user.refreshToken,
      };

      const persistentSession: PersistentSession = {
        sessionData,
        isActive: true,
        createdAt: now,
        lastAccessed: now,
      };

      await AsyncStorage.setItem(
        SESSION_KEYS.PERSISTENT_SESSION,
        JSON.stringify(persistentSession)
      );

      // Store remember me credentials if requested
      if (rememberMe && user.email) {
        const credentials = {
          email: user.email,
          rememberMe: true,
          storedAt: now,
        };
        await AsyncStorage.setItem(
          SESSION_KEYS.REMEMBER_ME,
          JSON.stringify(credentials)
        );
      }
    } catch (error) {
      console.error('Error creating session:', error);
      throw error;
    }
  },

  // Get persistent session
  async getPersistentSession(): Promise<PersistentSession | null> {
    try {
      const sessionData = await AsyncStorage.getItem(SESSION_KEYS.PERSISTENT_SESSION);
      return sessionData ? JSON.parse(sessionData) : null;
    } catch (error) {
      console.error('Error getting persistent session:', error);
      return null;
    }
  },

  // Update last accessed time
  async updateLastAccessed(): Promise<void> {
    try {
      const session = await this.getPersistentSession();
      if (session) {
        session.lastAccessed = Date.now();
        await AsyncStorage.setItem(
          SESSION_KEYS.PERSISTENT_SESSION,
          JSON.stringify(session)
        );
      }
    } catch (error) {
      console.error('Error updating last accessed:', error);
    }
  },

  // Clear session
  async clearSession(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        SESSION_KEYS.PERSISTENT_SESSION,
        SESSION_KEYS.REMEMBER_ME,
      ]);
    } catch (error) {
      console.error('Error clearing session:', error);
    }
  },

  // Check if user is logged in
  async isLoggedIn(): Promise<boolean> {
    try {
      const session = await this.getPersistentSession();
      if (!session || !session.isActive) return false;
      
      const now = Date.now();
      return now < session.sessionData.sessionExpiry;
    } catch (error) {
      console.error('Error checking login status:', error);
      return false;
    }
  },

  // Get current user from session
  async getCurrentUser(): Promise<User | null> {
    try {
      const session = await this.getPersistentSession();
      if (!session || !session.isActive) return null;
      
      const now = Date.now();
      if (now >= session.sessionData.sessionExpiry) {
        await this.clearSession();
        return null;
      }
      
      return {
        uid: session.sessionData.uid,
        email: session.sessionData.email,
        displayName: session.sessionData.displayName,
        emailVerified: session.sessionData.emailVerified,
      } as User;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  },

  // Set up auth state listener with session management
  setupAuthStateListener(callback: (user: User | null) => void) {
    return onAuthStateChanged(auth, async (user) => {
      if (user) {
        // User is authenticated, create/update session
        await this.createSession(user, true); // Always remember for persistent login
        await this.refreshSession(); // Ensure session is fresh
        callback(user);
      } else {
        // Check for existing session before clearing
        const session = await this.getPersistentSession();
        if (session && session.isActive) {
          try {
            // Try to refresh the session
            await this.refreshSession();
            // If successful, don't clear session and return existing user
            const currentUser = auth.currentUser;
            if (currentUser) {
              callback(currentUser);
              return;
            }
          } catch (error) {
            console.error('Error refreshing session:', error);
          }
        }
        // Clear session if refresh failed or no active session
        await this.clearSession();
        callback(null);
      }
    });
  },

  // Refresh session (extend expiry)
  async refreshSession(): Promise<void> {
    try {
      const session = await this.getPersistentSession();
      if (session && session.isActive) {
        const now = Date.now();
        session.sessionData.sessionExpiry = now + SESSION_KEYS.SESSION_EXPIRY;
        session.lastAccessed = now;
        
        await AsyncStorage.setItem(
          SESSION_KEYS.PERSISTENT_SESSION,
          JSON.stringify(session)
        );
      }
    } catch (error) {
      console.error('Error refreshing session:', error);
    }
  },
}; 