/**
 * App Initialization Service for Production Scale
 * 
 * Coordinates initialization of all services in the correct order
 * Handles user authentication state changes
 * Manages service lifecycle
 */

import { getAuth, User } from 'firebase/auth';
import { productionCache } from './productionCacheService';
import { offlineService } from './offlineService';
import { recentActivityService } from './recentActivityService';
import { performanceMonitor } from '../utils/performanceMonitor';

interface InitializationStatus {
  isInitialized: boolean;
  currentUser: User | null;
  services: {
    cache: boolean;
    offline: boolean;
    recentActivity: boolean;
    performance: boolean;
  };
  initializationTime: number;
  errors: string[];
}

class AppInitializationService {
  private initializationStatus: InitializationStatus = {
    isInitialized: false,
    currentUser: null,
    services: {
      cache: false,
      offline: false,
      recentActivity: false,
      performance: false
    },
    initializationTime: 0,
    errors: []
  };

  private initializationPromise: Promise<void> | null = null;
  private authUnsubscribe: (() => void) | null = null;

  /**
   * Initialize all app services
   */
  async initialize(): Promise<InitializationStatus> {
    if (this.initializationPromise) {
      await this.initializationPromise;
      return this.initializationStatus;
    }

    this.initializationPromise = this.performInitialization();
    await this.initializationPromise;
    return this.initializationStatus;
  }

  /**
   * Perform the actual initialization
   */
  private async performInitialization(): Promise<void> {
    const startTime = Date.now();
    console.log('🚀 Starting app initialization...');

    try {
      // Step 1: Initialize core services (no user context needed)
      await this.initializeCoreServices();

      // Step 2: Set up authentication listener
      await this.setupAuthenticationListener();

      // Step 3: Initialize user-specific services if user is logged in
      const auth = getAuth();
      if (auth.currentUser) {
        await this.initializeUserServices(auth.currentUser);
      }

      this.initializationStatus.isInitialized = true;
      this.initializationStatus.initializationTime = Date.now() - startTime;

      console.log(`✅ App initialization completed in ${this.initializationStatus.initializationTime}ms`);
      
      // Log performance summary
      if (__DEV__) {
        performanceMonitor.logSummary();
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown initialization error';
      this.initializationStatus.errors.push(errorMessage);
      console.error('❌ App initialization failed:', error);
      throw error;
    }
  }

  /**
   * Initialize core services that don't require user context
   */
  private async initializeCoreServices(): Promise<void> {
    console.log('🔧 Initializing core services...');

    try {
      // Initialize production cache
      await productionCache.initialize();
      this.initializationStatus.services.cache = true;
      console.log('✅ Production cache initialized');

      // Initialize offline service
      await offlineService.initialize();
      this.initializationStatus.services.offline = true;
      console.log('✅ Offline service initialized');

      // Performance monitoring is always active
      this.initializationStatus.services.performance = true;
      console.log('✅ Performance monitoring active');

    } catch (error) {
      console.error('❌ Core services initialization failed:', error);
      throw error;
    }
  }

  /**
   * Set up authentication state listener
   */
  private async setupAuthenticationListener(): Promise<void> {
    const auth = getAuth();
    
    this.authUnsubscribe = auth.onAuthStateChanged(async (user) => {
      console.log(`🔐 Auth state changed: ${user ? user.email : 'logged out'}`);
      
      this.initializationStatus.currentUser = user;

      if (user) {
        // User logged in - initialize user-specific services
        await this.initializeUserServices(user);
      } else {
        // User logged out - cleanup user-specific services
        await this.cleanupUserServices();
      }
    });
  }

  /**
   * Initialize services that require user context
   */
  private async initializeUserServices(user: User): Promise<void> {
    console.log(`👤 Initializing user services for: ${user.email}`);

    try {
      // Initialize recent activity service with user context
      await recentActivityService.initialize(user.uid);
      this.initializationStatus.services.recentActivity = true;
      console.log('✅ Recent activity service initialized');

      // Initialize production cache with user context
      await productionCache.initialize(user.uid);
      console.log('✅ Production cache initialized with user context');

    } catch (error) {
      console.error('❌ User services initialization failed:', error);
      this.initializationStatus.errors.push(`User services: ${error}`);
    }
  }

  /**
   * Cleanup user-specific services on logout
   */
  private async cleanupUserServices(): Promise<void> {
    console.log('🧹 Cleaning up user services...');

    try {
      // Clear recent activity data
      await recentActivityService.clearActivityData();
      this.initializationStatus.services.recentActivity = false;

      // Clear offline data
      await offlineService.clearOfflineData();

      // Clear user-specific cache
      await productionCache.clearAll();

      console.log('✅ User services cleaned up');

    } catch (error) {
      console.error('❌ User services cleanup failed:', error);
    }
  }

  /**
   * Get current initialization status
   */
  getInitializationStatus(): InitializationStatus {
    return { ...this.initializationStatus };
  }

  /**
   * Check if app is fully initialized
   */
  isInitialized(): boolean {
    return this.initializationStatus.isInitialized;
  }

  /**
   * Check if user services are initialized
   */
  isUserInitialized(): boolean {
    return this.initializationStatus.currentUser !== null &&
           this.initializationStatus.services.recentActivity;
  }

  /**
   * Force re-initialization (for debugging)
   */
  async forceReinitialize(): Promise<InitializationStatus> {
    console.log('🔄 Force re-initializing app...');
    
    // Cleanup existing state
    if (this.authUnsubscribe) {
      this.authUnsubscribe();
      this.authUnsubscribe = null;
    }

    // Reset status
    this.initializationStatus = {
      isInitialized: false,
      currentUser: null,
      services: {
        cache: false,
        offline: false,
        recentActivity: false,
        performance: false
      },
      initializationTime: 0,
      errors: []
    };

    this.initializationPromise = null;
    
    return await this.initialize();
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    return {
      initializationTime: this.initializationStatus.initializationTime,
      cacheStats: productionCache.getStats(),
      performanceStats: performanceMonitor.getSummary(),
      offlineStatus: offlineService.getSyncStatus(),
      recentActivityStats: recentActivityService.getActivityStats()
    };
  }

  /**
   * Health check for all services
   */
  async healthCheck(): Promise<{ healthy: boolean; issues: string[] }> {
    const issues: string[] = [];

    // Check if initialization completed
    if (!this.initializationStatus.isInitialized) {
      issues.push('App not fully initialized');
    }

    // Check service states
    Object.entries(this.initializationStatus.services).forEach(([service, status]) => {
      if (!status) {
        issues.push(`${service} service not initialized`);
      }
    });

    // Check performance
    if (!performanceMonitor.isPerformanceOptimal()) {
      issues.push('Performance not optimal');
    }

    // Check cache health
    const cacheStats = productionCache.getStats();
    if (cacheStats.hitRate < 50) {
      issues.push('Low cache hit rate');
    }

    return {
      healthy: issues.length === 0,
      issues
    };
  }

  /**
   * Cleanup on app termination
   */
  async cleanup(): Promise<void> {
    console.log('🛑 Cleaning up app initialization service...');

    if (this.authUnsubscribe) {
      this.authUnsubscribe();
      this.authUnsubscribe = null;
    }

    await this.cleanupUserServices();
    
    console.log('✅ App initialization service cleaned up');
  }
}

export const appInitializationService = new AppInitializationService();
