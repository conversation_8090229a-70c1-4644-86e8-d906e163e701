import * as functions from "firebase-functions";
import * as admin from "firebase-admin";

admin.initializeApp();
const db = admin.firestore();

// Rate limiting settings
const RATE_LIMIT_SECONDS = 30; // Only one quiz submission every 30 seconds per user

/**
 * A callable function to handle quiz result submission with rate limiting.
 */
export const submitQuizResult = functions.https.onCall(async (data, context) => {
  const userId = context.auth?.uid;

  if (!userId) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "You must be logged in to submit a quiz.",
    );
  }

  const { score, totalQuestions, difficulty, quizSetId } = data;

  // Validate input data
  if (!Number.isInteger(score) || !Number.isInteger(totalQuestions) || !difficulty || !quizSetId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "The function must be called with score, totalQuestions, difficulty, and quizSetId.",
    );
  }

  const userRef = db.doc(`users/${userId}`);
  const now = admin.firestore.Timestamp.now();

  try {
    await db.runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);

      if (!userDoc.exists) {
        throw new functions.https.HttpsError("not-found", "User document does not exist.");
      }

      const lastSubmission = userDoc.data()?.lastQuizSubmission as admin.firestore.Timestamp | undefined;

      if (lastSubmission && now.seconds - lastSubmission.seconds < RATE_LIMIT_SECONDS) {
        throw new functions.https.HttpsError(
          "resource-exhausted",
          `You can only submit one quiz every ${RATE_LIMIT_SECONDS} seconds.`,
        );
      }

      // If checks pass, update the timestamp and proceed with the logic.
      // In a real scenario, you would move the logic from `userService.updateQuizStats` here.
      // For now, we just update the rate-limiting timestamp.
      transaction.update(userRef, {
        lastQuizSubmission: now,
        // ... you would also update stats here
      });

      console.log(`User ${userId} successfully submitted quiz ${quizSetId}. Rate limit updated.`);
    });

    return { success: true, message: "Quiz result submitted successfully." };
  } catch (error) {
    console.error("Error in submitQuizResult transaction:", error);
    // Re-throw HttpsError or convert other errors
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    throw new functions.https.HttpsError("internal", "An internal error occurred.");
  }
}); 