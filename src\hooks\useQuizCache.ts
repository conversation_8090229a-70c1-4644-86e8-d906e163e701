import { useState, useEffect } from 'react';
import { getAuth } from 'firebase/auth';
import { cacheData, getCachedData, CACHE_KEYS } from '../utils/cache';
import { getFirestore, collection, getDocs, query, where } from 'firebase/firestore';

interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: string;
  explanation?: string;
}

interface QuizSet {
  id: string;
  title: string;
  questions: QuizQuestion[];
}

export const useQuizCache = (quizSetId: string) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quizData, setQuizData] = useState<QuizSet | null>(null);
  const auth = getAuth();
  const db = getFirestore();

  const fetchAndCacheQuizData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to get cached data first
      const cachedQuiz = await getCachedData(CACHE_KEYS.QUIZ_QUESTIONS + quizSetId);
      if (cachedQuiz) {
        setQuizData(cachedQuiz);
        setLoading(false);
        return;
      }

      // If no cache, fetch from Firestore
      const quizRef = collection(db, 'quizSets');
      const q = query(quizRef, where('id', '==', quizSetId));
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        throw new Error('Quiz not found');
      }

      const quizDoc = querySnapshot.docs[0];
      const quiz = { id: quizDoc.id, ...quizDoc.data() } as QuizSet;

      // Cache the fetched data
      await cacheData(CACHE_KEYS.QUIZ_QUESTIONS + quizSetId, quiz);
      
      setQuizData(quiz);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load quiz');
      console.error('Error fetching quiz:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAndCacheQuizData();
  }, [quizSetId]);

  const refreshQuizData = () => {
    fetchAndCacheQuizData();
  };

  return {
    loading,
    error,
    quizData,
    refreshQuizData,
  };
}; 