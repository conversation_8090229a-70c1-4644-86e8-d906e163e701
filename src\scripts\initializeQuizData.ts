import { collection, addDoc } from 'firebase/firestore';
import { db } from '../config/firebase';
import { quizSets } from '../data/quizData';

// Available quiz types
const QUIZ_TYPES = ['<PERSON>', 'Romeo', 'Juliet'] as const;
type QuizType = typeof QUIZ_TYPES[number];

export const initializeQuizData = async (selectedType?: QuizType) => {
  try {
    const quizSetCollection = collection(db, 'quizSets');

    // If no type specified, prompt user to choose
    let typeToAssign: QuizType;
    if (selectedType && QUIZ_TYPES.includes(selectedType)) {
      typeToAssign = selectedType;
    } else {
      // Default to <PERSON> if no valid type provided
      typeToAssign = 'Hunter';
      console.log('⚠️ No valid type specified. Using default type: Hunter');
      console.log('💡 Available types: <PERSON>, <PERSON>, <PERSON>');
    }

    console.log(`🚀 Initializing quiz data with type: ${typeToAssign}`);
    console.log(`📊 Processing ${quizSets.length} quiz sets...`);

    // Add each quiz set to Firestore with the selected type
    for (let i = 0; i < quizSets.length; i++) {
      const quizSet = quizSets[i];
      const enrichedQuizSet = {
        ...quizSet,
        type: typeToAssign, // 🎯 Assign the selected type
        description: `${quizSet.questions.length} words • ${quizSet.difficulty || 'intermediate'}`,
        wordCount: quizSet.questions.length,
        difficulty: quizSet.difficulty || 'intermediate',
        icon: quizSet.icon || 'book',
        category: quizSet.category || 'General',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      await addDoc(quizSetCollection, enrichedQuizSet);
      console.log(`✅ Added: ${quizSet.title} (${i + 1}/${quizSets.length})`);
    }

    console.log(`🎉 Successfully initialized ${quizSets.length} quiz sets with type: ${typeToAssign}`);
    return { success: true, count: quizSets.length, type: typeToAssign };
  } catch (error) {
    console.error('❌ Error initializing quiz data:', error);
    throw error;
  }
};

// Helper function to initialize with specific type
export const initializeQuizDataWithType = async (type: QuizType) => {
  if (!QUIZ_TYPES.includes(type)) {
    throw new Error(`Invalid quiz type: ${type}. Available types: ${QUIZ_TYPES.join(', ')}`);
  }
  return await initializeQuizData(type);
};

// Helper functions for each type
export const initializeHunterQuizzes = () => initializeQuizDataWithType('Hunter');
export const initializeRomeoQuizzes = () => initializeQuizDataWithType('Romeo');
export const initializeJulietQuizzes = () => initializeQuizDataWithType('Juliet');