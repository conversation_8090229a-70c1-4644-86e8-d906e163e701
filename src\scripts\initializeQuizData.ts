import { collection, addDoc } from 'firebase/firestore';
import { db } from '../config/firebase';
import { quizSets } from '../data/quizData';

export const initializeQuizData = async () => {
  try {
    const quizSetCollection = collection(db, 'quizSets');
    
    // Add each quiz set to Firestore
    for (const quizSet of quizSets) {
      const enrichedQuizSet = {
        ...quizSet,
        description: `${quizSet.questions.length} words • ${quizSet.level || 'Intermediate'}`,
        wordCount: quizSet.questions.length,
        level: quizSet.level || 'Intermediate',
        icon: quizSet.icon || 'book',
        category: quizSet.category || 'General',
      };
      
      await addDoc(quizSetCollection, enrichedQuizSet);
    }
    
    console.log('Quiz data initialized successfully!');
  } catch (error) {
    console.error('Error initializing quiz data:', error);
    throw error;
  }
}; 