{"logs": [{"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-v22_values-v22.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-v22\\values-v22.xml", "from": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,487", "endLines": "2,3,8,13", "endColumns": "74,86,12,12", "endOffsets": "125,212,482,764"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ms_values-ms.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,3807", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,3883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,379", "endColumns": "104,99,118,100", "endOffsets": "155,255,374,475"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2800,3122,3222,3341", "endColumns": "104,99,118,100", "endOffsets": "2900,3217,3336,3437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,216,272,316,383,446,518,571", "endColumns": "113,46,55,43,66,62,71,52,65", "endOffsets": "164,211,267,311,378,441,513,566,632"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2905,3019,3066,3442,3486,3553,3616,3688,3741", "endColumns": "113,46,55,43,66,62,71,52,65", "endOffsets": "3014,3061,3117,3481,3548,3611,3683,3736,3802"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3888", "endColumns": "100", "endOffsets": "3984"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-hi_values-hi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3879", "endColumns": "100", "endOffsets": "3975"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,3797", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,3874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,375", "endColumns": "105,101,111,101", "endOffsets": "156,258,370,472"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2787,3100,3202,3314", "endColumns": "105,101,111,101", "endOffsets": "2888,3197,3309,3411"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,208,262,307,373,440,524,578", "endColumns": "105,46,53,44,65,66,83,53,64", "endOffsets": "156,203,257,302,368,435,519,573,638"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2893,2999,3046,3416,3461,3527,3594,3678,3732", "endColumns": "105,46,53,44,65,66,83,53,64", "endOffsets": "2994,3041,3095,3456,3522,3589,3673,3727,3792"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-v26_values-v26.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-v26\\values-v26.xml", "from": {"startLines": "2,3,4,8,12,16", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,130,217,381,557,796", "endLines": "2,3,7,11,15,16", "endColumns": "74,86,12,12,12,92", "endOffsets": "125,212,376,552,791,884"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-hdpi-v4_values-hdpi-v4.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-hdpi-v4\\values-hdpi-v4.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "6", "endColumns": "13", "endOffsets": "327"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-kn_values-kn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3372", "endColumns": "100", "endOffsets": "3468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,3289", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,3367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,269,385", "endColumns": "113,99,115,99", "endOffsets": "164,264,380,480"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2859,2973,3073,3189", "endColumns": "113,99,115,99", "endOffsets": "2968,3068,3184,3284"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-zh-rCN_values-zh-rCN.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,3518", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,3592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,177,229,271,328,383,435,485", "endColumns": "74,46,51,41,56,54,51,49,57", "endOffsets": "125,172,224,266,323,378,430,480,538"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2745,2820,2867,3204,3246,3303,3358,3410,3460", "endColumns": "74,46,51,41,56,54,51,49,57", "endOffsets": "2815,2862,2914,3241,3298,3353,3405,3455,3513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,91", "endOffsets": "133,225,326,418"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2662,2919,3011,3112", "endColumns": "82,91,100,91", "endOffsets": "2740,3006,3107,3199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3597", "endColumns": "100", "endOffsets": "3693"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-tl_values-tl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3353", "endColumns": "100", "endOffsets": "3449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,263,374", "endColumns": "102,104,110,103", "endOffsets": "153,258,369,473"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2845,2948,3053,3164", "endColumns": "102,104,110,103", "endOffsets": "2943,3048,3159,3263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,3268", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,3348"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values_values.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "26,32,34,87,92,93,95,96,97,98,99,100,101,104,105,106,107,108,109,110,111,112,113,118,119,171,172,173,174,175,176,177,178,179,180,182,183,184,185,186,187,188,189,190,191,192,193,205,206,207,208,209,210,211,212,213,214,215,216,217,218,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,369,370,374,375,376,377,378,379,380,572,573,574,575,576,577,578,579,625,626,627,628,633,644,645,654,682,691,692,695,696,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,867,882,883,887,888,889,913,921,922,926,930,941,946,974,981,985,989,994,998,1002,1006,1010,1014,1018,1024,1028,1034,1038,1044,1048,1053,1057,1060,1064,1070,1074,1080,1084,1090,1093,1097,1101,1105,1109,1113,1114,1115,1116,1119,1122,1125,1128,1132,1133,1134,1135,1173,1176,1178,1180,1182,1187,1188,1192,1198,1202,1203,1205,1217,1218,1222,1228,1232,1287,1288,1292,1319,1323,1324,1328,1878,2050,2076,2247,2273,2304,2312,2318,2334,2356,2361,2366,2376,2385,2394,2398,2405,2424,2431,2432,2441,2444,2447,2451,2455,2459,2462,2463,2468,2473,2483,2488,2495,2501,2502,2505,2509,2514,2516,2518,2521,2524,2526,2530,2533,2540,2543,2546,2550,2552,2556,2558,2560,2562,2566,2574,2582,2594,2600,2609,2612,2623,2626,2627,2632,2633,2864,2933,3007,3008,3018,3027,3033,3035,3039,3042,3045,3048,3051,3054,3057,3060,3064,3067,3070,3073,3077,3080,3084,3210,3211,3212,3213,3214,3215,3216,3217,3218,3219,3220,3221,3222,3223,3224,3225,3226,3227,3228,3229,3230,3232,3234,3235,3236,3237,3238,3239,3240,3241,3243,3244,3246,3247,3249,3251,3252,3254,3255,3256,3257,3258,3259,3261,3262,3263,3264,3265,3399,3401,3403,3409,3410,3411,3412,3413,3414,3415,3416,3417,3418,3419,3420,3421,3423,3424,3425,3426,3427,3428,3429,3431,3435,3636,3637,3638,3639,3640,3641,3645,3646,3647,3801,3803,3805,3807,3809,3811,3812,3813,3814,3816,3818,3820,3821,3822,3823,3824,3825,3826,3827,3828,3829,3830,3831,3834,3835,3836,3837,3839,3841,3842,3844,3845,3847,3849,3851,3852,3853,3854,3855,3856,3857,3858,3859,3860,3861,3862,3864,3865,3866,3867,3869,3870,3871,3872,3873,3875,3877,3879,3881,3882,3883,3884,3885,3886,3887,3888,3889,3890,3891,3892,3893,3894,3895,4647,4722,4725,4728,4731,4745,4751,4856,4859,4888,4915,4924,4988,5500,5538,5798,5975,6313,6337,6343,6497,6518,6642,6699,6705,6712,6746,6985,7017,7171,7417,7481,7493,7522", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1408,1776,1874,5043,5303,5358,5482,5546,5616,5677,5752,5828,5905,6143,6228,6310,6386,6462,6539,6617,6723,6829,6908,7237,7294,10742,10816,10891,10956,11022,11082,11143,11215,11288,11355,11472,11531,11590,11649,11708,11767,11821,11875,11928,11982,12036,12090,12930,13004,13083,13156,13230,13301,13373,13445,13518,13575,13633,13706,13780,13854,13987,14059,14132,14202,14273,14333,14394,14463,14532,14602,14676,14752,14816,14893,14969,15046,15111,15180,15257,15332,15401,15469,15546,15612,15673,15770,15835,15904,16003,16074,16133,16191,16248,16307,16371,16442,16514,16586,16658,16730,16797,16865,16933,16992,17055,17119,17209,17300,17360,17426,17493,17559,17629,17693,17746,17813,17874,17941,18054,18112,18175,18240,18305,18380,18453,18525,18569,18616,18662,18711,18772,18833,18894,18956,19020,19084,19148,19213,19276,19336,19397,19463,19522,19582,19644,19715,19775,23834,23920,24170,24260,24347,24435,24517,24600,24690,37203,37255,37313,37358,37424,37488,37545,37602,40293,40350,40398,40447,40700,41229,41276,41708,43175,43628,43692,43882,43942,45394,45468,45538,45616,45670,45740,45825,45873,45919,45980,46043,46109,46173,46244,46307,46372,46436,46497,46558,46610,46683,46757,46826,46901,46975,47049,47190,62657,63823,63901,64125,64213,64309,65874,66456,66545,66792,67073,67739,68024,69772,70249,70471,70693,70969,71196,71426,71656,71886,72116,72343,72762,72988,73413,73643,74071,74290,74573,74781,74912,75139,75565,75790,76217,76438,76863,76983,77259,77560,77884,78175,78489,78626,78757,78862,79104,79271,79475,79683,79954,80066,80178,80283,82228,82442,82588,82728,82814,83162,83250,83496,83914,84163,84245,84343,85000,85100,85352,85776,86031,90062,90151,90388,92412,92654,92756,93009,130427,141108,142624,153319,154847,156604,157230,157650,158911,160176,160432,160668,161215,161709,162314,162512,163092,164460,164835,164953,165491,165648,165844,166117,166373,166543,166684,166748,167113,167480,168156,168420,168758,169111,169205,169391,169697,169959,170084,170211,170450,170661,170780,170973,171150,171605,171786,171908,172167,172280,172467,172569,172676,172805,173080,173588,174084,174961,175255,175825,175974,176706,176878,176962,177298,177390,191144,196375,202090,202152,202730,203314,203650,203763,203992,204152,204304,204475,204641,204810,204977,205140,205383,205553,205726,205897,206171,206370,206575,213834,213918,214014,214110,214208,214308,214410,214512,214614,214716,214818,214918,215014,215126,215255,215378,215509,215640,215738,215852,215946,216086,216220,216316,216428,216528,216644,216740,216852,216952,217092,217228,217392,217522,217680,217830,217971,218115,218250,218362,218512,218640,218768,218904,219036,219166,219296,219408,227454,227600,227744,228051,228117,228207,228283,228387,228477,228579,228687,228795,228895,228975,229067,229165,229275,229327,229405,229511,229603,229707,229817,229939,230102,244034,244114,244214,244304,244414,244504,244745,244839,244945,255505,255605,255717,255831,255947,256063,256157,256271,256383,256485,256605,256727,256809,256913,257033,257159,257257,257351,257439,257551,257667,257789,257901,258076,258192,258278,258370,258482,258606,258673,258799,258867,258995,259139,259267,259336,259431,259546,259659,259758,259867,259978,260089,260190,260295,260395,260525,260616,260739,260833,260945,261031,261135,261231,261319,261437,261541,261645,261771,261859,261967,262067,262157,262267,262351,262453,262537,262591,262655,262761,262847,262957,263041,308267,310883,311001,311116,311196,311557,311790,315905,315983,317327,318688,319076,321919,337439,338701,347402,356579,367051,367802,368064,373267,373646,377924,379690,379919,380181,381519,387695,388545,394235,403084,405549,405889,407310", "endLines": "26,32,34,87,92,93,95,96,97,98,99,100,101,104,105,106,107,108,109,110,111,112,113,118,119,171,172,173,174,175,176,177,178,179,180,182,183,184,185,186,187,188,189,190,191,192,193,205,206,207,208,209,210,211,212,213,214,215,216,217,218,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,369,370,374,375,376,377,378,379,380,572,573,574,575,576,577,578,579,625,626,627,628,633,644,645,654,682,691,692,695,696,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,867,882,883,887,888,889,920,921,925,929,933,945,951,980,984,988,993,997,1001,1005,1009,1013,1017,1023,1027,1033,1037,1043,1047,1052,1056,1059,1063,1069,1073,1079,1083,1089,1092,1096,1100,1104,1108,1112,1113,1114,1115,1118,1121,1124,1127,1131,1132,1133,1134,1135,1175,1177,1179,1181,1186,1187,1191,1197,1201,1202,1204,1216,1217,1221,1227,1231,1232,1287,1291,1318,1322,1323,1327,1355,2049,2075,2246,2272,2303,2311,2317,2333,2355,2360,2365,2375,2384,2393,2397,2404,2423,2430,2431,2440,2443,2446,2450,2454,2458,2461,2462,2467,2472,2482,2487,2494,2500,2501,2504,2508,2513,2515,2517,2520,2523,2525,2529,2532,2539,2542,2545,2549,2551,2555,2557,2559,2561,2565,2573,2581,2593,2599,2608,2611,2622,2625,2626,2631,2632,2637,2932,3002,3007,3017,3026,3027,3034,3038,3041,3044,3047,3050,3053,3056,3059,3063,3066,3069,3072,3076,3079,3083,3087,3210,3211,3212,3213,3214,3215,3216,3217,3218,3219,3220,3221,3222,3223,3224,3225,3226,3227,3228,3229,3231,3233,3234,3235,3236,3237,3238,3239,3240,3242,3243,3245,3246,3248,3250,3251,3253,3254,3255,3256,3257,3258,3260,3261,3262,3263,3264,3265,3400,3402,3404,3409,3410,3411,3412,3413,3414,3415,3416,3417,3418,3419,3420,3422,3423,3424,3425,3426,3427,3428,3430,3434,3438,3636,3637,3638,3639,3640,3644,3645,3646,3647,3802,3804,3806,3808,3810,3811,3812,3813,3815,3817,3819,3820,3821,3822,3823,3824,3825,3826,3827,3828,3829,3830,3833,3834,3835,3836,3838,3840,3841,3843,3844,3846,3848,3850,3851,3852,3853,3854,3855,3856,3857,3858,3859,3860,3861,3863,3864,3865,3866,3868,3869,3870,3871,3872,3874,3876,3878,3880,3881,3882,3883,3884,3885,3886,3887,3888,3889,3890,3891,3892,3893,3894,3895,4721,4724,4727,4730,4744,4750,4760,4858,4887,4914,4923,4987,5350,5503,5565,5825,5992,6336,6342,6348,6517,6641,6661,6704,6708,6717,6780,6996,7082,7190,7471,7492,7518,7528", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1458,1816,1918,5079,5353,5415,5541,5611,5672,5747,5823,5900,5978,6223,6305,6381,6457,6534,6612,6718,6824,6903,6983,7289,7347,10811,10886,10951,11017,11077,11138,11210,11283,11350,11418,11526,11585,11644,11703,11762,11816,11870,11923,11977,12031,12085,12139,12999,13078,13151,13225,13296,13368,13440,13513,13570,13628,13701,13775,13849,13924,14054,14127,14197,14268,14328,14389,14458,14527,14597,14671,14747,14811,14888,14964,15041,15106,15175,15252,15327,15396,15464,15541,15607,15668,15765,15830,15899,15998,16069,16128,16186,16243,16302,16366,16437,16509,16581,16653,16725,16792,16860,16928,16987,17050,17114,17204,17295,17355,17421,17488,17554,17624,17688,17741,17808,17869,17936,18049,18107,18170,18235,18300,18375,18448,18520,18564,18611,18657,18706,18767,18828,18889,18951,19015,19079,19143,19208,19271,19331,19392,19458,19517,19577,19639,19710,19770,19838,23915,24002,24255,24342,24430,24512,24595,24685,24776,37250,37308,37353,37419,37483,37540,37597,37651,40345,40393,40442,40493,40729,41271,41320,41749,43202,43687,43749,43937,43994,45463,45533,45611,45665,45735,45820,45868,45914,45975,46038,46104,46168,46239,46302,46367,46431,46492,46553,46605,46678,46752,46821,46896,46970,47044,47185,47255,62705,63896,63986,64208,64304,64394,66451,66540,66787,67068,67320,68019,68412,70244,70466,70688,70964,71191,71421,71651,71881,72111,72338,72757,72983,73408,73638,74066,74285,74568,74776,74907,75134,75560,75785,76212,76433,76858,76978,77254,77555,77879,78170,78484,78621,78752,78857,79099,79266,79470,79678,79949,80061,80173,80278,80395,82437,82583,82723,82809,83157,83245,83491,83909,84158,84240,84338,84995,85095,85347,85771,86026,86120,90146,90383,92407,92649,92751,93004,95160,141103,142619,153314,154842,156599,157225,157645,158906,160171,160427,160663,161210,161704,162309,162507,163087,164455,164830,164948,165486,165643,165839,166112,166368,166538,166679,166743,167108,167475,168151,168415,168753,169106,169200,169386,169692,169954,170079,170206,170445,170656,170775,170968,171145,171600,171781,171903,172162,172275,172462,172564,172671,172800,173075,173583,174079,174956,175250,175820,175969,176701,176873,176957,177293,177385,177663,196370,201741,202147,202725,203309,203400,203758,203987,204147,204299,204470,204636,204805,204972,205135,205378,205548,205721,205892,206166,206365,206570,206900,213913,214009,214105,214203,214303,214405,214507,214609,214711,214813,214913,215009,215121,215250,215373,215504,215635,215733,215847,215941,216081,216215,216311,216423,216523,216639,216735,216847,216947,217087,217223,217387,217517,217675,217825,217966,218110,218245,218357,218507,218635,218763,218899,219031,219161,219291,219403,219543,227595,227739,227877,228112,228202,228278,228382,228472,228574,228682,228790,228890,228970,229062,229160,229270,229322,229400,229506,229598,229702,229812,229934,230097,230254,244109,244209,244299,244409,244499,244740,244834,244940,245032,255600,255712,255826,255942,256058,256152,256266,256378,256480,256600,256722,256804,256908,257028,257154,257252,257346,257434,257546,257662,257784,257896,258071,258187,258273,258365,258477,258601,258668,258794,258862,258990,259134,259262,259331,259426,259541,259654,259753,259862,259973,260084,260185,260290,260390,260520,260611,260734,260828,260940,261026,261130,261226,261314,261432,261536,261640,261766,261854,261962,262062,262152,262262,262346,262448,262532,262586,262650,262756,262842,262952,263036,263156,310878,310996,311111,311191,311552,311785,312302,315978,317322,318683,319071,321914,331967,337569,340066,348754,357146,367797,368059,368259,373641,377919,378525,379914,380065,380391,382597,388002,391566,394974,405210,405884,407195,407508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "114,115,116,117,310,311,786,800,801,802", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "6988,7046,7112,7175,20023,20094,51752,53094,53161,53240", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "7041,7107,7170,7232,20089,20161,51815,53156,53235,53304"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\74f887f2d992a24d7a2a9bb57befd934\\transformed\\lifecycle-runtime-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "686", "startColumns": "4", "startOffsets": "43357", "endColumns": "42", "endOffsets": "43395"}}, {"source": "D:\\CadetVocab - Copy\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,2,3", "startColumns": "2,2,2", "startOffsets": "14,60,146", "endColumns": "45,85,94", "endOffsets": "57,143,238"}, "to": {"startLines": "747,795,796", "startColumns": "4,4,4", "startOffsets": "47416,52506,52594", "endColumns": "47,87,96", "endOffsets": "47459,52589,52686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4dfec2d3282dc05e74257e73dac5bcbd\\transformed\\jetified-activity-1.7.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "647,687", "startColumns": "4,4", "startOffsets": "41368,43400", "endColumns": "41,59", "endOffsets": "41405,43455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfb57a730389e05991612d1bc8a291e2\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "4761,4777,4783,6997,7013", "startColumns": "4,4,4,4,4", "startOffsets": "312307,312732,312910,388007,388418", "endLines": "4776,4782,4792,7012,7016", "endColumns": "24,24,24,24,24", "endOffsets": "312727,312905,313189,388413,388540"}}, {"source": "D:\\CadetVocab - Copy\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "1,8,13", "startColumns": "2,2,2", "startOffsets": "61,476,719", "endLines": "7,12,15", "endColumns": "10,10,10", "endOffsets": "473,716,857"}, "to": {"startLines": "906,3028,3406", "startColumns": "4,4,4", "startOffsets": "65457,203405,227908", "endLines": "912,3032,3408", "endColumns": "10,10,10", "endOffsets": "65869,203645,228046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,89,90,94,95,96,97,103,113,146,167,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,344,407,477,545,617,687,748,822,895,956,1017,1079,1143,1205,1266,1334,1434,1494,1560,1633,1702,1759,1811,1873,1945,2021,2086,2145,2204,2264,2324,2384,2444,2504,2564,2624,2684,2744,2804,2863,2923,2983,3043,3103,3163,3223,3283,3343,3403,3463,3522,3582,3642,3701,3760,3819,3878,3937,3996,4031,4066,4121,4184,4239,4297,4355,4416,4479,4536,4587,4637,4698,4755,4821,4855,4890,4925,4995,5066,5183,5384,5494,5695,5824,5896,5963,6166,6467,8198,8879,9561", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,88,89,93,94,95,96,102,112,145,166,199,205", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,339,402,472,540,612,682,743,817,890,951,1012,1074,1138,1200,1261,1329,1429,1489,1555,1628,1697,1754,1806,1868,1940,2016,2081,2140,2199,2259,2319,2379,2439,2499,2559,2619,2679,2739,2799,2858,2918,2978,3038,3098,3158,3218,3278,3338,3398,3458,3517,3577,3637,3696,3755,3814,3873,3932,3991,4026,4061,4116,4179,4234,4292,4350,4411,4474,4531,4582,4632,4693,4750,4816,4850,4885,4920,4990,5061,5178,5379,5489,5690,5819,5891,5958,6161,6462,8193,8874,9556,9723"}, "to": {"startLines": "56,102,103,202,203,315,316,317,318,319,320,321,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,580,581,582,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,636,637,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,676,713,877,3266,3267,3272,3275,3280,3930,3931,5504,5788,6065,6098,6268,6301", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3239,5983,6055,12709,12778,20336,20406,20474,20546,20616,20677,20751,35948,36009,36070,36132,36196,36258,36319,36387,36487,36547,36613,36686,36755,36812,36864,37656,37728,37804,38033,38092,38151,38211,38271,38331,38391,38451,38511,38571,38631,38691,38751,38810,38870,38930,38990,39050,39110,39170,39230,39290,39350,39410,39469,39529,39589,39648,39707,39766,39825,39884,40841,40876,41754,41809,41872,41927,41985,42043,42104,42167,42224,42275,42325,42386,42443,42509,42543,42874,45004,63446,219548,219665,219932,220225,220492,265047,265119,337574,347101,359128,360859,365935,366617", "endLines": "56,102,103,202,203,315,316,317,318,319,320,321,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,580,581,582,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,636,637,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,676,713,877,3266,3270,3272,3278,3280,3930,3931,5509,5797,6097,6118,6300,6306", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "3294,6050,6138,12773,12836,20401,20469,20541,20611,20672,20746,20819,36004,36065,36127,36191,36253,36314,36382,36482,36542,36608,36681,36750,36807,36859,36921,37723,37799,37864,38087,38146,38206,38266,38326,38386,38446,38506,38566,38626,38686,38746,38805,38865,38925,38985,39045,39105,39165,39225,39285,39345,39405,39464,39524,39584,39643,39702,39761,39820,39879,39938,40871,40906,41804,41867,41922,41980,42038,42099,42162,42219,42270,42320,42381,42438,42504,42538,42573,42904,45069,63512,219660,219861,220037,220421,220616,265114,265181,337772,347397,360854,361535,366612,366779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5aa25914fa2b78ed667de0ddd139f3f\\transformed\\jetified-autofill-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,19,20,27,32,37,44,53", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,934,994,1376,1656,1938,2322,2820", "endLines": "2,18,19,26,31,36,43,52,66", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "118,929,989,1371,1651,1933,2317,2815,3867"}, "to": {"startLines": "309,3448,3896,3897,3904,3909,3914,3921,5351", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "19955,230788,263161,263221,263603,263883,264165,264549,331972", "endLines": "309,3463,3896,3903,3908,3913,3920,3929,5364", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "20018,231594,263216,263598,263878,264160,264544,265042,332553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ff927c381813b5e2b9b314f7d6c57346\\transformed\\jetified-viewpager2-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "24", "endOffsets": "160"}, "to": {"startLines": "7519", "startColumns": "4", "startOffsets": "407200", "endLines": "7521", "endColumns": "24", "endOffsets": "407305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e46f44a0276ba8aafbcfba1cd5c69733\\transformed\\transition-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "630,631,642,649,650,677,678,679,680,681", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "40555,40595,41142,41453,41508,42909,42963,43015,43064,43125", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "40590,40637,41180,41503,41550,42958,43010,43059,43120,43170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\49389fe05353e9e81c6c6ad561e8629c\\transformed\\lifecycle-viewmodel-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "689", "startColumns": "4", "startOffsets": "43514", "endColumns": "49", "endOffsets": "43559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,214,268,312,382,448,519,573,626,693,748", "endLines": "2,3,4,5,6,7,8,9,10,11,12,71", "endColumns": "111,46,53,43,69,65,70,53,52,66,54,24", "endOffsets": "162,209,263,307,377,443,514,568,621,688,743,3836"}, "to": {"startLines": "787,788,789,805,806,807,808,809,860,861,862,5916", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "51820,51932,51979,53538,53582,53652,53718,53789,62158,62211,62278,353486", "endLines": "787,788,789,805,806,807,808,809,860,861,862,5974", "endColumns": "111,46,53,43,69,65,70,53,52,66,54,24", "endOffsets": "51927,51974,52028,53577,53647,53713,53784,53838,62206,62273,62328,356574"}}, {"source": "D:\\CadetVocab - Copy\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "3,4,2,1", "startColumns": "2,2,2,2", "startOffsets": "117,162,70,14", "endColumns": "44,48,46,55", "endOffsets": "159,208,114,67"}, "to": {"startLines": "126,127,181,219", "startColumns": "4,4,4,4", "startOffsets": "7727,7774,11423,13929", "endColumns": "46,50,48,57", "endOffsets": "7769,7820,11467,13982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fc45c20841ea83600d34b10ff3d24920\\transformed\\recyclerview-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,38", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,2084"}, "to": {"startLines": "58,371,372,373,381,382,383,634,6718", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3357,24007,24066,24114,24781,24856,24932,40734,380396", "endLines": "58,371,372,373,381,382,383,634,6738", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "3408,24061,24109,24165,24851,24927,24999,40795,381231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4115d94f0331b651ff9a931638b09a6b\\transformed\\jetified-react-android-0.72.6-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,80,84,88,91,95,99,102,105,106,107,116,123,130,133,136,139,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,163,226,277,331,390,438,487,536,584,633,691,740,781,825,868,922,1103,1176,1299,1395,1484,1592,1709,1829,1949,2051,2154,2265,2372,2475,2586,2755,2923,3040,3150,3265,3378,3534,3642,3755,3846,3957,4126,4224,4351,4476,4571,4678,4758,4834,4907,4994,5065,5136,5214,5294,5380,5464,5536,5618,5702,5779,5866,5951,6030,6105,6178,6267,6344,6422,6495,6573,6821,7069,7337,7522,7724,7930,8131,8320,8346,8381,8919,9337,9715,9892,10071,10254,10619", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,79,83,87,90,94,98,101,104,105,106,115,122,129,132,135,138,144,154", "endColumns": "62,62,50,53,58,47,48,48,47,48,57,48,40,43,42,53,47,72,122,95,88,107,116,119,119,101,102,110,106,102,110,168,167,116,109,114,112,155,107,112,90,110,168,97,126,124,94,106,79,75,72,86,70,70,77,79,85,83,71,81,83,76,86,84,78,74,72,88,76,77,72,77,10,10,12,12,10,10,12,12,25,34,10,10,10,10,10,12,12,10", "endOffsets": "158,221,272,326,385,433,482,531,579,628,686,735,776,820,863,917,965,1171,1294,1390,1479,1587,1704,1824,1944,2046,2149,2260,2367,2470,2581,2750,2918,3035,3145,3260,3373,3529,3637,3750,3841,3952,4121,4219,4346,4471,4566,4673,4753,4829,4902,4989,5060,5131,5209,5289,5375,5459,5531,5613,5697,5774,5861,5946,6025,6100,6173,6262,6339,6417,6490,6568,6816,7064,7332,7517,7719,7925,8126,8315,8341,8376,8914,9332,9710,9887,10066,10249,10614,11055"}, "to": {"startLines": "124,125,583,584,585,618,619,620,621,622,623,624,635,643,646,684,685,745,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,785,803,811,812,814,817,818,819,863,864,865,866,868,870,871,872,873,874,875,876,878,879,880,881,890,894,2792,2796,2807,2811,3153,3156,3405,3464,3465,3474,3517,3524,3527,3627,3630,4637", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7601,7664,37869,37920,37974,39943,39991,40040,40089,40137,40186,40244,40800,41185,41325,43255,43309,47260,47745,47868,47964,48053,48161,48278,48398,48518,48620,48723,48834,48941,49044,49155,49324,49492,49609,49719,49834,49947,50103,50211,50324,50415,50526,50695,50793,50920,51045,51140,51672,53309,53997,54070,54216,54639,54710,54788,62333,62419,62503,62575,62710,62881,62958,63045,63130,63209,63284,63357,63517,63594,63672,63745,64399,64647,186430,186698,187201,187403,210002,210203,227882,231599,231634,232172,234602,234980,235157,243486,243669,307826", "endLines": "124,125,583,584,585,618,619,620,621,622,623,624,635,643,646,684,685,745,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,785,803,811,812,814,817,818,819,863,864,865,866,868,870,871,872,873,874,875,876,878,879,880,881,893,897,2795,2798,2810,2814,3155,3158,3405,3464,3473,3480,3523,3526,3529,3629,3635,4646", "endColumns": "62,62,50,53,58,47,48,48,47,48,57,48,40,43,42,53,47,72,122,95,88,107,116,119,119,101,102,110,106,102,110,168,167,116,109,114,112,155,107,112,90,110,168,97,126,124,94,106,79,75,72,86,70,70,77,79,85,83,71,81,83,76,86,84,78,74,72,88,76,77,72,77,10,10,12,12,10,10,12,12,25,34,10,10,10,10,10,12,12,10", "endOffsets": "7659,7722,37915,37969,38028,39986,40035,40084,40132,40181,40239,40288,40836,41224,41363,43304,43352,47328,47863,47959,48048,48156,48273,48393,48513,48615,48718,48829,48936,49039,49150,49319,49487,49604,49714,49829,49942,50098,50206,50319,50410,50521,50690,50788,50915,51040,51135,51242,51747,53380,54065,54152,54282,54705,54783,54863,62414,62498,62570,62652,62789,62953,63040,63125,63204,63279,63352,63441,63589,63667,63740,63818,64642,64890,186693,186878,187398,187604,210198,210387,227903,231629,232167,232585,234975,235152,235331,243664,244029,308262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8bfb6d3e02b19f2d01d43ee6c3a793b\\transformed\\jetified-drawee-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2,141", "startColumns": "4,4", "startOffsets": "55,6080", "endLines": "140,231", "endColumns": "22,22", "endOffsets": "6075,9771"}, "to": {"startLines": "6134,6828", "startColumns": "4,4", "startOffsets": "362084,383963", "endLines": "6267,6913", "endColumns": "22,22", "endOffsets": "365930,385449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f087c01a1d51cb2a0941640443975f6a\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "688", "startColumns": "4", "startOffsets": "43460", "endColumns": "53", "endOffsets": "43509"}}, {"source": "D:\\CadetVocab - Copy\\node_modules\\expo-dev-menu\\android\\build\\intermediates\\packaged_res\\release\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "10", "endColumns": "10", "endOffsets": "579"}, "to": {"startLines": "3439", "startColumns": "4", "startOffsets": "230259", "endLines": "3447", "endColumns": "10", "endOffsets": "230783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3fc7415478e4731dd9355d61543956b8\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "629,653,690,6125,6130", "startColumns": "4,4,4,4,4", "startOffsets": "40498,41643,43564,361765,361935", "endLines": "629,653,690,6129,6133", "endColumns": "56,64,63,24,24", "endOffsets": "40550,41703,43623,361930,362079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c95790bbae57615f21c79472117e61f\\transformed\\material-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,439,443,447,454,461,469,479,488,497,506,507,508,513,514,518,519,525,529,530,531,532,542,543,544,548,549,555,559,560,561,562,563,618,681,726,789,843,906,954,999,1062,1066,1070,1082,1097,1111,1112,1160,1164,1165,1166,1169,1182,1195,1235,1236,1237,1249,1253,1258,1263,1268,1271,1274,1277,1281,1285,1286,1287,1288,1289,1292,1295,1298,1301,1305,1309,1310,1313,1316,1319,1322,1326,1329,1332,1335,1338,1341,1344,1348,1351,1354,1358,1361,1371,1379,1387,1390,1393,1396,1399,1402,1405,1408,1409,1412,1415,1416,1419,1420,1421,1425,1426,1431,1432,1440,1448,1449,1457,1461,1469,1477,1485,1493,1501,1502,1510,1518,1519,1522,1524,1529,1531,1536,1540,1544,1545,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1574,1575,1576,1582,1583,1587,1589,1590,1593,1598,1599,1600,1601,1602,1603,1607,1608,1609,1615,1616,1620,1622,1625,1629,1633,1637,1641,1642,1643,1644,1652,1660,1663,1668,1673,1678,1683,1687,1691,1700,1708,1709,1710,1711,1712,1720,1729,1734,1739,1740,1741,1742,1760,1764,1769,1772,1776,1779,1783,1787,1791,1794,1802,1811,1824,1828,1843,1851,1854,1865,1870,1874,1909,1913,1914,1921,1925,1926,1927,1930,1934,1938,1939,1943,1948,1963,1967,1968,1980,1990,1991,2001,2006,2029,2032,2038,2041,2050,2058,2062,2065,2068,2071,2075,2078,2093,2097,2100,2115,2118,2126,2131,2137,2143,2149,2179,2190,2207,2214,2217,2229,2238,2242,2247,2251,2255,2259,2263,2267,2270,2279,2284,2293,2297,2304,2313,2319,2323,2345,2346,2347,2348,2349,2353,2354,2363,2367,2379,2391,2398,2399,2403,2407,2408,2412,2426,2432,2438,2444,2450,2455,2461,2467,2468,2477,2485,2486,2493,2501,2526,2538,2577,2597,2631,2680,2737,2848,2874,2968,2983,2995,3001,3047,3051,3057,3063,3070,3076,3083,3086,3147,3159,3170,3182,3210,3219,3228,3234,3242,3247,3295,3298,3301,3305,3343,3355,3400,3408,3431,3438,3446,3531,3536,3776,3792", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,206,255,311,371,432,487,537,587,640,698,746,783,830,879,935,986,1035,1089,1143,1192,1248,1295,1351,1411,1472,1544,1608,1661,1713,1771,1836,1884,1950,2008,2077,2135,2205,2279,2347,2414,2484,2550,2623,2683,2743,2803,2862,2919,2977,3035,3081,3141,3195,3263,3332,3400,3453,3505,3555,3601,3651,3698,3756,3814,3873,3933,3995,4057,4119,4181,4243,4305,4373,4434,4496,4558,4611,4665,4716,4761,4825,4884,4946,5020,5091,5157,5231,5300,5371,5444,5515,5583,5656,5732,5802,5880,5948,6014,6075,6144,6208,6274,6342,6408,6471,6539,6610,6675,6748,6811,6892,6956,7022,7092,7162,7232,7302,7369,7434,7500,7553,7629,7695,7782,7858,7934,7981,8046,8096,8150,8229,8307,8380,8445,8508,8574,8645,8716,8778,8847,8913,8973,9040,9107,9163,9214,9267,9319,9373,9444,9507,9566,9628,9687,9760,9827,9887,9950,10025,10097,10193,10264,10320,10391,10448,10505,10571,10635,10706,10763,10816,10879,10931,10989,11056,11140,11225,11306,11374,11451,11524,11594,11666,11736,11809,11873,11943,11991,12060,12112,12170,12236,12303,12369,12450,12525,12581,12634,12695,12753,12803,12852,12901,12950,13012,13064,13109,13190,13244,13297,13351,13402,13451,13502,13563,13624,13686,13736,13777,13836,13895,13954,14015,14071,14127,14194,14255,14320,14375,14440,14509,14577,14655,14724,14784,14855,14929,14994,15066,15136,15203,15287,15356,15423,15493,15556,15623,15691,15774,15853,15943,16020,16088,16155,16233,16290,16347,16415,16481,16537,16597,16656,16710,16760,16810,16858,16920,16971,17037,17110,17190,17270,17334,17397,17464,17535,17593,17654,17720,17779,17846,17906,17966,18029,18097,18158,18225,18303,18373,18422,18479,18548,18609,18697,18785,18873,18961,19017,19104,19191,19278,19365,19423,19479,19550,19615,19677,19752,19825,19894,19964,20033,20088,20144,20200,20261,20319,20375,20430,20492,20545,20602,20696,20765,20866,20917,20975,21045,21114,21184,21254,21324,21391,21458,21533,21600,21659,21713,21767,21821,21874,21926,22000,22065,22121,22179,22241,22296,22339,22384,22427,22474,22519,22570,22621,22672,22723,22771,22837,22899,22962,23034,23091,23154,23211,23271,23336,23403,23468,23525,23586,23644,23714,23771,24091,24241,24372,24470,24585,24670,24718,24797,24862,24951,25108,25265,25418,25572,25631,25805,25983,26161,26343,26660,26842,27024,27214,27404,27603,27776,27886,28071,28208,28428,28612,28772,28930,29114,29317,29488,29708,29930,30085,30292,30476,30579,30720,30885,31056,31256,31460,31662,31867,32068,32267,32471,32549,32850,33016,33171,33273,33407,33684,33969,34359,34815,35324,35866,36331,36793,37264,37357,37464,37807,37914,38159,38280,38689,38937,39037,39142,39261,39795,39942,40061,40312,40445,40860,41114,41226,41347,41480,41627,45714,49885,53249,57434,61428,65562,68582,71862,76010,76262,76527,77587,78434,79354,79445,81633,81843,81952,82071,82255,83185,83931,86428,86523,86554,87424,87710,88113,88515,88858,89070,89271,89484,89773,90058,90131,90218,90303,90402,90522,90683,90846,91007,91172,91339,91392,91525,91645,91743,91856,92049,92175,92327,92469,92639,92795,92967,93258,93370,93499,93728,93946,94801,95388,96002,96170,96312,96473,96616,96784,96941,97136,97228,97401,97563,97658,97827,97921,98010,98253,98342,98635,98749,99158,99572,99688,100106,100347,100777,101212,101622,102044,102454,102576,102985,103401,103523,103707,103775,104119,104199,104555,104705,104849,104933,105322,105420,105528,105622,105752,105860,105982,106118,106226,106346,106480,106602,106730,106872,106998,107138,107264,107382,107514,107612,107722,108022,108134,108252,108716,108832,109135,109261,109357,109487,109888,109998,110122,110260,110370,110492,110804,110928,111058,111534,111662,111977,112115,112261,112423,112639,112795,112999,113067,113151,113255,113817,114448,114606,114825,115056,115279,115514,115736,116002,116594,117193,117307,117451,117563,117687,118258,118856,119351,119897,120042,120135,120227,121588,121976,122274,122463,122669,122862,123072,123289,123550,123681,124113,124637,125281,125478,126426,126983,127106,127879,128100,128300,130277,130512,130636,131144,131358,131461,131591,131766,132013,132204,132344,132538,132808,133689,133977,134107,134872,135517,135663,136224,136462,137935,138085,138502,138667,139353,139823,140019,140181,140336,140480,140714,140881,141655,141941,142101,142716,142875,143203,143430,143795,144166,144527,146281,146910,147986,148506,148658,149648,150385,150588,150834,151081,151323,151644,151949,152172,152344,152885,153154,153648,153909,154349,155094,155459,155764,157464,157570,157700,157838,157962,158208,158308,158944,159245,160106,160861,161300,161424,161665,161853,161987,162178,162957,163226,163517,163796,164113,164335,164630,164913,165017,165664,166229,166349,166846,167380,169004,169715,171763,172605,174529,177450,181203,186428,187706,193536,194244,194931,195315,197485,197716,198020,198337,198794,199101,199531,199690,202899,203517,204046,204541,205841,206332,206826,207199,207541,207749,210970,211081,211218,211453,213307,213855,216074,216515,217745,218166,218482,223124,223389,236918,237987", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,438,442,446,453,460,468,478,487,496,505,506,507,512,513,517,518,524,528,529,530,531,541,542,543,547,548,554,558,559,560,561,562,617,680,725,788,842,905,953,998,1061,1065,1069,1081,1096,1110,1111,1159,1163,1164,1165,1168,1181,1194,1234,1235,1236,1248,1252,1257,1262,1267,1270,1273,1276,1280,1284,1285,1286,1287,1288,1291,1294,1297,1300,1304,1308,1309,1312,1315,1318,1321,1325,1328,1331,1334,1337,1340,1343,1347,1350,1353,1357,1360,1370,1378,1386,1389,1392,1395,1398,1401,1404,1407,1408,1411,1414,1415,1418,1419,1420,1424,1425,1430,1431,1439,1447,1448,1456,1460,1468,1476,1484,1492,1500,1501,1509,1517,1518,1521,1523,1528,1530,1535,1539,1543,1544,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1573,1574,1575,1581,1582,1586,1588,1589,1592,1597,1598,1599,1600,1601,1602,1606,1607,1608,1614,1615,1619,1621,1624,1628,1632,1636,1640,1641,1642,1643,1651,1659,1662,1667,1672,1677,1682,1686,1690,1699,1707,1708,1709,1710,1711,1719,1728,1733,1738,1739,1740,1741,1759,1763,1768,1771,1775,1778,1782,1786,1790,1793,1801,1810,1823,1827,1842,1850,1853,1864,1869,1873,1908,1912,1913,1920,1924,1925,1926,1929,1933,1937,1938,1942,1947,1962,1966,1967,1979,1989,1990,2000,2005,2028,2031,2037,2040,2049,2057,2061,2064,2067,2070,2074,2077,2092,2096,2099,2114,2117,2125,2130,2136,2142,2148,2178,2189,2206,2213,2216,2228,2237,2241,2246,2250,2254,2258,2262,2266,2269,2278,2283,2292,2296,2303,2312,2318,2322,2344,2345,2346,2347,2348,2352,2353,2362,2366,2378,2390,2397,2398,2402,2406,2407,2411,2425,2431,2437,2443,2449,2454,2460,2466,2467,2476,2484,2485,2492,2500,2525,2537,2576,2596,2630,2679,2736,2847,2873,2967,2982,2994,3000,3046,3050,3056,3062,3069,3075,3082,3085,3146,3158,3169,3181,3209,3218,3227,3233,3241,3246,3294,3297,3300,3304,3342,3354,3399,3407,3430,3437,3445,3530,3535,3775,3791,3800", "endColumns": "55,48,55,59,60,54,49,49,52,57,47,36,46,48,55,50,48,53,53,48,55,46,55,59,60,71,63,52,51,57,64,47,65,57,68,57,69,73,67,66,69,65,72,59,59,59,58,56,57,57,45,59,53,67,68,67,52,51,49,45,49,46,57,57,58,59,61,61,61,61,61,61,67,60,61,61,52,53,50,44,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,64,65,52,75,65,86,75,75,46,64,49,53,78,77,72,64,62,65,70,70,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,83,84,80,67,76,72,69,71,69,72,63,69,47,68,51,57,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,53,52,53,50,48,50,60,60,61,49,40,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,65,72,79,79,63,62,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,55,86,86,86,86,57,55,70,64,61,74,72,68,69,68,54,55,55,60,57,55,54,61,52,56,93,68,100,50,57,69,68,69,69,69,66,66,74,66,58,53,53,53,52,51,73,64,55,57,61,54,42,44,42,46,44,50,50,50,50,47,65,61,62,71,56,62,56,59,64,66,64,56,60,57,69,56,12,149,130,97,114,84,47,78,64,88,156,156,152,153,58,173,177,177,181,316,181,181,189,189,198,172,109,184,136,219,183,159,157,183,202,170,219,221,154,206,183,102,140,164,170,199,203,201,204,200,198,203,77,300,165,154,101,10,10,10,10,10,10,10,10,10,10,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,90,10,10,108,118,10,10,10,10,94,30,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,10,10,10,10,10,52,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,10,10,10,113,143,111,123,10,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,129,137,123,10,99,10,10,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,119,10,10,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22", "endOffsets": "201,250,306,366,427,482,532,582,635,693,741,778,825,874,930,981,1030,1084,1138,1187,1243,1290,1346,1406,1467,1539,1603,1656,1708,1766,1831,1879,1945,2003,2072,2130,2200,2274,2342,2409,2479,2545,2618,2678,2738,2798,2857,2914,2972,3030,3076,3136,3190,3258,3327,3395,3448,3500,3550,3596,3646,3693,3751,3809,3868,3928,3990,4052,4114,4176,4238,4300,4368,4429,4491,4553,4606,4660,4711,4756,4820,4879,4941,5015,5086,5152,5226,5295,5366,5439,5510,5578,5651,5727,5797,5875,5943,6009,6070,6139,6203,6269,6337,6403,6466,6534,6605,6670,6743,6806,6887,6951,7017,7087,7157,7227,7297,7364,7429,7495,7548,7624,7690,7777,7853,7929,7976,8041,8091,8145,8224,8302,8375,8440,8503,8569,8640,8711,8773,8842,8908,8968,9035,9102,9158,9209,9262,9314,9368,9439,9502,9561,9623,9682,9755,9822,9882,9945,10020,10092,10188,10259,10315,10386,10443,10500,10566,10630,10701,10758,10811,10874,10926,10984,11051,11135,11220,11301,11369,11446,11519,11589,11661,11731,11804,11868,11938,11986,12055,12107,12165,12231,12298,12364,12445,12520,12576,12629,12690,12748,12798,12847,12896,12945,13007,13059,13104,13185,13239,13292,13346,13397,13446,13497,13558,13619,13681,13731,13772,13831,13890,13949,14010,14066,14122,14189,14250,14315,14370,14435,14504,14572,14650,14719,14779,14850,14924,14989,15061,15131,15198,15282,15351,15418,15488,15551,15618,15686,15769,15848,15938,16015,16083,16150,16228,16285,16342,16410,16476,16532,16592,16651,16705,16755,16805,16853,16915,16966,17032,17105,17185,17265,17329,17392,17459,17530,17588,17649,17715,17774,17841,17901,17961,18024,18092,18153,18220,18298,18368,18417,18474,18543,18604,18692,18780,18868,18956,19012,19099,19186,19273,19360,19418,19474,19545,19610,19672,19747,19820,19889,19959,20028,20083,20139,20195,20256,20314,20370,20425,20487,20540,20597,20691,20760,20861,20912,20970,21040,21109,21179,21249,21319,21386,21453,21528,21595,21654,21708,21762,21816,21869,21921,21995,22060,22116,22174,22236,22291,22334,22379,22422,22469,22514,22565,22616,22667,22718,22766,22832,22894,22957,23029,23086,23149,23206,23266,23331,23398,23463,23520,23581,23639,23709,23766,24086,24236,24367,24465,24580,24665,24713,24792,24857,24946,25103,25260,25413,25567,25626,25800,25978,26156,26338,26655,26837,27019,27209,27399,27598,27771,27881,28066,28203,28423,28607,28767,28925,29109,29312,29483,29703,29925,30080,30287,30471,30574,30715,30880,31051,31251,31455,31657,31862,32063,32262,32466,32544,32845,33011,33166,33268,33402,33679,33964,34354,34810,35319,35861,36326,36788,37259,37352,37459,37802,37909,38154,38275,38684,38932,39032,39137,39256,39790,39937,40056,40307,40440,40855,41109,41221,41342,41475,41622,45709,49880,53244,57429,61423,65557,68577,71857,76005,76257,76522,77582,78429,79349,79440,81628,81838,81947,82066,82250,83180,83926,86423,86518,86549,87419,87705,88108,88510,88853,89065,89266,89479,89768,90053,90126,90213,90298,90397,90517,90678,90841,91002,91167,91334,91387,91520,91640,91738,91851,92044,92170,92322,92464,92634,92790,92962,93253,93365,93494,93723,93941,94796,95383,95997,96165,96307,96468,96611,96779,96936,97131,97223,97396,97558,97653,97822,97916,98005,98248,98337,98630,98744,99153,99567,99683,100101,100342,100772,101207,101617,102039,102449,102571,102980,103396,103518,103702,103770,104114,104194,104550,104700,104844,104928,105317,105415,105523,105617,105747,105855,105977,106113,106221,106341,106475,106597,106725,106867,106993,107133,107259,107377,107509,107607,107717,108017,108129,108247,108711,108827,109130,109256,109352,109482,109883,109993,110117,110255,110365,110487,110799,110923,111053,111529,111657,111972,112110,112256,112418,112634,112790,112994,113062,113146,113250,113812,114443,114601,114820,115051,115274,115509,115731,115997,116589,117188,117302,117446,117558,117682,118253,118851,119346,119892,120037,120130,120222,121583,121971,122269,122458,122664,122857,123067,123284,123545,123676,124108,124632,125276,125473,126421,126978,127101,127874,128095,128295,130272,130507,130631,131139,131353,131456,131586,131761,132008,132199,132339,132533,132803,133684,133972,134102,134867,135512,135658,136219,136457,137930,138080,138497,138662,139348,139818,140014,140176,140331,140475,140709,140876,141650,141936,142096,142711,142870,143198,143425,143790,144161,144522,146276,146905,147981,148501,148653,149643,150380,150583,150829,151076,151318,151639,151944,152167,152339,152880,153149,153643,153904,154344,155089,155454,155759,157459,157565,157695,157833,157957,158203,158303,158939,159240,160101,160856,161295,161419,161660,161848,161982,162173,162952,163221,163512,163791,164108,164330,164625,164908,165012,165659,166224,166344,166841,167375,168999,169710,171758,172600,174524,177445,181198,186423,187701,193531,194239,194926,195310,197480,197711,198015,198332,198789,199096,199526,199685,202894,203512,204041,204536,205836,206327,206821,207194,207536,207744,210965,211076,211213,211448,213302,213850,216069,216510,217740,218161,218477,223119,223384,236913,237982,238316"}, "to": {"startLines": "2,3,4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,33,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,57,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,88,89,90,91,94,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,194,195,196,197,198,199,200,201,307,308,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,571,638,639,640,641,648,651,652,670,671,672,673,674,675,683,693,694,697,698,699,700,701,702,703,704,705,706,707,708,709,712,714,748,749,780,781,782,783,784,794,797,798,799,804,810,813,815,816,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,884,898,902,952,959,966,1136,1146,1155,1164,1233,1234,1235,1240,1241,1245,1246,1252,1256,1257,1258,1259,1269,1270,1271,1275,1276,1282,1286,1356,1357,1358,1359,1414,1477,1522,1585,1639,1702,1750,1795,1858,1862,1866,2638,2653,2667,2668,2716,2720,2721,2722,2725,2738,2751,2791,2815,2816,2828,2832,2837,2842,2847,2850,2853,2856,2860,3003,3004,3005,3006,3088,3091,3094,3097,3100,3104,3108,3109,3112,3115,3118,3121,3125,3128,3131,3134,3137,3140,3143,3147,3150,3159,3163,3166,3176,3184,3192,3195,3198,3201,3204,3207,3282,3285,3286,3289,3292,3293,3296,3297,3298,3302,3303,3308,3309,3317,3325,3326,3334,3338,3346,3354,3362,3370,3378,3379,3387,3395,3396,3481,3483,3488,3490,3495,3499,3530,3531,3536,3537,3538,3539,3540,3541,3542,3543,3544,3545,3546,3547,3548,3549,3550,3551,3552,3553,3554,3555,3556,3560,3561,3562,3568,3569,3573,3575,3576,3579,3584,3585,3586,3587,3588,3589,3593,3594,3595,3601,3602,3606,3608,3611,3615,3619,3623,3648,3649,3650,3651,3659,3667,3670,3675,3680,3685,3690,3694,3698,3707,3715,3716,3717,3718,3719,3727,3736,3741,3746,3747,3748,3749,3767,3771,3776,3779,3783,3786,3790,3794,3798,3932,3940,3949,3962,3966,3981,3989,3992,4003,4008,4012,4047,4051,4052,4059,4063,4064,4065,4068,4072,4076,4077,4081,4086,4101,4105,4106,4118,4128,4129,4139,4144,4167,4170,4176,4179,4188,4196,4200,4203,4206,4209,4213,4216,4231,4235,4238,4253,4256,4264,4269,4275,4281,4287,4317,4328,4345,4352,4355,4367,4376,4380,4385,4389,4393,4397,4401,4405,4408,4417,4422,4431,4435,4442,4451,4457,4461,4479,4480,4481,4482,4483,4487,4488,4496,4500,4512,4524,4531,4532,4536,4540,4541,4545,4559,4565,4571,4577,4583,4588,4594,4600,4601,4610,4618,4619,4626,4793,4808,4818,5365,5385,5419,5454,5566,5673,5693,5774,5993,6005,6011,6055,6059,6119,6307,6349,6355,6362,6365,6414,6423,6434,6446,6469,6475,6481,6487,6492,6662,6709,6739,6742,6781,6819,6914,6957,6965,7083,7087,7095,7191,7195,7409,7472", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,206,255,311,371,432,539,589,639,692,750,798,835,882,931,987,1038,1087,1141,1195,1244,1300,1463,1519,1579,1640,1712,1821,1923,1975,2033,2098,2146,2212,2270,2339,2397,2467,2541,2609,2676,2746,2812,2885,2945,3005,3065,3124,3181,3299,3413,3459,3519,3573,3641,3710,3778,3831,3883,3933,3979,4029,4076,4134,4192,4251,4311,4373,4435,4497,4559,4621,4683,4751,4812,4874,4936,4989,5084,5135,5180,5244,5420,7825,7899,7970,8036,8110,8179,8250,8323,8394,8462,8535,8611,8681,8759,8827,8893,8954,9023,9087,9153,9221,9287,9350,9418,9489,9554,9627,9690,9771,9835,9901,9971,10041,10111,10181,12144,12209,12275,12328,12404,12470,12557,12633,19843,19890,20824,20874,20928,21007,21085,21158,21223,21286,21352,21423,21494,21556,21625,21691,21751,21818,21885,21941,21992,22045,22097,22151,22222,22285,22344,22406,22465,22538,22605,22665,22728,22803,22875,22971,23042,23098,23169,23226,23283,23349,23413,23484,23541,23594,23657,23709,23767,25004,25088,25173,25254,25322,25399,25472,25542,25614,25684,25757,25821,25891,25939,26008,26060,26118,26184,26251,26317,26398,26473,26529,26582,26643,26701,26751,26800,26849,26898,26960,27012,27057,27138,27192,27245,27299,27350,27399,27450,27511,27572,27634,27684,27725,27784,27843,27902,27963,28019,28075,28142,28203,28268,28323,28388,28457,28525,28603,28672,28732,28803,28877,28942,29014,29084,29151,29235,29304,29371,29441,29504,29571,29639,29722,29801,29891,29968,30036,30103,30181,30238,30295,30363,30429,30485,30545,30604,30658,30708,30758,30806,30868,30919,30985,31058,31138,31218,31282,31345,31412,31483,31541,31602,31668,31727,31794,31854,31914,31977,32045,32106,32173,32251,32321,32370,32427,32496,32557,32645,32733,32821,32909,32965,33052,33139,33226,33313,33371,33427,33498,33563,33625,33700,33773,33842,33912,33981,34036,34092,34148,34209,34267,34323,34378,34440,34493,34550,34644,34713,34814,34865,34923,34993,35062,35132,35202,35272,35339,35406,35481,35548,35607,35661,35715,35769,35822,35874,37138,40911,40967,41025,41087,41410,41555,41600,42578,42625,42670,42721,42772,42823,43207,43754,43820,43999,44062,44134,44191,44254,44311,44371,44436,44503,44568,44625,44686,44744,44947,45074,47464,47614,51247,51345,51460,51545,51593,52441,52691,52780,52937,53385,53843,54157,54287,54461,54868,55046,55228,55545,55727,55909,56099,56289,56488,56661,56771,56956,57093,57313,57497,57657,57815,57999,58202,58373,58593,58815,58970,59177,59361,59464,59605,59770,59941,60141,60345,60547,60752,60953,61152,61356,61434,61735,61901,62056,63991,64895,65172,68417,68807,69263,80400,80914,81351,81785,86125,86218,86325,86668,86775,87020,87141,87550,87798,87898,88003,88122,88631,88778,88897,89148,89281,89696,89950,95165,95286,95419,95566,99489,103551,106822,110898,114728,118753,121705,124877,128916,129146,129389,177668,178515,179435,179526,181714,181924,182033,182152,182336,183266,184012,186335,187609,187640,188510,188796,189199,189601,189944,190156,190357,190570,190859,201746,201819,201906,201991,206905,207025,207186,207349,207510,207675,207842,207895,208028,208148,208246,208359,208552,208678,208830,208972,209142,209298,209470,209761,209873,210392,210621,210839,211694,212281,212895,213063,213205,213366,213509,213677,220688,220883,220975,221148,221310,221405,221574,221668,221757,222000,222089,222382,222496,222905,223319,223435,223853,224094,224524,224959,225369,225791,226201,226323,226732,227148,227270,232590,232658,233002,233082,233438,233588,235336,235420,235809,235907,236015,236109,236239,236347,236469,236605,236713,236833,236967,237089,237217,237359,237485,237625,237751,237869,238001,238099,238209,238509,238621,238739,239203,239319,239622,239748,239844,239974,240375,240485,240609,240747,240857,240979,241291,241415,241545,242021,242149,242464,242602,242748,242910,243126,243282,245037,245105,245189,245293,245780,246336,246494,246713,246944,247167,247402,247624,247890,248482,249081,249195,249339,249451,249575,250146,250744,251239,251785,251930,252023,252115,253412,253800,254098,254287,254493,254686,254896,255113,255374,265186,265618,266142,266786,266983,267931,268488,268611,269384,269605,269805,271782,272017,272141,272553,272767,272870,273000,273175,273422,273613,273753,273947,274217,275098,275386,275516,276281,276926,277072,277633,277871,279344,279494,279911,280076,280762,281232,281428,281519,281603,281747,281981,282148,282922,283208,283368,283983,284142,284470,284697,285062,285433,285794,287548,288177,289253,289677,289829,290819,291556,291759,292005,292252,292494,292815,293120,293343,293515,294056,294325,294819,295080,295520,296265,296630,296935,298225,298331,298461,298599,298723,298969,299069,299531,299832,300693,301448,301887,302011,302252,302440,302574,302765,303544,303813,304104,304383,304700,304922,305217,305500,305604,306155,306625,306745,307147,313194,313667,313966,332558,333335,334439,335549,340071,342325,342765,346518,357151,357466,357668,358803,358953,361540,366784,368264,368571,369001,369160,370640,370860,371389,371884,372383,372536,372692,372885,373059,378530,380070,381236,381373,382602,383740,385454,386764,387012,391571,391687,391862,394979,395126,402841,405215", "endLines": "2,3,4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,33,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,57,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,88,89,90,91,94,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,194,195,196,197,198,199,200,201,307,308,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,571,638,639,640,641,648,651,652,670,671,672,673,674,675,683,693,694,697,698,699,700,701,702,703,704,705,706,707,708,709,712,717,748,749,780,781,782,783,784,794,797,798,799,804,810,813,815,816,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,886,901,905,958,965,973,1145,1154,1163,1172,1233,1234,1239,1240,1244,1245,1251,1255,1256,1257,1258,1268,1269,1270,1274,1275,1281,1285,1286,1356,1357,1358,1413,1476,1521,1584,1638,1701,1749,1794,1857,1861,1865,1877,2652,2666,2667,2715,2719,2720,2721,2724,2737,2750,2790,2791,2815,2827,2831,2836,2841,2846,2849,2852,2855,2859,2863,3003,3004,3005,3006,3090,3093,3096,3099,3103,3107,3108,3111,3114,3117,3120,3124,3127,3130,3133,3136,3139,3142,3146,3149,3152,3162,3165,3175,3183,3191,3194,3197,3200,3203,3206,3209,3284,3285,3288,3291,3292,3295,3296,3297,3301,3302,3307,3308,3316,3324,3325,3333,3337,3345,3353,3361,3369,3377,3378,3386,3394,3395,3398,3482,3487,3489,3494,3498,3502,3530,3535,3536,3537,3538,3539,3540,3541,3542,3543,3544,3545,3546,3547,3548,3549,3550,3551,3552,3553,3554,3555,3559,3560,3561,3567,3568,3572,3574,3575,3578,3583,3584,3585,3586,3587,3588,3592,3593,3594,3600,3601,3605,3607,3610,3614,3618,3622,3626,3648,3649,3650,3658,3666,3669,3674,3679,3684,3689,3693,3697,3706,3714,3715,3716,3717,3718,3726,3735,3740,3745,3746,3747,3748,3766,3770,3775,3778,3782,3785,3789,3793,3797,3800,3939,3948,3961,3965,3980,3988,3991,4002,4007,4011,4046,4050,4051,4058,4062,4063,4064,4067,4071,4075,4076,4080,4085,4100,4104,4105,4117,4127,4128,4138,4143,4166,4169,4175,4178,4187,4195,4199,4202,4205,4208,4212,4215,4230,4234,4237,4252,4255,4263,4268,4274,4280,4286,4316,4327,4344,4351,4354,4366,4375,4379,4384,4388,4392,4396,4400,4404,4407,4416,4421,4430,4434,4441,4450,4456,4460,4478,4479,4480,4481,4482,4486,4487,4495,4499,4511,4523,4530,4531,4535,4539,4540,4544,4558,4564,4570,4576,4582,4587,4593,4599,4600,4609,4617,4618,4625,4633,4807,4817,4855,5384,5418,5453,5499,5672,5692,5773,5787,6004,6010,6054,6058,6064,6124,6312,6354,6361,6364,6413,6422,6433,6445,6468,6474,6480,6486,6491,6496,6698,6711,6741,6745,6818,6827,6956,6964,6984,7086,7094,7170,7194,7408,7416,7480", "endColumns": "55,48,55,59,60,54,49,49,52,57,47,36,46,48,55,50,48,53,53,48,55,46,55,59,60,71,63,52,51,57,64,47,65,57,68,57,69,73,67,66,69,65,72,59,59,59,58,56,57,57,45,59,53,67,68,67,52,51,49,45,49,46,57,57,58,59,61,61,61,61,61,61,67,60,61,61,52,53,50,44,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,64,65,52,75,65,86,75,75,46,64,49,53,78,77,72,64,62,65,70,70,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,83,84,80,67,76,72,69,71,69,72,63,69,47,68,51,57,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,53,52,53,50,48,50,60,60,61,49,40,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,65,72,79,79,63,62,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,55,86,86,86,86,57,55,70,64,61,74,72,68,69,68,54,55,55,60,57,55,54,61,52,56,93,68,100,50,57,69,68,69,69,69,66,66,74,66,58,53,53,53,52,51,73,64,55,57,61,54,42,44,42,46,44,50,50,50,50,47,65,61,62,71,56,62,56,59,64,66,64,56,60,57,69,56,12,149,130,97,114,84,47,78,64,88,156,156,152,153,58,173,177,177,181,316,181,181,189,189,198,172,109,184,136,219,183,159,157,183,202,170,219,221,154,206,183,102,140,164,170,199,203,201,204,200,198,203,77,300,165,154,101,10,10,10,10,10,10,10,10,10,10,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,90,10,10,108,118,10,10,10,10,94,30,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,10,10,10,10,10,52,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,10,10,10,113,143,111,123,10,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,129,137,123,10,99,10,10,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,119,10,10,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22", "endOffsets": "201,250,306,366,427,482,584,634,687,745,793,830,877,926,982,1033,1082,1136,1190,1239,1295,1342,1514,1574,1635,1707,1771,1869,1970,2028,2093,2141,2207,2265,2334,2392,2462,2536,2604,2671,2741,2807,2880,2940,3000,3060,3119,3176,3234,3352,3454,3514,3568,3636,3705,3773,3826,3878,3928,3974,4024,4071,4129,4187,4246,4306,4368,4430,4492,4554,4616,4678,4746,4807,4869,4931,4984,5038,5130,5175,5239,5298,5477,7894,7965,8031,8105,8174,8245,8318,8389,8457,8530,8606,8676,8754,8822,8888,8949,9018,9082,9148,9216,9282,9345,9413,9484,9549,9622,9685,9766,9830,9896,9966,10036,10106,10176,10243,12204,12270,12323,12399,12465,12552,12628,12704,19885,19950,20869,20923,21002,21080,21153,21218,21281,21347,21418,21489,21551,21620,21686,21746,21813,21880,21936,21987,22040,22092,22146,22217,22280,22339,22401,22460,22533,22600,22660,22723,22798,22870,22966,23037,23093,23164,23221,23278,23344,23408,23479,23536,23589,23652,23704,23762,23829,25083,25168,25249,25317,25394,25467,25537,25609,25679,25752,25816,25886,25934,26003,26055,26113,26179,26246,26312,26393,26468,26524,26577,26638,26696,26746,26795,26844,26893,26955,27007,27052,27133,27187,27240,27294,27345,27394,27445,27506,27567,27629,27679,27720,27779,27838,27897,27958,28014,28070,28137,28198,28263,28318,28383,28452,28520,28598,28667,28727,28798,28872,28937,29009,29079,29146,29230,29299,29366,29436,29499,29566,29634,29717,29796,29886,29963,30031,30098,30176,30233,30290,30358,30424,30480,30540,30599,30653,30703,30753,30801,30863,30914,30980,31053,31133,31213,31277,31340,31407,31478,31536,31597,31663,31722,31789,31849,31909,31972,32040,32101,32168,32246,32316,32365,32422,32491,32552,32640,32728,32816,32904,32960,33047,33134,33221,33308,33366,33422,33493,33558,33620,33695,33768,33837,33907,33976,34031,34087,34143,34204,34262,34318,34373,34435,34488,34545,34639,34708,34809,34860,34918,34988,35057,35127,35197,35267,35334,35401,35476,35543,35602,35656,35710,35764,35817,35869,35943,37198,40962,41020,41082,41137,41448,41595,41638,42620,42665,42716,42767,42818,42869,43250,43815,43877,44057,44129,44186,44249,44306,44366,44431,44498,44563,44620,44681,44739,44809,44999,45389,47609,47740,51340,51455,51540,51588,51667,52501,52775,52932,53089,53533,53992,54211,54456,54634,55041,55223,55540,55722,55904,56094,56284,56483,56656,56766,56951,57088,57308,57492,57652,57810,57994,58197,58368,58588,58810,58965,59172,59356,59459,59600,59765,59936,60136,60340,60542,60747,60948,61147,61351,61429,61730,61896,62051,62153,64120,65167,65452,68802,69258,69767,80909,81346,81780,82223,86213,86320,86663,86770,87015,87136,87545,87793,87893,87998,88117,88626,88773,88892,89143,89276,89691,89945,90057,95281,95414,95561,99484,103546,106817,110893,114723,118748,121700,124872,128911,129141,129384,130422,178510,179430,179521,181709,181919,182028,182147,182331,183261,184007,186330,186425,187635,188505,188791,189194,189596,189939,190151,190352,190565,190854,191139,201814,201901,201986,202085,207020,207181,207344,207505,207670,207837,207890,208023,208143,208241,208354,208547,208673,208825,208967,209137,209293,209465,209756,209868,209997,210616,210834,211689,212276,212890,213058,213200,213361,213504,213672,213829,220878,220970,221143,221305,221400,221569,221663,221752,221995,222084,222377,222491,222900,223314,223430,223848,224089,224519,224954,225364,225786,226196,226318,226727,227143,227265,227449,232653,232997,233077,233433,233583,233727,235415,235804,235902,236010,236104,236234,236342,236464,236600,236708,236828,236962,237084,237212,237354,237480,237620,237746,237864,237996,238094,238204,238504,238616,238734,239198,239314,239617,239743,239839,239969,240370,240480,240604,240742,240852,240974,241286,241410,241540,242016,242144,242459,242597,242743,242905,243121,243277,243481,245100,245184,245288,245775,246331,246489,246708,246939,247162,247397,247619,247885,248477,249076,249190,249334,249446,249570,250141,250739,251234,251780,251925,252018,252110,253407,253795,254093,254282,254488,254681,254891,255108,255369,255500,265613,266137,266781,266978,267926,268483,268606,269379,269600,269800,271777,272012,272136,272548,272762,272865,272995,273170,273417,273608,273748,273942,274212,275093,275381,275511,276276,276921,277067,277628,277866,279339,279489,279906,280071,280757,281227,281423,281514,281598,281742,281976,282143,282917,283203,283363,283978,284137,284465,284692,285057,285428,285789,287543,288172,289248,289672,289824,290814,291551,291754,292000,292247,292489,292810,293115,293338,293510,294051,294320,294814,295075,295515,296260,296625,296930,298220,298326,298456,298594,298718,298964,299064,299526,299827,300688,301443,301882,302006,302247,302435,302569,302760,303539,303808,304099,304378,304695,304917,305212,305495,305599,306150,306620,306740,307142,307676,313662,313961,315900,333330,334434,335544,337434,342320,342760,346513,347096,357461,357663,358798,358948,359123,361760,367046,368566,368996,369155,370635,370855,371384,371879,372378,372531,372687,372880,373054,373262,379685,380176,381368,381514,383735,383958,386759,387007,387690,391682,391857,394230,395121,402836,403079,405544"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\36dc0d2607f0871053495442ce0bdfcc\\transformed\\coordinatorlayout-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,6,16", "startColumns": "4,4,4,4", "startOffsets": "55,116,261,869", "endLines": "2,5,15,104", "endColumns": "60,12,24,24", "endOffsets": "111,256,864,6075"}, "to": {"startLines": "25,4634,5826,5832", "startColumns": "4,4,4,4", "startOffsets": "1347,307681,348759,348970", "endLines": "25,4636,5831,5915", "endColumns": "60,12,24,24", "endOffsets": "1403,307821,348965,353481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0a43de56c5261a993837e8c0dbd0df27\\transformed\\jetified-glide-4.13.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "632", "startColumns": "4", "startOffsets": "40642", "endColumns": "57", "endOffsets": "40695"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\37315463ce468fae5a77bd90c79d2ce3\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "204,567,568,569,570,3271,3273,3274,3279,3281", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "12841,36926,36979,37032,37085,219866,220042,220164,220426,220621", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "12925,36974,37027,37080,37133,219927,220159,220220,220487,220683"}}, {"source": "D:\\CadetVocab - Copy\\android\\app\\build\\generated\\res\\resValues\\release\\values\\gradleResValues.xml", "from": {"startLines": "6,8", "startColumns": "4,4", "startOffsets": "159,265", "endColumns": "63,68", "endOffsets": "218,329"}, "to": {"startLines": "710,711", "startColumns": "4,4", "startOffsets": "44814,44878", "endColumns": "63,68", "endOffsets": "44873,44942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\509cad9e024e34683798c3572b16898f\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "8,120,121,122,123,312,313,314,934,2799,2801,2804,5510", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "487,7352,7413,7475,7537,20166,20225,20282,67325,186883,186947,187073,337777", "endLines": "8,120,121,122,123,312,313,314,940,2800,2803,2806,5537", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "534,7408,7470,7532,7596,20220,20277,20331,67734,186942,187068,187196,338696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bad11f7f0dbf15cc0a31ab383e6f0c7c\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "746", "startColumns": "4", "startOffsets": "47333", "endColumns": "82", "endOffsets": "47411"}}, {"source": "D:\\CadetVocab - Copy\\node_modules\\expo-dev-launcher\\android\\build\\intermediates\\packaged_res\\release\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,22", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,181,245,309,369,424,496,549,681,797,881,957,1044,1523", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,21,28", "endColumns": "62,62,63,63,59,54,71,52,131,115,83,75,86,10,10", "endOffsets": "113,176,240,304,364,419,491,544,676,792,876,952,1039,1518,1909"}, "to": {"startLines": "163,164,165,166,167,168,169,170,790,791,792,793,869,3503,3510", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10248,10311,10374,10438,10502,10562,10617,10689,52033,52165,52281,52365,62794,233732,234211", "endLines": "163,164,165,166,167,168,169,170,790,791,792,793,869,3509,3516", "endColumns": "62,62,63,63,59,54,71,52,131,115,83,75,86,10,10", "endOffsets": "10306,10369,10433,10497,10557,10612,10684,10737,52160,52276,52360,52436,62876,234206,234597"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-in_values-in.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,99", "endOffsets": "150,248,357,457"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2809,3127,3225,3334", "endColumns": "99,97,108,99", "endOffsets": "2904,3220,3329,3429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,217,273,318,394,466,544,597", "endColumns": "114,46,55,44,75,71,77,52,65", "endOffsets": "165,212,268,313,389,461,539,592,658"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2909,3024,3071,3434,3479,3555,3627,3705,3758", "endColumns": "114,46,55,44,75,71,77,52,65", "endOffsets": "3019,3066,3122,3474,3550,3622,3700,3753,3819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,3824", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,3904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3909", "endColumns": "100", "endOffsets": "4005"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-b+sr+Latn_values-b+sr+Latn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,3257", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,3339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,96", "endOffsets": "161,261,374,471"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2836,2947,3047,3160", "endColumns": "110,99,112,96", "endOffsets": "2942,3042,3155,3252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3344", "endColumns": "100", "endOffsets": "3440"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-port_values-port.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-port\\values-port.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "55", "endOffsets": "106"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-pa_values-pa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,101", "endOffsets": "155,256,370,472"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2767,2872,2973,3087", "endColumns": "104,100,113,101", "endOffsets": "2867,2968,3082,3184"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,3189", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,3264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3269", "endColumns": "100", "endOffsets": "3365"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-h720dp-v13_values-h720dp-v13.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-h720dp-v13\\values-h720dp-v13.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "66", "endOffsets": "117"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-eu_values-eu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,3269", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,3347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3352", "endColumns": "100", "endOffsets": "3448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,257,370", "endColumns": "99,101,112,103", "endOffsets": "150,252,365,469"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2850,2950,3052,3165", "endColumns": "99,101,112,103", "endOffsets": "2945,3047,3160,3264"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-pt_values-pt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,3274", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,3355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3360", "endColumns": "100", "endOffsets": "3456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,104", "endOffsets": "165,264,376,481"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2843,2958,3057,3169", "endColumns": "114,98,111,104", "endOffsets": "2953,3052,3164,3269"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-zh_values-zh.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,182,234,276,333,388,440,490", "endColumns": "79,46,51,41,56,54,51,49,57", "endOffsets": "130,177,229,271,328,383,435,485,543"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-sk_values-sk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,3243", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,3321"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3326", "endColumns": "100", "endOffsets": "3422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,265,380", "endColumns": "106,102,114,100", "endOffsets": "157,260,375,476"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2817,2924,3027,3142", "endColumns": "106,102,114,100", "endOffsets": "2919,3022,3137,3238"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-en-rAU_values-en-rAU.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,3164", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,3242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3247", "endColumns": "100", "endOffsets": "3343"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,97", "endOffsets": "148,245,354,452"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2762,2860,2957,3066", "endColumns": "97,96,108,97", "endOffsets": "2855,2952,3061,3159"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-uz_values-uz.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,3218", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,3297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,173,282,392", "endColumns": "117,108,109,104", "endOffsets": "168,277,387,492"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2776,2894,3003,3113", "endColumns": "117,108,109,104", "endOffsets": "2889,2998,3108,3213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3302", "endColumns": "100", "endOffsets": "3398"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-lo_values-lo.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,3182", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,3259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,251,366", "endColumns": "97,97,114,98", "endOffsets": "148,246,361,460"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2772,2870,2968,3083", "endColumns": "97,97,114,98", "endOffsets": "2865,2963,3078,3177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3264", "endColumns": "100", "endOffsets": "3360"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-gu_values-gu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,366", "endColumns": "100,100,108,99", "endOffsets": "151,252,361,461"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2788,2889,2990,3099", "endColumns": "100,100,108,99", "endOffsets": "2884,2985,3094,3194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3280", "endColumns": "100", "endOffsets": "3376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,3199", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,3275"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-lv_values-lv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,373", "endColumns": "102,98,115,100", "endOffsets": "153,252,368,469"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2989,3092,3191,3307", "endColumns": "102,98,115,100", "endOffsets": "3087,3186,3302,3403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3491", "endColumns": "100", "endOffsets": "3587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,3408", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3486"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ldltr-v21_values-ldltr-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ldltr-v21\\values-ldltr-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "112", "endOffsets": "163"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-fi_values-fi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3267", "endColumns": "100", "endOffsets": "3363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,3186", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,3262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,97", "endOffsets": "153,254,363,461"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2775,2878,2979,3088", "endColumns": "102,100,108,97", "endOffsets": "2873,2974,3083,3181"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ko_values-ko.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,3558", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,3632"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,183,236,278,335,390,443,493", "endColumns": "80,46,52,41,56,54,52,49,59", "endOffsets": "131,178,231,273,330,385,438,488,548"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2771,2852,2899,3241,3283,3340,3395,3448,3498", "endColumns": "80,46,52,41,56,54,52,49,59", "endOffsets": "2847,2894,2947,3278,3335,3390,3443,3493,3553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3637", "endColumns": "100", "endOffsets": "3733"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,141,234,337", "endColumns": "85,92,102,92", "endOffsets": "136,229,332,425"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2685,2952,3045,3148", "endColumns": "85,92,102,92", "endOffsets": "2766,3040,3143,3236"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-mk_values-mk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,273,388", "endColumns": "112,104,114,99", "endOffsets": "163,268,383,483"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2820,2933,3038,3153", "endColumns": "112,104,114,99", "endOffsets": "2928,3033,3148,3248"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3338", "endColumns": "100", "endOffsets": "3434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2900"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,3253", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,3333"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-h480dp-land-v13_values-h480dp-land-v13.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c95790bbae57615f21c79472117e61f\\transformed\\material-1.2.1\\res\\values-h480dp-land-v13\\values-h480dp-land-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,170,226,293,358,413,478,547", "endColumns": "58,55,55,66,64,54,64,68,68", "endOffsets": "109,165,221,288,353,408,473,542,611"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-v21_values-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c95790bbae57615f21c79472117e61f\\transformed\\material-1.2.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,19,22,25,28,31,34,37,38,41,46,57,63,69,75,81,87,88,89,93,96,99,102,105,109,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,202,290,378,466,554,641,728,815,902,995,1102,1207,1326,1539,1798,2069,2287,2519,2755,3005,3121,3291,3612,4641,5098,5440,5784,6134,6484,6628,6784,7177,7395,7617,7843,8059,8300,8559", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,18,21,24,27,30,33,36,37,40,45,56,62,68,74,80,86,87,88,92,95,98,101,104,108,112,115", "endColumns": "67,78,87,87,87,87,86,86,86,86,92,106,104,118,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,143,155,10,10,10,10,10,10,10,10", "endOffsets": "118,197,285,373,461,549,636,723,810,897,990,1097,1202,1321,1534,1793,2064,2282,2514,2750,3000,3116,3286,3607,4636,5093,5435,5779,6129,6479,6623,6779,7172,7390,7612,7838,8054,8295,8554,8731"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,76,77,78,79,81,84,87,182,185,188,191,261,262,265,270,281,341,347,353,359,365,366,367,371,374,377,380,391,395,399", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,241,320,408,496,584,672,759,846,933,6582,6675,6782,6887,7109,7322,7581,13762,13980,14212,14448,19453,19569,19739,20060,21089,24860,25152,25446,25746,26046,26190,26346,26739,26957,27179,27405,28145,28386,28645", "endLines": "3,4,5,6,7,8,9,10,11,12,76,77,78,79,83,86,89,184,187,190,193,261,264,269,280,286,346,352,358,364,365,366,370,373,376,379,382,394,398,401", "endColumns": "67,78,87,87,87,87,86,86,86,86,92,106,104,118,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,143,155,10,10,10,10,10,10,10,10", "endOffsets": "236,315,403,491,579,667,754,841,928,1015,6670,6777,6882,7001,7317,7576,7847,13975,14207,14443,14693,19564,19734,20055,21084,21541,25147,25441,25741,26041,26185,26341,26734,26952,27174,27400,27616,28381,28640,28817"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,31,33,34,35,36,38,40,41,42,43,44,46,48,50,52,54,56,57,62,64,66,67,68,70,72,73,74,75,80,90,133,136,179,194,197,199,201,203,206,210,213,214,215,218,219,220,221,222,223,226,227,229,231,233,235,239,241,242,243,244,246,250,252,254,255,256,257,258,259,287,288,289,299,300,301,313", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1215,1306,1409,1512,1617,1724,1833,1942,2051,2160,2269,2376,2479,2598,2753,2908,3013,3134,3235,3382,3523,3626,3745,3852,3955,4110,4281,4430,4595,4752,4903,5022,5373,5522,5671,5783,5930,6083,6230,6305,6394,6481,7006,7852,10610,10795,13565,14698,14897,15020,15143,15256,15439,15694,15895,15984,16095,16328,16429,16524,16647,16776,16893,17070,17169,17304,17447,17582,17701,17902,18021,18114,18225,18281,18388,18583,18694,18827,18922,19013,19104,19197,19314,21546,21617,21700,22323,22380,22438,23062", "endLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,30,32,33,34,35,37,39,40,41,42,43,45,47,49,51,53,55,56,61,63,65,66,67,69,71,72,73,74,75,80,132,135,178,181,196,198,200,202,205,209,212,213,214,217,218,219,220,221,222,225,226,228,230,232,234,238,240,241,242,243,245,249,251,253,254,255,256,257,258,260,287,288,298,299,300,312,324", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1301,1404,1507,1612,1719,1828,1937,2046,2155,2264,2371,2474,2593,2748,2903,3008,3129,3230,3377,3518,3621,3740,3847,3950,4105,4276,4425,4590,4747,4898,5017,5368,5517,5666,5778,5925,6078,6225,6300,6389,6476,6577,7104,10605,10790,13560,13757,14892,15015,15138,15251,15434,15689,15890,15979,16090,16323,16424,16519,16642,16771,16888,17065,17164,17299,17442,17577,17696,17897,18016,18109,18220,18276,18383,18578,18689,18822,18917,19008,19099,19192,19309,19448,21612,21695,22318,22375,22433,23057,23693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,13,14,15,325,326,333,337,383,386", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1020,1084,1151,23698,23814,24271,24565,27621,27793", "endLines": "2,13,14,15,325,326,333,337,385,390", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1079,1146,1210,23809,23935,24392,24688,27788,28140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\37315463ce468fae5a77bd90c79d2ce3\\transformed\\media-1.0.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "327,330,334,338", "startColumns": "4,4,4,4", "startOffsets": "23940,24108,24397,24693", "endLines": "329,332,336,340", "endColumns": "12,12,12,12", "endOffsets": "24103,24266,24560,24855"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-sq_values-sq.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,3228", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,3305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,271,382", "endColumns": "114,100,110,99", "endOffsets": "165,266,377,477"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2801,2916,3017,3128", "endColumns": "114,100,110,99", "endOffsets": "2911,3012,3123,3223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3310", "endColumns": "100", "endOffsets": "3406"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-night-v8_values-night-v8.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "2,3,4,5,6,7,8,25", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,2549", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,2633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c95790bbae57615f21c79472117e61f\\transformed\\material-1.2.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,267,369,471,587,689,803,931,1047,1169,1305,1425,1559,1679,1791,1917,2055,2179,2309,2431,2569,2685", "endColumns": "87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,137,123,129,121,137,115,119", "endOffsets": "138,262,364,466,582,684,798,926,1042,1164,1300,1420,1554,1674,1786,1912,2050,2174,2304,2426,2564,2680,2800"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,26,27,28,29,30,31,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "687,775,899,1001,1103,1219,1321,1435,1563,1679,1801,1937,2057,2191,2311,2423,2638,2776,2900,3030,3152,3290,3406", "endColumns": "87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,137,123,129,121,137,115,119", "endOffsets": "770,894,996,1098,1214,1316,1430,1558,1674,1796,1932,2052,2186,2306,2418,2544,2771,2895,3025,3147,3285,3401,3521"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-et_values-et.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3906", "endColumns": "100", "endOffsets": "4002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,3823", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,3901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,362", "endColumns": "99,99,106,97", "endOffsets": "150,250,357,455"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2827,3133,3233,3340", "endColumns": "99,99,106,97", "endOffsets": "2922,3228,3335,3433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,206,261,312,389,462,525,578", "endColumns": "103,46,54,50,76,72,62,52,67", "endOffsets": "154,201,256,307,384,457,520,573,641"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2927,3031,3078,3438,3489,3566,3639,3702,3755", "endColumns": "103,46,54,50,76,72,62,52,67", "endOffsets": "3026,3073,3128,3484,3561,3634,3697,3750,3818"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-sl_values-sl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3352", "endColumns": "100", "endOffsets": "3448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,100", "endOffsets": "156,260,372,473"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2845,2951,3055,3167", "endColumns": "105,103,111,100", "endOffsets": "2946,3050,3162,3263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,3268", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,3347"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-v23_values-v23.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1225,1975,2094,2221,2443,2667,2782,2889,3002", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1220,1970,2089,2216,2438,2662,2777,2884,2997,3227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\509cad9e024e34683798c3572b16898f\\transformed\\cardview-1.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "3232", "endLines": "52", "endColumns": "12", "endOffsets": "3377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c95790bbae57615f21c79472117e61f\\transformed\\material-1.2.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,6", "startColumns": "4,4", "startOffsets": "55,320", "endLines": "5,9", "endColumns": "10,10", "endOffsets": "315,588"}, "to": {"startLines": "53,57", "startColumns": "4,4", "startOffsets": "3382,3647", "endLines": "56,60", "endColumns": "10,10", "endOffsets": "3642,3915"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-watch-v21_values-watch-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-watch-v21\\values-watch-v21.xml", "from": {"startLines": "2,6,10", "startColumns": "4,4,4", "startOffsets": "55,271,499", "endLines": "5,9,13", "endColumns": "12,12,12", "endOffsets": "266,494,724"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ne_values-ne.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,274,388", "endColumns": "106,111,113,113", "endOffsets": "157,269,383,497"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2865,2972,3084,3198", "endColumns": "106,111,113,113", "endOffsets": "2967,3079,3193,3307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,2865", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,2940"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,3312", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,3387"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3392", "endColumns": "100", "endOffsets": "3488"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-v25_values-v25.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-v25\\values-v25.xml", "from": {"startLines": "2,3,4,6", "startColumns": "4,4,4,4", "startOffsets": "55,126,209,308", "endLines": "2,3,5,7", "endColumns": "70,82,12,12", "endOffsets": "121,204,303,414"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-fr_values-fr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4107", "endColumns": "100", "endOffsets": "4203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,103", "endOffsets": "157,259,378,482"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2847,3175,3277,3396", "endColumns": "106,101,118,103", "endOffsets": "2949,3272,3391,3495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,212,276,325,403,477,540,604,665,741", "endColumns": "109,46,63,48,77,73,62,63,60,75,54", "endOffsets": "160,207,271,320,398,472,535,599,660,736,791"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41,42,43", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2954,3064,3111,3500,3549,3627,3701,3764,3828,3889,3965", "endColumns": "109,46,63,48,77,73,62,63,60,75,54", "endOffsets": "3059,3106,3170,3544,3622,3696,3759,3823,3884,3960,4015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,4020", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,4102"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-zh-rTW_values-zh-rTW.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,177,229,271,328,383,435,485", "endColumns": "74,46,51,41,56,54,51,49,57", "endOffsets": "125,172,224,266,323,378,430,480,538"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2755,2830,2877,3214,3256,3313,3368,3420,3470", "endColumns": "74,46,51,41,56,54,51,49,57", "endOffsets": "2825,2872,2924,3251,3308,3363,3415,3465,3523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,3528", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,3602"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,139,231,332", "endColumns": "83,91,100,91", "endOffsets": "134,226,327,419"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2671,2929,3021,3122", "endColumns": "83,91,100,91", "endOffsets": "2750,3016,3117,3209"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3607", "endColumns": "100", "endOffsets": "3703"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ka_values-ka.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3313", "endColumns": "100", "endOffsets": "3409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,3231", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,3308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,264,374", "endColumns": "105,102,109,103", "endOffsets": "156,259,369,473"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2808,2914,3017,3127", "endColumns": "105,102,109,103", "endOffsets": "2909,3012,3122,3226"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ur_values-ur.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3916", "endColumns": "100", "endOffsets": "4012"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,105", "endOffsets": "151,252,363,469"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2832,3137,3238,3349", "endColumns": "100,100,110,105", "endOffsets": "2928,3233,3344,3450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,206,259,306,371,442,509,564", "endColumns": "103,46,52,46,64,70,66,54,69", "endOffsets": "154,201,254,301,366,437,504,559,629"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2933,3037,3084,3455,3502,3567,3638,3705,3760", "endColumns": "103,46,52,46,64,70,66,54,69", "endOffsets": "3032,3079,3132,3497,3562,3633,3700,3755,3825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,3830", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,3911"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-hr_values-hr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,3236", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,3316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,100", "endOffsets": "155,255,369,470"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2816,2921,3021,3135", "endColumns": "104,99,113,100", "endOffsets": "2916,3016,3130,3231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3321", "endColumns": "100", "endOffsets": "3417"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ta_values-ta.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3382", "endColumns": "100", "endOffsets": "3478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,274,381", "endColumns": "116,101,106,102", "endOffsets": "167,269,376,479"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2871,2988,3090,3197", "endColumns": "116,101,106,102", "endOffsets": "2983,3085,3192,3295"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,3300", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,3377"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ro_values-ro.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,3279", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,3358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3363", "endColumns": "100", "endOffsets": "3459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,379", "endColumns": "106,101,114,103", "endOffsets": "157,259,374,478"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2851,2958,3060,3175", "endColumns": "106,101,114,103", "endOffsets": "2953,3055,3170,3274"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-cs_values-cs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,3829", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,3907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3912", "endColumns": "100", "endOffsets": "4008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,216,274,323,395,462,546,600", "endColumns": "113,46,57,48,71,66,83,53,65", "endOffsets": "164,211,269,318,390,457,541,595,661"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2901,3015,3062,3437,3486,3558,3625,3709,3763", "endColumns": "113,46,57,48,71,66,83,53,65", "endOffsets": "3010,3057,3115,3481,3553,3620,3704,3758,3824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,157,260,374", "endColumns": "101,102,113,99", "endOffsets": "152,255,369,469"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2799,3120,3223,3337", "endColumns": "101,102,113,99", "endOffsets": "2896,3218,3332,3432"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-v17_values-v17.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-v17\\values-v17.xml", "from": {"startLines": "2,5,9,12,15,18,22,25,29,33,37,40,43,46,50,53,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,456,614,764,936,1161,1331,1559,1783,2025,2196,2370,2539,2812,3012,3216", "endLines": "4,8,11,14,17,21,24,28,32,36,39,42,45,49,52,56,60", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "223,451,609,759,931,1156,1326,1554,1778,2020,2191,2365,2534,2807,3007,3211,3540"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-zu_values-zu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3316", "endColumns": "100", "endOffsets": "3412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,3234", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,3311"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,275,387", "endColumns": "111,107,111,110", "endOffsets": "162,270,382,493"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2791,2903,3011,3123", "endColumns": "111,107,111,110", "endOffsets": "2898,3006,3118,3229"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-bg_values-bg.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,386", "endColumns": "110,107,111,108", "endOffsets": "161,269,381,490"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2854,2965,3073,3185", "endColumns": "110,107,111,108", "endOffsets": "2960,3068,3180,3289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,3294", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,3373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3378", "endColumns": "100", "endOffsets": "3474"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-en-rGB_values-en-rGB.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,97", "endOffsets": "148,245,354,452"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2762,2860,2957,3066", "endColumns": "97,96,108,97", "endOffsets": "2855,2952,3061,3159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3247", "endColumns": "100", "endOffsets": "3343"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,3164", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,3242"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-kk_values-kk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,3212", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,3289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3294", "endColumns": "100", "endOffsets": "3390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,259,367", "endColumns": "99,103,107,103", "endOffsets": "150,254,362,466"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2796,2896,3000,3108", "endColumns": "99,103,107,103", "endOffsets": "2891,2995,3103,3207"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-sv_values-sv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,2853"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,3765", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,3840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,96", "endOffsets": "150,250,363,460"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2778,3092,3192,3305", "endColumns": "99,99,112,96", "endOffsets": "2873,3187,3300,3397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3845", "endColumns": "100", "endOffsets": "3941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,213,269,313,383,448,509,569", "endColumns": "110,46,55,43,69,64,60,59,62", "endOffsets": "161,208,264,308,378,443,504,564,627"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2878,2989,3036,3402,3446,3516,3581,3642,3702", "endColumns": "110,46,55,43,69,64,60,59,62", "endOffsets": "2984,3031,3087,3441,3511,3576,3637,3697,3760"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-watch-v20_values-watch-v20.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-watch-v20\\values-watch-v20.xml", "from": {"startLines": "2,5,8", "startColumns": "4,4,4", "startOffsets": "55,214,385", "endLines": "4,7,10", "endColumns": "12,12,12", "endOffsets": "209,380,553"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-as_values-as.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3358", "endColumns": "100", "endOffsets": "3454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,3275", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,3353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,269,377", "endColumns": "107,105,107,104", "endOffsets": "158,264,372,477"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2848,2956,3062,3170", "endColumns": "107,105,107,104", "endOffsets": "2951,3057,3165,3270"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-pt-rPT_values-pt-rPT.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,3265", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,3346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,101", "endOffsets": "166,265,377,479"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2836,2952,3051,3163", "endColumns": "115,98,111,101", "endOffsets": "2947,3046,3158,3260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3351", "endColumns": "100", "endOffsets": "3447"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-sr_values-sr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,96", "endOffsets": "161,261,374,471"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2833,2944,3044,3157", "endColumns": "110,99,112,96", "endOffsets": "2939,3039,3152,3249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,3254", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,3336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3341", "endColumns": "100", "endOffsets": "3437"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-pt-rBR_values-pt-rBR.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,104", "endOffsets": "165,264,376,481"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2843,3183,3282,3394", "endColumns": "114,98,111,104", "endOffsets": "2953,3277,3389,3494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4103", "endColumns": "100", "endOffsets": "4199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,4017", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,4098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,224,280,328,403,475,543,611,664,743", "endColumns": "121,46,55,47,74,71,67,67,52,78,54", "endOffsets": "172,219,275,323,398,470,538,606,659,738,793"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41,42,43", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2958,3080,3127,3499,3547,3622,3694,3762,3830,3883,3962", "endColumns": "121,46,55,47,74,71,67,67,52,78,54", "endOffsets": "3075,3122,3178,3542,3617,3689,3757,3825,3878,3957,4012"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-si_values-si.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,270,386", "endColumns": "107,106,115,103", "endOffsets": "158,265,381,485"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2821,2929,3036,3152", "endColumns": "107,106,115,103", "endOffsets": "2924,3031,3147,3251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3338", "endColumns": "100", "endOffsets": "3434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,3256", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,3333"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ky_values-ky.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,259,368", "endColumns": "98,104,108,104", "endOffsets": "149,254,363,468"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2817,2916,3021,3130", "endColumns": "98,104,108,104", "endOffsets": "2911,3016,3125,3230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3317", "endColumns": "100", "endOffsets": "3413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,3235", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,3312"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-da_values-da.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,3171", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,3246"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3251", "endColumns": "100", "endOffsets": "3347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,95", "endOffsets": "162,261,368,464"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2757,2869,2968,3075", "endColumns": "111,98,106,95", "endOffsets": "2864,2963,3070,3166"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ca_values-ca.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3347", "endColumns": "100", "endOffsets": "3443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,3265", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,3342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,271,382", "endColumns": "112,102,110,107", "endOffsets": "163,266,377,485"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2830,2943,3046,3157", "endColumns": "112,102,110,107", "endOffsets": "2938,3041,3152,3260"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-it_values-it.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,3948", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,4025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,101", "endOffsets": "150,248,358,460"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2799,3128,3226,3336", "endColumns": "99,97,109,101", "endOffsets": "2894,3221,3331,3433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,226,284,333,411,485,557,610,667,738", "endColumns": "123,46,57,48,77,73,71,52,56,70,55", "endOffsets": "174,221,279,328,406,480,552,605,662,733,789"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41,42,43", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2899,3023,3070,3438,3487,3565,3639,3711,3764,3821,3892", "endColumns": "123,46,57,48,77,73,71,52,56,70,55", "endOffsets": "3018,3065,3123,3482,3560,3634,3706,3759,3816,3887,3943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4030", "endColumns": "100", "endOffsets": "4126"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-be_values-be.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,272,384", "endColumns": "108,107,111,105", "endOffsets": "159,267,379,485"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2835,2944,3052,3164", "endColumns": "108,107,111,105", "endOffsets": "2939,3047,3159,3265"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3352", "endColumns": "100", "endOffsets": "3448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,3270", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,3347"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ar_values-ar.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,3837", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,3914"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "3919", "endColumns": "100", "endOffsets": "4015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,154,201,255,299,363,425,498,549,605,670", "endColumns": "98,46,53,43,63,61,72,50,55,64,55", "endOffsets": "149,196,250,294,358,420,493,544,600,665,721"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41,42,43", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2859,2958,3005,3366,3410,3474,3536,3609,3660,3716,3781", "endColumns": "98,46,53,43,63,61,72,50,55,64,55", "endOffsets": "2953,3000,3054,3405,3469,3531,3604,3655,3711,3776,3832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,100", "endOffsets": "150,248,356,457"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2759,3059,3157,3265", "endColumns": "99,97,107,100", "endOffsets": "2854,3152,3260,3361"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-fr-rCA_values-fr-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,103", "endOffsets": "157,259,378,482"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2855,2962,3064,3183", "endColumns": "106,101,118,103", "endOffsets": "2957,3059,3178,3282"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3374", "endColumns": "100", "endOffsets": "3470"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,3287", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,3369"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-bn_values-bn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,372", "endColumns": "105,101,108,104", "endOffsets": "156,258,367,472"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2835,2941,3043,3152", "endColumns": "105,101,108,104", "endOffsets": "2936,3038,3147,3252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,3257", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,3339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3344", "endColumns": "100", "endOffsets": "3440"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-nl_values-nl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,3967", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,4045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,97", "endOffsets": "153,254,365,463"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2827,3161,3262,3373", "endColumns": "102,100,110,97", "endOffsets": "2925,3257,3368,3466"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,226,286,335,409,479,550,605,658,727", "endColumns": "123,46,59,48,73,69,70,54,52,68,54", "endOffsets": "174,221,281,330,404,474,545,600,653,722,777"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41,42,43", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2930,3054,3101,3471,3520,3594,3664,3735,3790,3843,3912", "endColumns": "123,46,59,48,73,69,70,54,52,68,54", "endOffsets": "3049,3096,3156,3515,3589,3659,3730,3785,3838,3907,3962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4050", "endColumns": "100", "endOffsets": "4146"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-te_values-te.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3376", "endColumns": "100", "endOffsets": "3472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,272,383", "endColumns": "110,105,110,105", "endOffsets": "161,267,378,484"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2859,2970,3076,3187", "endColumns": "110,105,110,105", "endOffsets": "2965,3071,3182,3288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,3293", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,3371"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ml_values-ml.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,267,378", "endColumns": "108,102,110,102", "endOffsets": "159,262,373,476"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2854,2963,3066,3177", "endColumns": "108,102,110,102", "endOffsets": "2958,3061,3172,3275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3363", "endColumns": "100", "endOffsets": "3459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,3280", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,3358"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-small-v4_values-small-v4.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c95790bbae57615f21c79472117e61f\\transformed\\material-1.2.1\\res\\values-small-v4\\values-small-v4.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,128,198,270", "endColumns": "72,69,71,69", "endOffsets": "123,193,265,335"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-v24_values-v24.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,212", "endColumns": "156,134", "endOffsets": "207,342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\37315463ce468fae5a77bd90c79d2ce3\\transformed\\media-1.0.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,121,182,248", "endColumns": "65,60,65,66", "endOffsets": "116,177,243,310"}, "to": {"startLines": "4,5,6,7", "startColumns": "4,4,4,4", "startOffsets": "347,413,474,540", "endColumns": "65,60,65,66", "endOffsets": "408,469,535,602"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-tr_values-tr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,104", "endOffsets": "150,256,363,468"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2797,3104,3210,3317", "endColumns": "99,105,106,104", "endOffsets": "2892,3205,3312,3417"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3877", "endColumns": "100", "endOffsets": "3973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,208,262,307,378,447,513,570", "endColumns": "105,46,53,44,70,68,65,56,66", "endOffsets": "156,203,257,302,373,442,508,565,632"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2897,3003,3050,3422,3467,3538,3607,3673,3730", "endColumns": "105,46,53,44,70,68,65,56,66", "endOffsets": "2998,3045,3099,3462,3533,3602,3668,3725,3792"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,3797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,3872"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-es_values-es.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,103", "endOffsets": "157,258,373,477"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2836,3167,3268,3383", "endColumns": "106,100,114,103", "endOffsets": "2938,3263,3378,3482"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4080", "endColumns": "100", "endOffsets": "4176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,223,279,332,406,476,543,609,662,734", "endColumns": "120,46,55,52,73,69,66,65,52,71,54", "endOffsets": "171,218,274,327,401,471,538,604,657,729,784"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41,42,43", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2943,3064,3111,3487,3540,3614,3684,3751,3817,3870,3942", "endColumns": "120,46,55,52,73,69,66,65,52,71,54", "endOffsets": "3059,3106,3162,3535,3609,3679,3746,3812,3865,3937,3992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,3997", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,4075"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-sw_values-sw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3318", "endColumns": "100", "endOffsets": "3414"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,270,387", "endColumns": "113,100,116,101", "endOffsets": "164,265,382,484"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2801,2915,3016,3133", "endColumns": "113,100,116,101", "endOffsets": "2910,3011,3128,3230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,3235", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,3313"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-my_values-my.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3388", "endColumns": "100", "endOffsets": "3484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,270,385", "endColumns": "108,105,114,105", "endOffsets": "159,265,380,486"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2866,2975,3081,3196", "endColumns": "108,105,114,105", "endOffsets": "2970,3076,3191,3297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,3302", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,3383"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-xlarge-v4_values-xlarge-v4.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-xlarge-v4\\values-xlarge-v4.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,126,197,267,337,405", "endColumns": "70,70,69,69,67,67", "endOffsets": "121,192,262,332,400,468"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-am_values-am.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,3116", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,3191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3196", "endColumns": "100", "endOffsets": "3292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,95", "endOffsets": "146,241,347,443"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2723,2819,2914,3020", "endColumns": "95,94,105,95", "endOffsets": "2814,2909,3015,3111"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-h360dp-land-v13_values-h360dp-land-v13.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c95790bbae57615f21c79472117e61f\\transformed\\material-1.2.1\\res\\values-h360dp-land-v13\\values-h360dp-land-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,170,226,293,358,413,478,547", "endColumns": "58,55,55,66,64,54,64,68,68", "endOffsets": "109,165,221,288,353,408,473,542,611"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-is_values-is.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3271", "endColumns": "100", "endOffsets": "3367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,3190", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,3266"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,99", "endOffsets": "154,255,361,461"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2779,2883,2984,3090", "endColumns": "103,100,105,99", "endOffsets": "2878,2979,3085,3185"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-hu_values-hu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,102", "endOffsets": "146,247,362,465"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2859,2955,3056,3171", "endColumns": "95,100,114,102", "endOffsets": "2950,3051,3166,3269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3358", "endColumns": "100", "endOffsets": "3454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,3274", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,3353"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-af_values-af.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,3196", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,3272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3277", "endColumns": "100", "endOffsets": "3373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,262,375", "endColumns": "104,101,112,98", "endOffsets": "155,257,370,469"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2777,2882,2984,3097", "endColumns": "104,101,112,98", "endOffsets": "2877,2979,3092,3191"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-az_values-az.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,3223", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,3302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3307", "endColumns": "100", "endOffsets": "3403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,255,363", "endColumns": "95,103,107,101", "endOffsets": "146,250,358,460"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2813,2909,3013,3121", "endColumns": "95,103,107,101", "endOffsets": "2904,3008,3116,3218"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-gl_values-gl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,3281", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,3359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3364", "endColumns": "100", "endOffsets": "3460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,376", "endColumns": "106,101,111,104", "endOffsets": "157,259,371,476"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2855,2962,3064,3176", "endColumns": "106,101,111,104", "endOffsets": "2957,3059,3171,3276"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-or_values-or.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,270,383", "endColumns": "109,104,112,107", "endOffsets": "160,265,378,486"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2869,2979,3084,3197", "endColumns": "109,104,112,107", "endOffsets": "2974,3079,3192,3300"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3395", "endColumns": "100", "endOffsets": "3491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,2869", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,2954"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,3305", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,3390"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-land_values-land.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c95790bbae57615f21c79472117e61f\\transformed\\material-1.2.1\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,24,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,198,270,340,405,472,542,614,683,752,834,924,1000,1068,1135,1213,1278,1345,1517,1912", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,23,29,34", "endColumns": "72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10", "endOffsets": "123,193,265,335,400,467,537,609,678,747,829,919,995,1063,1130,1208,1273,1340,1512,1907,2176"}, "to": {"startLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,27,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,337,407,479,549,614,681,751,823,892,961,1043,1133,1209,1277,1344,1422,1487,1554,1726,2121", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,26,32,37", "endColumns": "72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10", "endOffsets": "332,402,474,544,609,676,746,818,887,956,1038,1128,1204,1272,1339,1417,1482,1549,1721,2116,2385"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ru_values-ru.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3333", "endColumns": "100", "endOffsets": "3429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,3251", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,3328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,103", "endOffsets": "158,263,375,479"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2822,2930,3035,3147", "endColumns": "107,104,111,103", "endOffsets": "2925,3030,3142,3246"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-lt_values-lt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,265,379", "endColumns": "104,104,113,104", "endOffsets": "155,260,374,479"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2874,2979,3084,3198", "endColumns": "104,104,113,104", "endOffsets": "2974,3079,3193,3298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,3303", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,3382"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3387", "endColumns": "100", "endOffsets": "3483"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-mr_values-mr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3289", "endColumns": "100", "endOffsets": "3385"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,100", "endOffsets": "151,252,363,464"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2795,2896,2997,3108", "endColumns": "100,100,110,100", "endOffsets": "2891,2992,3103,3204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,3209", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,3284"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-vi_values-vi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3901", "endColumns": "100", "endOffsets": "3997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,3816", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,3896"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,271,385", "endColumns": "110,104,113,103", "endOffsets": "161,266,380,484"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2798,3118,3223,3337", "endColumns": "110,104,113,103", "endOffsets": "2904,3218,3332,3436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,211,264,307,380,449,523,575", "endColumns": "108,46,52,42,72,68,73,51,63", "endOffsets": "159,206,259,302,375,444,518,570,634"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2909,3018,3065,3441,3484,3557,3626,3700,3752", "endColumns": "108,46,52,42,72,68,73,51,63", "endOffsets": "3013,3060,3113,3479,3552,3621,3695,3747,3811"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-km_values-km.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,152,249,382", "endColumns": "96,96,132,98", "endOffsets": "147,244,377,476"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2793,2890,2987,3120", "endColumns": "96,96,132,98", "endOffsets": "2885,2982,3115,3214"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3303", "endColumns": "100", "endOffsets": "3399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,3219", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,3298"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-he_values-he.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-he\\values-he.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,193,247,291,355,416,483,535", "endColumns": "90,46,53,43,63,60,66,51,61", "endOffsets": "141,188,242,286,350,411,478,530,592"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-en-rCA_values-en-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,3164", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,3242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,97", "endOffsets": "148,245,354,452"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2762,2860,2957,3066", "endColumns": "97,96,108,97", "endOffsets": "2855,2952,3061,3159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3247", "endColumns": "100", "endOffsets": "3343"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-es-rGT_values-es-rGT.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-es-rGT\\values-es-rGT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,217,273,326,402,474,541,607,660,732", "endColumns": "114,46,55,52,75,71,66,65,52,71,54", "endOffsets": "165,212,268,321,397,469,536,602,655,727,782"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ru-rRU_values-ru-rRU.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-ru-rRU\\values-ru-rRU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,214,272,320,396,468,531,596", "endColumns": "111,46,57,47,75,71,62,64,69", "endOffsets": "162,209,267,315,391,463,526,591,661"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-en-rIN_values-en-rIN.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,3164", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,3242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,97", "endOffsets": "148,245,354,452"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2762,2860,2957,3066", "endColumns": "97,96,108,97", "endOffsets": "2855,2952,3061,3159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3247", "endColumns": "100", "endOffsets": "3343"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-uk_values-uk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,106", "endOffsets": "160,267,387,494"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2829,2939,3046,3166", "endColumns": "109,106,119,106", "endOffsets": "2934,3041,3161,3268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3355", "endColumns": "100", "endOffsets": "3451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,3273", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,3350"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-de_values-de.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4067", "endColumns": "100", "endOffsets": "4163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,98", "endOffsets": "154,255,366,465"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2832,3180,3281,3392", "endColumns": "103,100,110,98", "endOffsets": "2931,3276,3387,3486"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,191,238,299,347,419,487,564,618,671,738", "endColumns": "135,46,60,47,71,67,76,53,52,66,54", "endOffsets": "186,233,294,342,414,482,559,613,666,733,788"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41,42,43", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2936,3072,3119,3491,3539,3611,3679,3756,3810,3863,3930", "endColumns": "135,46,60,47,71,67,76,53,52,66,54", "endOffsets": "3067,3114,3175,3534,3606,3674,3751,3805,3858,3925,3980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,3985", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,4062"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-w360dp-port-v13_values-w360dp-port-v13.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c95790bbae57615f21c79472117e61f\\transformed\\material-1.2.1\\res\\values-w360dp-port-v13\\values-w360dp-port-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,114,170,226,293,358,413,478", "endColumns": "58,55,55,66,64,54,64,68", "endOffsets": "109,165,221,288,353,408,473,542"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-bs_values-bs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,3247", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,3329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3334", "endColumns": "100", "endOffsets": "3430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,259,373", "endColumns": "103,99,113,98", "endOffsets": "154,254,368,467"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2830,2934,3034,3148", "endColumns": "103,99,113,98", "endOffsets": "2929,3029,3143,3242"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-fa_values-fa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,3809", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,3886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,251,362", "endColumns": "98,96,110,101", "endOffsets": "149,246,357,459"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2795,3100,3197,3308", "endColumns": "98,96,110,101", "endOffsets": "2889,3192,3303,3405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,152,199,261,311,387,462,535,587", "endColumns": "96,46,61,49,75,74,72,51,72", "endOffsets": "147,194,256,306,382,457,530,582,655"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2894,2991,3038,3410,3460,3536,3611,3684,3736", "endColumns": "96,46,61,49,75,74,72,51,72", "endOffsets": "2986,3033,3095,3455,3531,3606,3679,3731,3804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3891", "endColumns": "100", "endOffsets": "3987"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-el_values-el.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,397", "endColumns": "109,106,124,108", "endOffsets": "160,267,392,501"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2886,2996,3103,3228", "endColumns": "109,106,124,108", "endOffsets": "2991,3098,3223,3332"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3423", "endColumns": "100", "endOffsets": "3519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,3337", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,3418"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-id_values-id.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-id\\values-id.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,217,273,318,394,466,544,597", "endColumns": "114,46,55,44,75,71,77,52,65", "endOffsets": "165,212,268,313,389,461,539,592,658"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-v28_values-v28.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c95790bbae57615f21c79472117e61f\\transformed\\material-1.2.1\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,143,231,319,407,494,581,668", "endColumns": "87,87,87,87,86,86,86,86", "endOffsets": "138,226,314,402,489,576,663,750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,8", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,397", "endLines": "2,3,7,11", "endColumns": "74,86,12,12", "endOffsets": "125,212,392,584"}, "to": {"startLines": "10,11,12,16", "startColumns": "4,4,4,4", "startOffsets": "755,830,917,1097", "endLines": "10,11,15,19", "endColumns": "74,86,12,12", "endOffsets": "825,912,1092,1284"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-large-v4_values-large-v4.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-large-v4\\values-large-v4.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,185,256,326,396,464,532,636", "endColumns": "58,70,70,69,69,67,67,103,115", "endOffsets": "109,180,251,321,391,459,527,631,747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c95790bbae57615f21c79472117e61f\\transformed\\material-1.2.1\\res\\values-large-v4\\values-large-v4.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,177", "endColumns": "121,133", "endOffsets": "172,306"}, "to": {"startLines": "11,12", "startColumns": "4,4", "startOffsets": "752,874", "endColumns": "121,133", "endOffsets": "869,1003"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-ja_values-ja.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3655", "endColumns": "100", "endOffsets": "3751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,3576", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,3650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,189,243,285,342,397,447,498", "endColumns": "86,46,53,41,56,54,49,50,58", "endOffsets": "137,184,238,280,337,392,442,493,552"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2784,2871,2918,3262,3304,3361,3416,3466,3517", "endColumns": "86,46,53,41,56,54,49,50,58", "endOffsets": "2866,2913,2967,3299,3356,3411,3461,3512,3571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,93", "endOffsets": "143,238,339,433"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2691,2972,3067,3168", "endColumns": "92,94,100,93", "endOffsets": "2779,3062,3163,3257"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-iw_values-iw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,247,353", "endColumns": "90,100,105,100", "endOffsets": "141,242,348,449"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2761,2852,2953,3059", "endColumns": "90,100,105,100", "endOffsets": "2847,2948,3054,3155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3242", "endColumns": "100", "endOffsets": "3338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,3160", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,3237"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-sw600dp-v13_values-sw600dp-v13.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,124,193,263,337,413,472,543", "endColumns": "68,68,69,73,75,58,70,67", "endOffsets": "119,188,258,332,408,467,538,606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c95790bbae57615f21c79472117e61f\\transformed\\material-1.2.1\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,185,256,328,386,444,553,617,673,732,795", "endLines": "2,3,4,5,6,7,9,10,11,12,13,17", "endColumns": "59,69,70,71,57,57,10,63,55,58,62,10", "endOffsets": "110,180,251,323,381,439,548,612,668,727,790,962"}, "to": {"startLines": "10,11,12,13,14,15,16,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "611,671,741,812,884,942,1000,1109,1173,1229,1288,1351", "endLines": "10,11,12,13,14,15,17,18,19,20,21,25", "endColumns": "59,69,70,71,57,57,10,63,55,58,62,10", "endOffsets": "666,736,807,879,937,995,1104,1168,1224,1283,1346,1518"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-pl_values-pl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,3812", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,3890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,220,278,323,389,453,512,565", "endColumns": "117,46,57,44,65,63,58,52,67", "endOffsets": "168,215,273,318,384,448,507,560,628"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2917,3035,3082,3457,3502,3568,3632,3691,3744", "endColumns": "117,46,57,44,65,63,58,52,67", "endOffsets": "3030,3077,3135,3497,3563,3627,3686,3739,3807"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3895", "endColumns": "100", "endOffsets": "3991"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,102", "endOffsets": "150,249,364,467"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2817,3140,3239,3354", "endColumns": "99,98,114,102", "endOffsets": "2912,3234,3349,3452"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-v18_values-v18.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-v18\\values-v18.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "48", "endOffsets": "99"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-mn_values-mn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,3218", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,3294"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3299", "endColumns": "100", "endOffsets": "3395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,264,369", "endColumns": "104,103,104,106", "endOffsets": "155,259,364,471"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2797,2902,3006,3111", "endColumns": "104,103,104,106", "endOffsets": "2897,3001,3106,3213"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-w480dp-port-v13_values-w480dp-port-v13.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c95790bbae57615f21c79472117e61f\\transformed\\material-1.2.1\\res\\values-w480dp-port-v13\\values-w480dp-port-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,114,170,226,293,358,413,478", "endColumns": "58,55,55,66,64,54,64,68", "endOffsets": "109,165,221,288,353,408,473,542"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-en-rXC_values-en-rXC.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,256,455,667", "endColumns": "200,198,211,195", "endOffsets": "251,450,662,858"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "5528,5729,5928,6140", "endColumns": "200,198,211,195", "endOffsets": "5724,5923,6135,6331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "203", "endOffsets": "254"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "6522", "endColumns": "203", "endOffsets": "6721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,5528", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,5709"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,6336", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,6517"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-zh-rHK_values-zh-rHK.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,91", "endOffsets": "133,225,326,418"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2666,2749,2841,2942", "endColumns": "82,91,100,91", "endOffsets": "2744,2836,2937,3029"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3113", "endColumns": "100", "endOffsets": "3209"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,3034", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,3108"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-th_values-th.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,3144", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,3221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3226", "endColumns": "100", "endOffsets": "3322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,96", "endOffsets": "153,252,363,460"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2734,2837,2936,3047", "endColumns": "102,98,110,96", "endOffsets": "2832,2931,3042,3139"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-es-rUS_values-es-rUS.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,3252", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,3330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,266,381", "endColumns": "108,101,114,104", "endOffsets": "159,261,376,481"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2821,2930,3032,3147", "endColumns": "108,101,114,104", "endOffsets": "2925,3027,3142,3247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3335", "endColumns": "100", "endOffsets": "3431"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-hy_values-hy.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,3234", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,3312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,262,373", "endColumns": "103,102,110,101", "endOffsets": "154,257,368,470"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2814,2918,3021,3132", "endColumns": "103,102,110,101", "endOffsets": "2913,3016,3127,3229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3317", "endColumns": "100", "endOffsets": "3413"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-v16_values-v16.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "223"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "121", "endLines": "6", "endColumns": "12", "endOffsets": "289"}}]}, {"outputFile": "com.cadetvocab.quiz.app-merged_res-47:/values-nb_values-nb.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,95", "endOffsets": "160,261,373,469"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2737,3060,3161,3273", "endColumns": "109,100,111,95", "endOffsets": "2842,3156,3268,3364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,3733", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,3808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "3813", "endColumns": "100", "endOffsets": "3909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,211,268,312,378,442,515,568", "endColumns": "108,46,56,43,65,63,72,52,63", "endOffsets": "159,206,263,307,373,437,510,563,627"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2847,2956,3003,3369,3413,3479,3543,3616,3669", "endColumns": "108,46,56,43,65,63,72,52,63", "endOffsets": "2951,2998,3055,3408,3474,3538,3611,3664,3728"}}]}]}