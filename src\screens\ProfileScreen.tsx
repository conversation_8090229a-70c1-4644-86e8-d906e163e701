import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, StatusBar, Platform, ActivityIndicator, ScrollView } from 'react-native';
import { Text, Avatar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { colors } from '../theme/colors';
import { auth } from '../config/firebase';
import { RootStackParamList } from '../navigation/types';
import { userService, UserStats } from '../services/userService';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import ProtectedScreen from '../components/ProtectedScreen';

type ProfileScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const defaultStats: UserStats = {
  totalXP: 0,
  quizzesTaken: 0,
  accuracy: 0,
  wordsLearned: 0,
  correctAnswers: 0,
  totalQuestions: 0,
  streak: 0,
  lastQuizDate: null,
};

const createStyles = (insets: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.dark.darker,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight || 24 : 40,
    paddingBottom: 24,
    backgroundColor: colors.dark.medium,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    marginBottom: 24,
    marginHorizontal: 16,
  },
  avatar: {
    backgroundColor: colors.primary,
    marginBottom: 12,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 8,
  },
  rankBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.dark.dark,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    marginBottom: 20,
  },
  rankText: {
    color: colors.primary,
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    width: '100%',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    color: colors.text.primary,
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
  },
  statLabel: {
    color: colors.text.secondary,
    fontSize: 12,
    marginTop: 4,
  },
  actionsContainer: {
    paddingHorizontal: 16,
    paddingTop: 0,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  actionButtonText: {
    color: colors.text.primary,
    fontSize: 16,
    marginLeft: 16,
  },
  divider: {
    height: 1,
    backgroundColor: colors.dark.medium,
    marginVertical: 8,
    marginHorizontal: 16,
  },
  logoutContainer: {
    paddingHorizontal: 16,
    paddingBottom: Platform.OS === 'ios' ? insets.bottom + 16 : 24,
    paddingTop: 16,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFD700',
    paddingVertical: 16,
    borderRadius: 12,
  },
  logoutButtonText: {
    color: colors.status.error,
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  retryButton: {
    marginTop: 20,
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.text.onPrimary,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

const ProfileScreen = () => {
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const insets = useSafeAreaInsets();
  const [profileStats, setProfileStats] = useState<UserStats>(defaultStats);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);

  const styles = createStyles(insets);

  useEffect(() => {
    const checkAdminStatus = () => {
      const user = auth.currentUser;
      setIsAdmin(!!user?.email?.endsWith('@admin.com'));
    };
    checkAdminStatus();
  }, []);

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        const user = auth.currentUser;
        if (!user) {
          setError('User not authenticated');
          setLoading(false);
          return;
        }

        // First try to get the latest data from server
        const serverProfile = await userService.fetchUserProfileFromServer(user.uid);
        if (serverProfile?.stats) {
          setProfileStats(serverProfile.stats);
          // Update local cache with server data
          await userService.updateLocalUserProfile(user.uid, serverProfile);
      } else {
          // Fallback to local cache if server fetch fails
          const localProfile = await userService.getUserProfile(user.uid);
          if (localProfile?.stats) {
            setProfileStats(localProfile.stats);
          }
      }
    } catch (e: any) {
      console.error("Failed to fetch profile stats:", e);
      setError("Failed to load profile data. Please try again.");
    } finally {
      setLoading(false);
    }
    };

    fetchProfileData();
  }, []);

  const handleLogout = async () => {
    try {
      await auth.signOut();
      navigation.reset({
        index: 0,
        routes: [{ name: 'Main' }],
      });
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (loading) {
  return (
      <ProtectedScreen screenName="Profile">
        <View style={[styles.container, styles.centered]}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={{ marginTop: 10, color: colors.text.secondary }}>Loading profile...</Text>
        </View>
      </ProtectedScreen>
    );
  }

  if (error) {
    return (
      <ProtectedScreen screenName="Profile">
        <View style={[styles.container, styles.centered]}>
          <Icon name="alert-circle-outline" size={48} color={colors.status.error} />
          <Text style={{ marginTop: 10, color: colors.status.error, textAlign: 'center', paddingHorizontal: 20 }}>
            {error}
          </Text>
          <TouchableOpacity 
            style={styles.retryButton} 
            onPress={() => setLoading(true)}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </ProtectedScreen>
    );
  }

  const renderActionButtons = () => (
    <View style={styles.actionsContainer}>
      <TouchableOpacity
        style={styles.actionButton}
        onPress={() => navigation.navigate('EditProfile')}
      >
        <Icon name="account-edit" size={24} color={colors.text.primary} />
        <Text style={styles.actionButtonText}>Edit Profile</Text>
      </TouchableOpacity>

      <View style={styles.divider} />

      <TouchableOpacity
        style={styles.actionButton}
        onPress={() => navigation.navigate('ChangePassword')}
      >
        <Icon name="lock-reset" size={24} color={colors.text.primary} />
        <Text style={styles.actionButtonText}>Change Password</Text>
      </TouchableOpacity>

      {isAdmin && (
        <>
          <View style={styles.divider} />
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('Admin')}
          >
            <Icon name="shield-crown" size={24} color={colors.primary} />
            <Text style={[styles.actionButtonText, { color: colors.primary }]}>Admin Panel</Text>
          </TouchableOpacity>
        </>
      )}

      <View style={styles.divider} />

      <TouchableOpacity
        style={styles.actionButton}
        onPress={() => navigation.navigate('PrivacyPolicy')}
      >
        <Icon name="shield-check" size={24} color={colors.text.primary} />
        <Text style={styles.actionButtonText}>Privacy Policy</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <ProtectedScreen screenName="Profile">
        <View style={styles.container}>
          <StatusBar barStyle="light-content" />
          <ScrollView style={styles.scrollView}>
            {/* Header Section with Stats */}
            <View style={styles.header}>
              <Avatar.Text
                size={80}
                label={auth.currentUser?.displayName?.charAt(0)?.toUpperCase() || 'U'}
                style={styles.avatar}
              />
              <Text style={styles.userName}>
                {auth.currentUser?.displayName || 'User Name'}
              </Text>
            <Text style={styles.userEmail}>{auth.currentUser?.email}</Text>
              <View style={styles.rankBadge}>
                <Icon name="shield-star" size={16} color={colors.primary} />
                <Text style={styles.rankText}>Cadet</Text>
              </View>

              {/* Stats Section - Now part of header */}
              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <Icon name="star" size={24} color={colors.primary} />
                  <Text style={styles.statValue}>{profileStats.totalXP}</Text>
                  <Text style={styles.statLabel}>Total XP</Text>
                </View>
                <View style={styles.statItem}>
                  <Icon name="checkbox-marked-circle" size={24} color={colors.primary} />
                  <Text style={styles.statValue}>{profileStats.quizzesTaken}</Text>
                <Text style={styles.statLabel}>Quizzes Taken</Text>
                </View>
                <View style={styles.statItem}>
                  <Icon name="target" size={24} color={colors.primary} />
                <Text style={styles.statValue}>{profileStats.accuracy}%</Text>
                  <Text style={styles.statLabel}>Accuracy</Text>
                </View>
              </View>
            </View>

            {/* Actions Section */}
            {renderActionButtons()}
          </ScrollView>

          {/* Logout Button */}
          <View style={styles.logoutContainer}>
            <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
              <Icon name="logout" size={20} color={colors.status.error} />
              <Text style={styles.logoutButtonText}>Logout</Text>
            </TouchableOpacity>
          </View>
        </View>
    </ProtectedScreen>
  );
};

export default ProfileScreen; 