import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, StatusBar, Alert } from 'react-native';
import { TextInput, Button, Text, ActivityIndicator, Appbar } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { auth } from '../config/firebase';
import { userService } from '../services/userService';
import { RootStackParamList } from '../types';
import { colors } from '../theme/colors';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

type EditProfileScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'EditProfile'>;

const EditProfileScreen = () => {
  const navigation = useNavigation<EditProfileScreenNavigationProp>();
  const [displayName, setDisplayName] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadUserData = async () => {
      setLoading(true);
      setError(null);
      const user = auth.currentUser;
      if (user) {
        setDisplayName(user.displayName || '');
        setEmail(user.email || '');
      } else {
        setError('User not authenticated. Please re-login.');
      }
      setLoading(false);
    };
    loadUserData();
  }, []);

  const handleSaveChanges = async () => {
    if (!displayName.trim()) {
      Alert.alert('Validation Error', 'Display name cannot be empty.');
      return;
    }

    setIsSaving(true);
    setError(null);
    try {
      const user = auth.currentUser;
      if (user) {
        await userService.updateUserProfile(user.uid, { displayName });
        Alert.alert('Success', 'Profile updated successfully!');
        if (auth.currentUser && auth.currentUser.displayName !== displayName) {
        }
        navigation.goBack();
      } else {
        throw new Error('User not authenticated.');
      }
    } catch (e: any) {
      console.error('Failed to save profile:', e);
      const errorMessage = e.message || 'Failed to save profile. Please try again.';
      setError(errorMessage);
      Alert.alert('Error', errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.centeredContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={{ marginTop: 10, color: colors.text.secondary }}>Loading profile...</Text>
      </View>
    );
  }
  
  if (error && !isSaving && !loading) {
     return (
      <View style={styles.centeredContainer}>
        <Appbar.Header style={{ backgroundColor: colors.primary, width: '100%' }}>
          <Appbar.BackAction onPress={() => navigation.goBack()} color={colors.text.onPrimary}/>
          <Appbar.Content title="Edit Profile" titleStyle={{ color: colors.text.onPrimary }} />
        </Appbar.Header>
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <Icon name="alert-circle-outline" size={48} color={colors.status.error} /> 
          <Text style={styles.errorText}>{error}</Text>
          <Button onPress={() => navigation.goBack()}>Go Back</Button>
        </View>
      </View>
    );
  }

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary} />
      <Appbar.Header style={{ backgroundColor: colors.primary }}>
        <Appbar.BackAction onPress={() => navigation.goBack()} color={colors.text.onPrimary}/>
        <Appbar.Content title="Edit Profile" titleStyle={{ color: colors.text.onPrimary }} />
      </Appbar.Header>
      <ScrollView style={styles.screenContainer} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.title}>Update Your Profile</Text>
        
        {error && isSaving && <Text style={styles.errorTextOnSave}>{error}</Text>}

        <TextInput
          label="Display Name"
          value={displayName}
          onChangeText={setDisplayName}
          mode="outlined"
          style={styles.input}
          disabled={isSaving}
          theme={{ colors: { primary: colors.primary } }}
        />

        <Button 
          mode="contained" 
          onPress={handleSaveChanges} 
          style={styles.button}
          loading={isSaving}
          disabled={isSaving || loading}
          labelStyle={{ color: colors.text.onPrimary }}
        >
          {isSaving ? 'Saving...' : 'Save Changes'}
        </Button>
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: colors.secondary,
  },
  contentContainer: {
    padding: 20,
    flexGrow: 1,
  },
  centeredContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: colors.secondary,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    marginBottom: 16,
    backgroundColor: colors.dark.medium,
  },
  button: {
    marginTop: 20,
    paddingVertical: 8,
    backgroundColor: colors.primary,
  },
  errorText: {
    color: colors.status.error,
    marginTop: 10,
    marginBottom: 10,
    textAlign: 'center',
    fontSize: 16,
  },
  errorTextOnSave: {
    color: colors.status.error,
    marginBottom: 15,
    textAlign: 'center',
    backgroundColor: colors.dark.light,
    padding: 10,
    borderRadius: 4,
    fontSize: 14,
  },
});

export default EditProfileScreen; 