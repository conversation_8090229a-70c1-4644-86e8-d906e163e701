import { useState, useEffect } from 'react';
import { getAuth } from 'firebase/auth';
import { getFirestore, collection, getDocs, query, where, doc, setDoc } from 'firebase/firestore';
import { cacheData, getCachedData, CACHE_KEYS } from '../utils/cache';

interface QuizProgress {
  quizId: string;
  score: number;
  completedAt: string;
  totalQuestions: number;
  correctAnswers: number;
}

interface UserProgress {
  totalScore: number;
  quizzesTaken: number;
  history: QuizProgress[];
}

export const useProgressCache = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState<UserProgress | null>(null);
  const auth = getAuth();
  const db = getFirestore();

  const fetchAndCacheProgress = async () => {
    try {
      setLoading(true);
      setError(null);

      const userId = auth.currentUser?.uid;
      if (!userId) {
        setProgress(null);
        setLoading(false);
        return;
      }

      // Try to get cached progress first
      const cachedProgress = await getCachedData(CACHE_KEYS.USER_PROGRESS, userId);
      if (cachedProgress) {
        console.log('[useProgressCache] CACHE HIT: Returning user progress from local cache.');
        setProgress(cachedProgress);
        setLoading(false);
        return;
      }

      // If no cache, fetch from Firestore
      console.log('[useProgressCache] CACHE MISS: Fetching user progress from Firestore... (COST: 1 read)');
      const progressRef = collection(db, 'userProgress');
      const q = query(progressRef, where('userId', '==', userId));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        const initialProgress: UserProgress = {
          totalScore: 0,
          quizzesTaken: 0,
          history: [],
        };
        console.log('[useProgressCache] No progress found on server, initializing locally.');
        await cacheData(CACHE_KEYS.USER_PROGRESS, initialProgress, userId);
        setProgress(initialProgress);
        return;
      }

      const progressDoc = querySnapshot.docs[0];
      const progressData = progressDoc.data() as UserProgress;

      // Cache the fetched data
      console.log('[useProgressCache] Stored fresh user progress in local cache.');
      await cacheData(CACHE_KEYS.USER_PROGRESS, progressData, userId);
      
      setProgress(progressData);

      // Update or create Firestore document in background (no await)
      (async () => {
        try {
          // We store progress in a document keyed by the user's ID for simplicity
          console.log('[useProgressCache] Writing user progress to Firestore... (COST: 1 write)');
          const docRef = doc(db, 'userProgress', userId);

          // Merge with existing data if present
          await setDoc(
            docRef,
            {
              userId,
              ...progressData,
            },
            { merge: true }
          );
        } catch (err) {
          console.warn('[useProgressCache] Failed to update Firestore progress:', err);
        }
      })();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load progress');
      console.error('Error fetching progress:', err);
    } finally {
      setLoading(false);
    }
  };

  const updateProgress = async (newQuizProgress: QuizProgress) => {
    try {
      const userId = auth.currentUser?.uid;
      if (!userId) return;

      const currentProgress = progress || {
        totalScore: 0,
        quizzesTaken: 0,
        history: [],
      };

      const updatedProgress: UserProgress = {
        totalScore: currentProgress.totalScore + newQuizProgress.score,
        quizzesTaken: currentProgress.quizzesTaken + 1,
        history: [newQuizProgress, ...currentProgress.history],
      };

      // Update cache immediately
      await cacheData(CACHE_KEYS.USER_PROGRESS, updatedProgress, userId);
      setProgress(updatedProgress);

      // Update or create Firestore document in background (no await)
      (async () => {
        try {
          // We store progress in a document keyed by the user's ID for simplicity
          console.log('[useProgressCache] Writing user progress to Firestore... (COST: 1 write)');
          const docRef = doc(db, 'userProgress', userId);

          // Merge with existing data if present
          await setDoc(
            docRef,
            {
              userId,
              ...updatedProgress,
            },
            { merge: true }
          );
        } catch (err) {
          console.warn('[useProgressCache] Failed to update Firestore progress:', err);
        }
      })();
    } catch (err) {
      console.error('Error updating progress:', err);
    }
  };

  useEffect(() => {
    fetchAndCacheProgress();

    // Subscribe to auth state changes
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) {
        fetchAndCacheProgress();
      } else {
        setProgress(null);
      }
    });

    return () => unsubscribe();
  }, []);

  return {
    loading,
    error,
    progress,
    updateProgress,
    refreshProgress: fetchAndCacheProgress,
  };
}; 