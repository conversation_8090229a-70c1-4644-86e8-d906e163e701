import React, { useEffect, useState } from 'react';
import { Provider as PaperProvider, MD3LightTheme, ActivityIndicator } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { View, Text, LogBox } from 'react-native';
import { initializeApp } from 'firebase/app';
import { getAuth, User } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { firebaseConfig } from './src/config/firebase';
import { colors } from './src/theme/colors';
import AppNavigator from './src/navigation/AppNavigator';
import { sessionService } from './src/services/sessionService';
import { networkService } from './src/services/networkService';

// Ignore specific warnings
LogBox.ignoreLogs([
  'AsyncStorage has been extracted from react-native core',
  'Setting a timer for a long period of time',
]);

const theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: colors.primary,
    secondary: colors.secondary,
    background: colors.secondary,
    surface: colors.dark.medium,
    surfaceVariant: colors.dark.dark,
    error: colors.status.error,
    onSurface: colors.text.primary,
    onSurfaceVariant: colors.text.secondary,
    onPrimary: colors.text.onPrimary,
  },
};

export default function App() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    const initializeServices = async () => {
      try {
        // Initialize Firebase
        const app = initializeApp(firebaseConfig);

        // Initialize Auth (Firebase won't persist between launches on RN; we handle via sessionService)
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Initialize session management
        const savedUser = await sessionService.initializeSession();
        if (savedUser) {
          setUser(savedUser);
        }

        // Set up auth state listener with session management
        const unsubscribe = sessionService.setupAuthStateListener((user) => {
          setUser(user);
        });

        // Initialize network monitoring
        await networkService.getNetworkState();

        setIsInitialized(true);

        // Cleanup auth listener on unmount
        return () => unsubscribe();
      } catch (err: any) {
        console.error('Initialization error:', err.message);
        setError(err.message || 'Failed to initialize app');
      }
    };

    initializeServices();
  }, []);

  if (error) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20, backgroundColor: colors.secondary }}>
        <Text style={{ color: colors.status.error, fontSize: 18, textAlign: 'center', marginBottom: 20 }}>App Initialization Failed</Text>
        <Text style={{ color: colors.text.primary, textAlign: 'center' }}>{error}</Text>
      </View>
    );
  }

  if (!isInitialized) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.secondary }}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={{ color: colors.text.primary, marginTop: 10 }}>Loading...</Text>
      </View>
    );
  }

  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <AppNavigator />
      </PaperProvider>
    </SafeAreaProvider>
  );
} 