/**
 * <PERSON><PERSON><PERSON> to initialize quiz data in Firebase with type selection
 *
 * Usage:
 * - npm run init-quiz-data Hunter
 * - npm run init-quiz-data Romeo
 * - npm run init-quiz-data Juliet
 *
 * Or run directly:
 * - npx ts-node src/scripts/runInitializeQuizData.ts Hunter
 */

import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { firebaseConfig } from '../config/firebase';
import {
  initializeQuizDataWithType,
  initializeHunterQuizzes,
  initializeRomeoQuizzes,
  initializeJulietQuizzes
} from './initializeQuizData';

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function runInitialization() {
  try {
    // Get type from command line arguments
    const args = process.argv.slice(2);
    const requestedType = args[0]?.toLowerCase();

    console.log('🚀 Starting quiz data initialization...');
    console.log('📋 Available types: <PERSON>, <PERSON>, <PERSON>');

    let result;

    switch (requestedType) {
      case 'hunter':
        console.log('🎯 Initializing Hunter quiz sets...');
        result = await initializeHunterQuizzes();
        break;

      case 'romeo':
        console.log('💕 Initializing Romeo quiz sets...');
        result = await initializeRomeoQuizzes();
        break;

      case 'juliet':
        console.log('🌹 Initializing Juliet quiz sets...');
        result = await initializeJulietQuizzes();
        break;

      default:
        console.log('⚠️ No valid type specified. Available options:');
        console.log('   - Hunter: npx ts-node src/scripts/runInitializeQuizData.ts Hunter');
        console.log('   - Romeo:  npx ts-node src/scripts/runInitializeQuizData.ts Romeo');
        console.log('   - Juliet: npx ts-node src/scripts/runInitializeQuizData.ts Juliet');
        console.log('');
        console.log('🎯 Defaulting to Hunter type...');
        result = await initializeHunterQuizzes();
        break;
    }

    console.log('✅ Quiz data initialization completed successfully!');
    console.log(`📊 Summary: ${result.count} quiz sets added as type "${result.type}"`);
    process.exit(0);
  } catch (error) {
    console.error('❌ Quiz data initialization failed:', error);
    process.exit(1);
  }
}

// Run the initialization
runInitialization();
