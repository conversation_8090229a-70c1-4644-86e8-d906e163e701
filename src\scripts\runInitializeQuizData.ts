/**
 * <PERSON><PERSON><PERSON> to initialize quiz data in Firebase
 * Run this to upload the local quiz data to Firestore
 */

import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { firebaseConfig } from '../config/firebase';
import { initializeQuizData } from './initializeQuizData';

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function runInitialization() {
  try {
    console.log('🚀 Starting quiz data initialization...');
    await initializeQuizData();
    console.log('✅ Quiz data initialization completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Quiz data initialization failed:', error);
    process.exit(1);
  }
}

// Run the initialization
runInitialization();
