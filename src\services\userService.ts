import { doc, getDoc, updateDoc, setDoc, arrayUnion } from 'firebase/firestore';
import { db, auth } from '../config/firebase';
import { updateProfile as firebaseUpdateProfile } from 'firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';

const USER_PROFILE_CACHE_KEY = '@user_profile_cache';

export interface UserSettings {
  quizTimer: boolean;
  difficulty: string;
  soundEffects: boolean;
  darkMode: boolean;
  textSize: number;
  dailyReminders: boolean;
  achievementAlerts: boolean;
}

export interface UserStats {
  wordsLearned: number;
  quizzesTaken: number;
  correctAnswers: number;
  totalQuestions: number;
  accuracy: number;
  totalXP: number;
  streak: number;
  lastQuizDate: string | null;
  completedDailyChallenges?: string[]; // Array of dates "YYYY-MM-DD"
}

export interface UserProfile {
  id: string;
  displayName: string;
  email: string;
  settings: UserSettings;
  stats: UserStats;
  createdAt: string;
  updatedAt: string;
  photoURL?: string;
}

export const userService = {
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      // First try to get from local cache
      const cachedProfile = await AsyncStorage.getItem(USER_PROFILE_CACHE_KEY);
      if (cachedProfile) {
        return JSON.parse(cachedProfile);
      }

      // If no cache, try to get from Firestore
      console.log(`[userService] Reading user profile... (COST: 1 read)`);
      const docRef = doc(db, 'users', userId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data();
        const profile = {
          id: docSnap.id,
          displayName: data.displayName || '',
          email: data.email || '',
          photoURL: data.photoURL || undefined,
          settings: data.settings || {
            quizTimer: true,
            difficulty: 'medium',
            soundEffects: true,
            darkMode: false,
            textSize: 16,
            dailyReminders: true,
            achievementAlerts: true,
          },
          stats: data.stats || {
            wordsLearned: 0,
            quizzesTaken: 0,
            correctAnswers: 0,
            totalQuestions: 0,
            accuracy: 0,
            totalXP: 0,
            streak: 0,
            lastQuizDate: null,
          },
          createdAt: data.createdAt || new Date().toISOString(),
          updatedAt: data.updatedAt || new Date().toISOString(),
        };

        // Cache the profile
        await AsyncStorage.setItem(USER_PROFILE_CACHE_KEY, JSON.stringify(profile));
        return profile;
      }
      return null;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      // If error (likely offline), try to get from cache
      const cachedProfile = await AsyncStorage.getItem(USER_PROFILE_CACHE_KEY);
      if (cachedProfile) {
        return JSON.parse(cachedProfile);
      }
      throw error;
    }
  },

  async updateUserSettings(userId: string, settings: Partial<UserSettings>): Promise<void> {
    try {
      console.log(`[userService] Writing user settings... (COST: 1 write)`);
      const docRef = doc(db, 'users', userId);
      await updateDoc(docRef, { settings });
    } catch (error) {
      console.error('Error updating user settings:', error);
      throw error;
    }
  },

  async getUserStats(userId: string): Promise<UserStats> {
    try {
      console.log(`[userService] Reading user stats... (COST: 1 read)`);
      const userDoc = await getDoc(doc(db, 'users', userId));
      if (!userDoc.exists()) {
        // Initialize stats for new user
        const initialStats: UserStats = {
          wordsLearned: 0,
          quizzesTaken: 0,
          correctAnswers: 0,
          totalQuestions: 0,
          accuracy: 0,
          totalXP: 0,
          streak: 0,
          lastQuizDate: null,
        };
        // When initializing stats, also ensure the user document itself exists if it doesn't
        // This might be better handled during user creation (signup)
        console.log(`[userService] User doc not found. Writing initial stats... (COST: 1 write)`);
        await setDoc(doc(db, 'users', userId), { stats: initialStats }, { merge: true });
        return initialStats;
      }
      const stats = userDoc.data().stats;
      if (!stats) { // Handle case where stats field might be missing on an existing document
        const initialStats: UserStats = {
            wordsLearned: 0, quizzesTaken: 0, correctAnswers: 0, totalQuestions: 0,
            accuracy: 0, totalXP: 0, streak: 0, lastQuizDate: null,
        };
        console.log(`[userService] Stats field missing. Writing initial stats... (COST: 1 write)`);
        await updateDoc(doc(db, 'users', userId), { stats: initialStats });
        return initialStats;
      }
      return stats;
    } catch (error) {
      console.error('Error getting user stats:', error);
      throw error;
    }
  },

  async updateQuizStats(userId: string, quizData: {
    score: number;
    totalQuestions: number;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    quizSetId: string;
  }): Promise<void> {
    try {
      // Get current profile from cache or server
      const currentProfile = await this.getUserProfile(userId);
      if (!currentProfile) throw new Error('User profile not found');
      
      const today = new Date();
      const xpMultiplier = {
        beginner: 5,
        intermediate: 10,
        advanced: 15
      };
      const xpEarned = quizData.score * xpMultiplier[quizData.difficulty];

      const currentStats = currentProfile.stats;
      const lastQuizDate = currentStats.lastQuizDate ? new Date(currentStats.lastQuizDate) : new Date(0);
      const daysSinceLastQuiz = Math.floor((today.getTime() - lastQuizDate.getTime()) / (1000 * 60 * 60 * 24));
      
      let newStreak = currentStats.streak || 0;
      if (daysSinceLastQuiz === 1) {
        newStreak++;
      } else if (daysSinceLastQuiz > 1) {
        newStreak = 1; // Reset streak
      } else if (daysSinceLastQuiz === 0 && currentStats.quizzesTaken > 0) {
        // Same day, streak doesn't change unless it's the very first quiz
        if (newStreak === 0) newStreak = 1;
      } else if (newStreak === 0) {
        newStreak = 1;
      }
      
      const totalCorrect = (currentStats.correctAnswers || 0) + quizData.score;
      const totalAnsweredQuestions = (currentStats.totalQuestions || 0) + quizData.totalQuestions;
      const newAccuracy = totalAnsweredQuestions > 0 ? Math.round((totalCorrect / totalAnsweredQuestions) * 100) : 0;

      const updatedStats: UserStats = {
        ...currentStats,
        wordsLearned: (currentStats.wordsLearned || 0) + quizData.totalQuestions,
        quizzesTaken: (currentStats.quizzesTaken || 0) + 1,
        correctAnswers: totalCorrect,
        totalQuestions: totalAnsweredQuestions,
        accuracy: newAccuracy,
        totalXP: (currentStats.totalXP || 0) + xpEarned,
        streak: newStreak,
        lastQuizDate: today.toISOString()
      };

      // Update cache first
      const updatedProfile = {
        ...currentProfile,
        stats: updatedStats,
        updatedAt: today.toISOString()
      };
      await AsyncStorage.setItem(USER_PROFILE_CACHE_KEY, JSON.stringify(updatedProfile));

      // Then update server
      try {
        console.log(`[userService] Writing updated stats... (COST: 1 write)`);
        const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        stats: updatedStats,
        updatedAt: today.toISOString()
      });
      } catch (error) {
        console.error('Failed to update server, but cache is updated:', error);
      }
    } catch (error) {
      console.error('Error updating user stats:', error);
      throw error;
    }
  },

  async updateUserProfile(userId: string, profileData: {
    displayName?: string;
    photoURL?: string; 
  }): Promise<void> {
    const currentUser = auth.currentUser;
    if (!currentUser || currentUser.uid !== userId) {
      throw new Error("User not authenticated or mismatch.");
    }

    const updates: { displayName?: string; photoURL?: string } = {};
    if (profileData.displayName !== undefined) {
      updates.displayName = profileData.displayName;
    }
    if (profileData.photoURL !== undefined) {
      updates.photoURL = profileData.photoURL;
    }

    try {
      // 1. Update Firebase Auth profile
      if (Object.keys(updates).length > 0) {
        await firebaseUpdateProfile(currentUser, updates);
      }

      // 2. Update Firestore user document (top-level fields)
      // We only update fields that were actually passed in profileData
      const firestoreUpdates: Partial<UserProfile> = {};
      if (profileData.displayName !== undefined) {
        firestoreUpdates.displayName = profileData.displayName;
      }
      if (profileData.photoURL !== undefined) {
        firestoreUpdates.photoURL = profileData.photoURL;
      } 
      // Always update the 'updatedAt' timestamp
      firestoreUpdates.updatedAt = new Date().toISOString();
      
      if (Object.keys(firestoreUpdates).length > 1) { // if more than just updatedAt
        const userRef = doc(db, 'users', userId);
        console.log(`[userService] Writing user profile updates to Firestore... (COST: 1 write)`);
        await updateDoc(userRef, firestoreUpdates);
      }

    } catch (error) {
      console.error('Error updating user profile:', error);
      // Consider more specific error handling or re-throwing a custom error
      throw error;
    }
  },
  
  // Placeholder for changePassword if needed later
  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    try {
      // TODO: Implement password change logic using Firebase Auth
      // Example: reauthenticate with currentPassword, then updatePassword
      console.log("Password change for", userId, currentPassword, newPassword); // Placeholder
      throw new Error("Password change not implemented yet.");
    } catch (error) {
      console.error('Error changing password:', error);
      throw error;
    }
  },

  // Fetch user profile directly from server
  async fetchUserProfileFromServer(userId: string): Promise<UserProfile | null> {
    try {
      console.log(`[userService] Reading user profile from server... (COST: 1 read)`);
      const docRef = doc(db, 'users', userId);
      const docSnap = await getDoc(docRef);
      if (docSnap.exists()) {
        const data = docSnap.data();
        const profile: UserProfile = {
          id: docSnap.id,
          displayName: data.displayName || '',
          email: data.email || '',
          photoURL: data.photoURL || undefined,
          settings: data.settings || {
            quizTimer: true,
            difficulty: 'medium',
            soundEffects: true,
            darkMode: false,
            textSize: 16,
            dailyReminders: true,
            achievementAlerts: true,
          },
          stats: {
            wordsLearned: data.stats?.wordsLearned || 0,
            quizzesTaken: data.stats?.quizzesTaken || 0,
            correctAnswers: data.stats?.correctAnswers || 0,
            totalQuestions: data.stats?.totalQuestions || 0,
            accuracy: data.stats?.accuracy || 0,
            totalXP: data.stats?.totalXP || 0,
            streak: data.stats?.streak || 0,
            lastQuizDate: data.stats?.lastQuizDate || null,
            completedDailyChallenges: data.stats?.completedDailyChallenges || [],
          },
          createdAt: data.createdAt || new Date().toISOString(),
          updatedAt: data.updatedAt || new Date().toISOString(),
        };
        return profile;
      }
      return null;
    } catch (error) {
      console.error('Error fetching user profile from server:', error);
      return null;
    }
  },

  // Update local cache with server data
  async updateLocalUserProfile(userId: string, profileData: UserProfile): Promise<void> {
    try {
      await AsyncStorage.setItem(USER_PROFILE_CACHE_KEY, JSON.stringify(profileData));
      console.log(`[userService] Updated local user profile cache for user ${userId}.`);
    } catch (error) {
      console.error('Error updating local user profile:', error);
    }
  },

  /**
   * Adds a date to the list of completed daily challenges for a user.
   */
  async addCompletedDailyChallenge(userId: string, date: string): Promise<void> {
    try {
      const userRef = doc(db, 'users', userId);
      console.log(`[userService] Writing completed daily challenge for date ${date}... (COST: 1 write)`);
      await updateDoc(userRef, {
        'stats.completedDailyChallenges': arrayUnion(date)
      });
    } catch (error) {
      console.error(`Failed to add completed daily challenge for user ${userId}:`, error);
      throw error;
    }
  },

  async updateUserStats(userId: string, stats: Partial<UserStats>): Promise<void> {
    try {
      // Get current profile from cache or server
      const currentProfile = await this.getUserProfile(userId);
      if (!currentProfile) throw new Error('User profile not found');

      // Update stats
      const updatedStats = {
        ...currentProfile.stats,
        ...stats,
        // Ensure accuracy is calculated consistently
        accuracy: stats.totalQuestions && stats.correctAnswers 
          ? Math.round((stats.correctAnswers / stats.totalQuestions) * 100)
          : currentProfile.stats.accuracy
      };

      // Update cache first
      const updatedProfile = {
        ...currentProfile,
        stats: updatedStats,
        updatedAt: new Date().toISOString()
      };
      await AsyncStorage.setItem(USER_PROFILE_CACHE_KEY, JSON.stringify(updatedProfile));

      // Then try to update server
      try {
        console.log(`[userService] Writing user stats... (COST: 1 write)`);
        const docRef = doc(db, 'users', userId);
        await updateDoc(docRef, { 
          stats: updatedStats,
          updatedAt: new Date().toISOString()
        });
      } catch (error) {
        console.error('Failed to update server, but cache is updated:', error);
      }
    } catch (error) {
      console.error('Error updating user stats:', error);
      throw error;
    }
  },

  async syncOfflineData(userId: string): Promise<void> {
    try {
      const cachedProfile = await AsyncStorage.getItem(USER_PROFILE_CACHE_KEY);
      if (!cachedProfile) return;

      const profile = JSON.parse(cachedProfile);
      const docRef = doc(db, 'users', userId);
      await updateDoc(docRef, {
        stats: profile.stats,
        settings: profile.settings,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error syncing offline data:', error);
    }
  }
}; 