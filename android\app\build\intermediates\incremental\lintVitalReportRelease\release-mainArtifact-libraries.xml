<libraries>
  <library
      name="D:\CadetVocab - Copy\android@@:react-native-community_slider::release"
      jars="D:\CadetVocab - Copy\node_modules\@react-native-community\slider\android\build\.transforms\48373315518b5111d702e40f50956d0c\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\@react-native-community\slider\android\build\.transforms\48373315518b5111d702e40f50956d0c\transformed\out\jars\libs\R.jar"
      resolved="CadetVocab:react-native-community_slider:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\@react-native-community\slider\android\build\.transforms\48373315518b5111d702e40f50956d0c\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.react:react-android:0.72.6:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4115d94f0331b651ff9a931638b09a6b\transformed\jetified-react-android-0.72.6-release\jars\classes.jar"
      resolved="com.facebook.react:react-android:0.72.6"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4115d94f0331b651ff9a931638b09a6b\transformed\jetified-react-android-0.72.6-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fresco:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\156cc80b8e827be23a6f83eedea3693c\transformed\jetified-fresco-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:fresco:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\156cc80b8e827be23a6f83eedea3693c\transformed\jetified-fresco-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-okhttp3:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\708563591f53d5a31de1906d830ad0e4\transformed\jetified-imagepipeline-okhttp3-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-okhttp3:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\708563591f53d5a31de1906d830ad0e4\transformed\jetified-imagepipeline-okhttp3-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-gif:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\42ea4c6e1a66a113079b1b4d69f83f54\transformed\jetified-animated-gif-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-gif:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\42ea4c6e1a66a113079b1b4d69f83f54\transformed\jetified-animated-gif-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:webpsupport:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fa0420fa807ca9e64936073561133e90\transformed\jetified-webpsupport-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:webpsupport:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fa0420fa807ca9e64936073561133e90\transformed\jetified-webpsupport-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.react:hermes-android:0.72.6:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7b52d1ae4d5c74a25ca6959d5bf0b4e2\transformed\jetified-hermes-android-0.72.6-release\jars\classes.jar"
      resolved="com.facebook.react:hermes-android:0.72.6"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7b52d1ae4d5c74a25ca6959d5bf0b4e2\transformed\jetified-hermes-android-0.72.6-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:react-native-async-storage_async-storage::release"
      jars="D:\CadetVocab - Copy\node_modules\@react-native-async-storage\async-storage\android\build\.transforms\13ec938c787348e0d9b932c9b9bbe185\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\@react-native-async-storage\async-storage\android\build\.transforms\13ec938c787348e0d9b932c9b9bbe185\transformed\out\jars\libs\R.jar"
      resolved="CadetVocab:react-native-async-storage_async-storage:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\@react-native-async-storage\async-storage\android\build\.transforms\13ec938c787348e0d9b932c9b9bbe185\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:react-native-community_netinfo::release"
      jars="D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\.transforms\f101b691fe33d3da4fbb860e322f132a\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\.transforms\f101b691fe33d3da4fbb860e322f132a\transformed\out\jars\libs\R.jar"
      resolved="CadetVocab:react-native-community_netinfo:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\.transforms\f101b691fe33d3da4fbb860e322f132a\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo::release"
      jars="D:\CadetVocab - Copy\node_modules\expo\android\build\.transforms\69a70383df6acc9c9f0458884e670954\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo\android\build\.transforms\69a70383df6acc9c9f0458884e670954\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo\android\build\.transforms\69a70383df6acc9c9f0458884e670954\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:react-native-safe-area-context::release"
      jars="D:\CadetVocab - Copy\node_modules\react-native-safe-area-context\android\build\.transforms\2612c1bb10b80d0443b52ab2c5e8aba5\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\react-native-safe-area-context\android\build\.transforms\2612c1bb10b80d0443b52ab2c5e8aba5\transformed\out\jars\libs\R.jar"
      resolved="CadetVocab:react-native-safe-area-context:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\react-native-safe-area-context\android\build\.transforms\2612c1bb10b80d0443b52ab2c5e8aba5\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:react-native-screens::release"
      jars="D:\CadetVocab - Copy\node_modules\react-native-screens\android\build\.transforms\bbf5c9ff310cc48feb78cf8c96bc83b2\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\react-native-screens\android\build\.transforms\bbf5c9ff310cc48feb78cf8c96bc83b2\transformed\out\jars\libs\R.jar"
      resolved="CadetVocab:react-native-screens:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\react-native-screens\android\build\.transforms\bbf5c9ff310cc48feb78cf8c96bc83b2\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:react-native-vector-icons::release"
      jars="D:\CadetVocab - Copy\node_modules\react-native-vector-icons\android\build\.transforms\19acadb60b0708b228c12010889641bd\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\react-native-vector-icons\android\build\.transforms\19acadb60b0708b228c12010889641bd\transformed\out\jars\libs\R.jar"
      resolved="CadetVocab:react-native-vector-icons:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\react-native-vector-icons\android\build\.transforms\19acadb60b0708b228c12010889641bd\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-dev-launcher::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-dev-launcher\android\build\.transforms\bb1a8b676966dab2a2c11b884639b864\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-dev-launcher\android\build\.transforms\bb1a8b676966dab2a2c11b884639b864\transformed\out\jars\libs\R.jar"
      resolved="CadetVocab:expo-dev-launcher:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-dev-launcher\android\build\.transforms\bb1a8b676966dab2a2c11b884639b864\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-dev-menu::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\.transforms\cbc331469b309586bd9b35a41cca682e\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\.transforms\cbc331469b309586bd9b35a41cca682e\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-dev-menu:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\.transforms\cbc331469b309586bd9b35a41cca682e\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\8fc775a10259692585e5f093b80f9883\transformed\appcompat-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\8fc775a10259692585e5f093b80f9883\transformed\appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\bfb57a730389e05991612d1bc8a291e2\transformed\jetified-appcompat-resources-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\bfb57a730389e05991612d1bc8a291e2\transformed\jetified-appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-file-system::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\.transforms\f0b858f0f784f87353595c9a259b23dd\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\.transforms\f0b858f0f784f87353595c9a259b23dd\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-file-system:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\.transforms\f0b858f0f784f87353595c9a259b23dd\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\d0a654646302f385221d786cebcdebb3\transformed\legacy-support-v4-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\d0a654646302f385221d786cebcdebb3\transformed\legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\5f58386d8ce2cbfe1eff192583b03fb0\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\5f58386d8ce2cbfe1eff192583b03fb0\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\3125dda4c537b5379be4b8059f12fd05\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\3125dda4c537b5379be4b8059f12fd05\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-constants::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-constants\android\build\.transforms\8a9d6366890a7414edbd59e71fa23052\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-constants\android\build\.transforms\8a9d6366890a7414edbd59e71fa23052\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-constants:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-constants\android\build\.transforms\8a9d6366890a7414edbd59e71fa23052\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-image-loader::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-image-loader\android\build\.transforms\f2113fc689ff7d53ee17c70f2f7a5e6a\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-image-loader\android\build\.transforms\f2113fc689ff7d53ee17c70f2f7a5e6a\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-image-loader:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-image-loader\android\build\.transforms\f2113fc689ff7d53ee17c70f2f7a5e6a\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.13.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\0a43de56c5261a993837e8c0dbd0df27\transformed\jetified-glide-4.13.2\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.13.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\0a43de56c5261a993837e8c0dbd0df27\transformed\jetified-glide-4.13.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\a618a4ec6e9a0f031159e531016d8bad\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\a618a4ec6e9a0f031159e531016d8bad\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\27abba2b290b4a6f70e4bbb29befb79f\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\27abba2b290b4a6f70e4bbb29befb79f\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7f52e543fca820718464f2c82e71274f\transformed\lifecycle-extensions-2.2.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-extensions:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7f52e543fca820718464f2c82e71274f\transformed\lifecycle-extensions-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.5.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\3fc7415478e4731dd9355d61543956b8\transformed\fragment-1.5.7\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.5.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\3fc7415478e4731dd9355d61543956b8\transformed\fragment-1.5.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4dfec2d3282dc05e74257e73dac5bcbd\transformed\jetified-activity-1.7.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4dfec2d3282dc05e74257e73dac5bcbd\transformed\jetified-activity-1.7.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\5548385058946391f436f92302dcc195\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\5548385058946391f436f92302dcc195\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-modules-core::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\.transforms\1cbfac2b932dccc4cd0b87d42d70e9f8\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\.transforms\1cbfac2b932dccc4cd0b87d42d70e9f8\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-modules-core:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\.transforms\1cbfac2b932dccc4cd0b87d42d70e9f8\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\388d7a49ace0c78216797aa6078fd027\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\388d7a49ace0c78216797aa6078fd027\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\680cc59621be334d9f131292c916b416\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\680cc59621be334d9f131292c916b416\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\8bdf6b0bf865be6d537eba5d61fa0e42\transformed\jetified-lifecycle-service-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\8bdf6b0bf865be6d537eba5d61fa0e42\transformed\jetified-lifecycle-service-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7762cadebcbb43f1c3c494b3fca017db\transformed\lifecycle-livedata-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7762cadebcbb43f1c3c494b3fca017db\transformed\lifecycle-livedata-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.1\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.1"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\eb4dbdbd7c7cbc5881d361f54e0425c1\transformed\lifecycle-livedata-core-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\eb4dbdbd7c7cbc5881d361f54e0425c1\transformed\lifecycle-livedata-core-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\49389fe05353e9e81c6c6ad561e8629c\transformed\lifecycle-viewmodel-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\49389fe05353e9e81c6c6ad561e8629c\transformed\lifecycle-viewmodel-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\74f887f2d992a24d7a2a9bb57befd934\transformed\lifecycle-runtime-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\74f887f2d992a24d7a2a9bb57befd934\transformed\lifecycle-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\356767d41a07ee2a91639a8f928dc465\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\356767d41a07ee2a91639a8f928dc465\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\9a1d09347030c2d407badbecd54e1c2a\transformed\jetified-core-ktx-1.9.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\9a1d09347030c2d407badbecd54e1c2a\transformed\jetified-core-ktx-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\6062a715f5a47472ef2162d3759bcdd0\transformed\browser-1.2.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\6062a715f5a47472ef2162d3759bcdd0\transformed\browser-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\446db2806f88813e5f9b4e2af57b1330\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\446db2806f88813e5f9b4e2af57b1330\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\36dc0d2607f0871053495442ce0bdfcc\transformed\coordinatorlayout-1.2.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\36dc0d2607f0871053495442ce0bdfcc\transformed\coordinatorlayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fcb8f471d2218871705213dafab8a687\transformed\slidingpanelayout-1.0.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fcb8f471d2218871705213dafab8a687\transformed\slidingpanelayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4f65a7f778b1c6e118cd2a5cf8624e92\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4f65a7f778b1c6e118cd2a5cf8624e92\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\37315463ce468fae5a77bd90c79d2ce3\transformed\media-1.0.0\jars\classes.jar"
      resolved="androidx.media:media:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\37315463ce468fae5a77bd90c79d2ce3\transformed\media-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\cd1dc4f00037cece7752e3dc78a9a6fa\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\cd1dc4f00037cece7752e3dc78a9a6fa\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\jars\classes.jar"
      resolved="androidx.core:core:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\357d8662b6a39a477d8ca0dba99ca9a9\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\357d8662b6a39a477d8ca0dba99ca9a9\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f087c01a1d51cb2a0941640443975f6a\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f087c01a1d51cb2a0941640443975f6a\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ba9616361a748d9cdeb40c9f673d0ba5\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ba9616361a748d9cdeb40c9f673d0ba5\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-dev-client::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-dev-client\android\build\.transforms\2e7d93bd4d4cd0d5961dee083de6c30f\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-dev-client\android\build\.transforms\2e7d93bd4d4cd0d5961dee083de6c30f\transformed\out\jars\libs\R.jar"
      resolved="CadetVocab:expo-dev-client:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-dev-client\android\build\.transforms\2e7d93bd4d4cd0d5961dee083de6c30f\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.databinding:viewbinding:7.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4709c8cba410ac406ee6c4772d795ca6\transformed\jetified-viewbinding-7.4.2\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:7.4.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4709c8cba410ac406ee6c4772d795ca6\transformed\jetified-viewbinding-7.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\c8d2f2de7a0a96e39bb78957cbf4b94b\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\c8d2f2de7a0a96e39bb78957cbf4b94b\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\1133259983c67cf09649b4496d5fee52\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\1133259983c67cf09649b4496d5fee52\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.13.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\bb0da6daa198b190ee8dcf2ad93287fd\transformed\jetified-gifdecoder-4.13.2\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.13.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\bb0da6daa198b190ee8dcf2ad93287fd\transformed\jetified-gifdecoder-4.13.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f87173cf67f339b39bd12524d493b97a\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f87173cf67f339b39bd12524d493b97a\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\852a0494fe875070e33262d714741b8c\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\852a0494fe875070e33262d714741b8c\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\983f915e781ebb6b2906b60d1f8b5829\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\983f915e781ebb6b2906b60d1f8b5829\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation:1.3.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation\1.3.0\21f49f5f9b85fc49de712539f79123119740595\annotation-1.3.0.jar"
      resolved="androidx.annotation:annotation:1.3.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\60439acb367ee91ded2be0e83721b8e8\transformed\jetified-annotation-experimental-1.3.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\60439acb367ee91ded2be0e83721b8e8\transformed\jetified-annotation-experimental-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp-urlconnection\4.9.2\3b9e64d3d56370bc7488ed8b336d17a8013cb336\okhttp-urlconnection-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp-urlconnection:4.9.2"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.9.2\5302714ee9320b64cf65ed865e5f65981ef9ba46\okhttp-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp:4.9.2"/>
  <library
      name="com.squareup.okio:okio:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio\2.9.0\dcc813b08ce5933f8bdfd1dfbab4ad4bd170e7a\okio-jvm-2.9.0.jar"
      resolved="com.squareup.okio:okio:2.9.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.6.4\2c997cd1c0ef33f3e751d3831929aeff1390cb30\kotlinx-coroutines-core-jvm-1.6.4.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.6.4\f955fc8b2ad196e2f4429598440e15f7492eeb2b\kotlinx-coroutines-android-1.6.4.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4"/>
  <library
      name="io.insert-koin:koin-core-jvm:3.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.insert-koin\koin-core-jvm\3.4.0\6cc0aace8b12422cdb9e95b95ae7e2176cce1470\koin-core-jvm-3.4.0.jar"
      resolved="io.insert-koin:koin-core-jvm:3.4.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.10\7c002ac41f547a82e81dfebd2a20577a738dbf3f\kotlin-stdlib-jdk8-1.8.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.10\cb726a23c808a850a43e7d6b9d1ba91b02fe9f05\kotlin-stdlib-jdk7-1.8.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.8.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.8.10\6d5560a229477df9406943d5feda5618e98eb64c\kotlin-stdlib-1.8.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.8.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.8.10\a61b182458550492c12aee66157d7b524a63a5ec\kotlin-stdlib-common-1.8.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.10"/>
  <library
      name="org.jetbrains:annotations:13.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar"
      resolved="org.jetbrains:annotations:13.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\bad11f7f0dbf15cc0a31ab383e6f0c7c\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\bad11f7f0dbf15cc0a31ab383e6f0c7c\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\a099fac719a29a6e56fc26417453923e\transformed\jetified-tracing-1.1.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\a099fac719a29a6e56fc26417453923e\transformed\jetified-tracing-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\b5aa25914fa2b78ed667de0ddd139f3f\transformed\jetified-autofill-1.1.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\b5aa25914fa2b78ed667de0ddd139f3f\transformed\jetified-autofill-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fbjni:fbjni:0.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\0b8ff2350ef33c7e890a553e73e65eb5\transformed\jetified-fbjni-0.3.0\jars\classes.jar"
      resolved="com.facebook.fbjni:fbjni:0.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\0b8ff2350ef33c7e890a553e73e65eb5\transformed\jetified-fbjni-0.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:soloader:0.10.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ed97f8b44ced4959eaa5e6c1cfa2c0c8\transformed\jetified-soloader-0.10.5\jars\classes.jar"
      resolved="com.facebook.soloader:soloader:0.10.5"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ed97f8b44ced4959eaa5e6c1cfa2c0c8\transformed\jetified-soloader-0.10.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:nativeloader:0.10.5@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\nativeloader\0.10.5\492cc5082540e19b29328f2f56c53255cb6e7cc6\nativeloader-0.10.5.jar"
      resolved="com.facebook.soloader:nativeloader:0.10.5"/>
  <library
      name="com.facebook.fresco:fbcore:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\a9de9d7e9c63cab3e93d43532ac2dfc0\transformed\jetified-fbcore-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:fbcore:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\a9de9d7e9c63cab3e93d43532ac2dfc0\transformed\jetified-fbcore-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:annotation:0.10.5@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\annotation\0.10.5\dc58463712cb3e5f03d8ee5ac9743b9ced9afa77\annotation-0.10.5.jar"
      resolved="com.facebook.soloader:annotation:0.10.5"/>
  <library
      name="com.facebook.fresco:ui-common:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\df050fcf2b47cf37bdedf1566e952bfe\transformed\jetified-ui-common-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-common:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\df050fcf2b47cf37bdedf1566e952bfe\transformed\jetified-ui-common-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:drawee:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\b8bfb6d3e02b19f2d01d43ee6c3a793b\transformed\jetified-drawee-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:drawee:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\b8bfb6d3e02b19f2d01d43ee6c3a793b\transformed\jetified-drawee-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\6bf6755a84cc53add610ec5742230ad9\transformed\jetified-imagepipeline-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\6bf6755a84cc53add610ec5742230ad9\transformed\jetified-imagepipeline-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-base:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ab0524f3419901175c6809ae5b5e9cac\transformed\jetified-imagepipeline-base-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-base:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ab0524f3419901175c6809ae5b5e9cac\transformed\jetified-imagepipeline-base-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.infer.annotation\infer-annotation\0.18.0\27539793fe93ed7d92b6376281c16cda8278ab2f\infer-annotation-0.18.0.jar"
      resolved="com.facebook.infer.annotation:infer-annotation:0.18.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-annotations-jvm\1.3.72\7dba6c57de526588d8080317bda0c14cd88c8055\kotlin-annotations-jvm-1.3.72.jar"
      resolved="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72"/>
  <library
      name="com.facebook.fresco:imagepipeline-native:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\26468ca1e8bcd6fc1a98d799567f85a2\transformed\jetified-imagepipeline-native-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-native:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\26468ca1e8bcd6fc1a98d799567f85a2\transformed\jetified-imagepipeline-native-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-ashmem:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\b23291a3572e2ad52d5a701c18ec7520\transformed\jetified-memory-type-ashmem-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-ashmem:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\b23291a3572e2ad52d5a701c18ec7520\transformed\jetified-memory-type-ashmem-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-native:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\1de27ebd63d53c54e8b5a351e4db6e9c\transformed\jetified-memory-type-native-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-native:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\1de27ebd63d53c54e8b5a351e4db6e9c\transformed\jetified-memory-type-native-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-java:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\6f95b8d3ac0a210f8a663c3c13b08176\transformed\jetified-memory-type-java-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-java:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\6f95b8d3ac0a210f8a663c3c13b08176\transformed\jetified-memory-type-java-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagefilters:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7c1317cc5096b21074f2ffb700914b78\transformed\jetified-nativeimagefilters-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagefilters:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7c1317cc5096b21074f2ffb700914b78\transformed\jetified-nativeimagefilters-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagetranscoder:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\be38081d50c9033aa72a21e7b57f5f87\transformed\jetified-nativeimagetranscoder-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagetranscoder:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\be38081d50c9033aa72a21e7b57f5f87\transformed\jetified-nativeimagetranscoder-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.yoga\proguard-annotations\1.19.0\fcbbb39052e6490eaaf6a6959c49c3a4fbe87c63\proguard-annotations-1.19.0.jar"
      resolved="com.facebook.yoga:proguard-annotations:1.19.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="commons-io:commons-io:2.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-io\commons-io\2.6\815893df5f31da2ece4040fe0a12fd44b577afaf\commons-io-2.6.jar"
      resolved="commons-io:commons-io:2.6"/>
  <library
      name="commons-codec:commons-codec:1.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.10\4b95f4897fa13f2cd904aee711aeafc0c5295cd8\commons-codec-1.10.jar"
      resolved="commons-codec:commons-codec:1.10"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.13.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.13.2\de4490a6d4eb207acc0aaef59de45ce18a6b63fd\disklrucache-4.13.2.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.13.2"/>
  <library
      name="com.github.bumptech.glide:annotations:4.13.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.13.2\41c0ddef7eb508bb6de9a93a85c8d4be82f65047\annotations-4.13.2.jar"
      resolved="com.github.bumptech.glide:annotations:4.13.2"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\cba7334e691c68d9c84ada86f43b2db6\transformed\exifinterface-1.3.3\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.3"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\cba7334e691c68d9c84ada86f43b2db6\transformed\exifinterface-1.3.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-application::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-application\android\build\.transforms\24a385b0c8457ec7f79499ab2d64acbb\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-application\android\build\.transforms\24a385b0c8457ec7f79499ab2d64acbb\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-application:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-application\android\build\.transforms\24a385b0c8457ec7f79499ab2d64acbb\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-font::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-font\android\build\.transforms\7e24ded004edcc474ad7335db6104898\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-font\android\build\.transforms\7e24ded004edcc474ad7335db6104898\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-font:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-font\android\build\.transforms\7e24ded004edcc474ad7335db6104898\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-image-picker::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\.transforms\0623823f5d293e72e57fcc93f46a7ebd\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\.transforms\0623823f5d293e72e57fcc93f46a7ebd\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-image-picker:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\.transforms\0623823f5d293e72e57fcc93f46a7ebd\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-json-utils::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-json-utils\android\build\.transforms\5ffa7ac07378d1d9bbd31fe6ca034ede\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-json-utils\android\build\.transforms\5ffa7ac07378d1d9bbd31fe6ca034ede\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-json-utils:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-json-utils\android\build\.transforms\5ffa7ac07378d1d9bbd31fe6ca034ede\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-keep-awake::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-keep-awake\android\build\.transforms\ad48da66eff8cdaad4da7439b164c9d8\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-keep-awake\android\build\.transforms\ad48da66eff8cdaad4da7439b164c9d8\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-keep-awake:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-keep-awake\android\build\.transforms\ad48da66eff8cdaad4da7439b164c9d8\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-manifests::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-manifests\android\build\.transforms\796c9e9214196d3d8faadd4ff76cfb81\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-manifests\android\build\.transforms\796c9e9214196d3d8faadd4ff76cfb81\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-manifests:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-manifests\android\build\.transforms\796c9e9214196d3d8faadd4ff76cfb81\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-modules-core$android-annotation"
      jars="D:\CadetVocab - Copy\node_modules\expo-modules-core\android-annotation\build\libs\expo-modules-core$android-annotation-1.1.1.jar"
      resolved="::"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-modules-core$android-annotation-processor"
      jars="D:\CadetVocab - Copy\node_modules\expo-modules-core\android-annotation-processor\build\libs\expo-modules-core$android-annotation-processor-1.1.1.jar"
      resolved="::"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-splash-screen::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-splash-screen\android\build\.transforms\904dbd8b82d3182418c716f04d8a4458\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-splash-screen\android\build\.transforms\904dbd8b82d3182418c716f04d8a4458\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-splash-screen:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-splash-screen\android\build\.transforms\904dbd8b82d3182418c716f04d8a4458\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-dev-menu-interface::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-dev-menu-interface\android\build\.transforms\8d70c0a19f7df0b9023dc9c97fc23563\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-dev-menu-interface\android\build\.transforms\8d70c0a19f7df0b9023dc9c97fc23563\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-dev-menu-interface:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-dev-menu-interface\android\build\.transforms\8d70c0a19f7df0b9023dc9c97fc23563\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\2c95790bbae57615f21c79472117e61f\transformed\material-1.2.1\jars\classes.jar"
      resolved="com.google.android.material:material:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\2c95790bbae57615f21c79472117e61f\transformed\material-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.CanHub:Android-Image-Cropper:4.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\jars\classes.jar"
      resolved="com.github.CanHub:Android-Image-Cropper:4.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing-ktx:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\019468a1a1d0c86c3457ec29bd73c429\transformed\jetified-tracing-ktx-1.1.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\019468a1a1d0c86c3457ec29bd73c429\transformed\jetified-tracing-ktx-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.5.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\3f9978ed181811ff7376b8cb86863f17\transformed\jetified-fragment-ktx-1.5.7\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.5.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\3f9978ed181811ff7376b8cb86863f17\transformed\jetified-fragment-ktx-1.5.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\931d105cc246d52d3f08762f609d61bf\transformed\jetified-activity-ktx-1.7.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\931d105cc246d52d3f08762f609d61bf\transformed\jetified-activity-ktx-1.7.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ff927c381813b5e2b9b314f7d6c57346\transformed\jetified-viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ff927c381813b5e2b9b314f7d6c57346\transformed\jetified-viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\8e019764ccce31df5238fed8a7fd8a0b\transformed\jetified-emoji2-views-helper-1.2.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\8e019764ccce31df5238fed8a7fd8a0b\transformed\jetified-emoji2-views-helper-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fc45c20841ea83600d34b10ff3d24920\transformed\recyclerview-1.1.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fc45c20841ea83600d34b10ff3d24920\transformed\recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\e46f44a0276ba8aafbcfba1cd5c69733\transformed\transition-1.2.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\e46f44a0276ba8aafbcfba1cd5c69733\transformed\transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\9f7ee4c3c3649e24a15110985e00a4f7\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\9f7ee4c3c3649e24a15110985e00a4f7\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\665343db0ab5b61bdccec606bcb72b4a\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\665343db0ab5b61bdccec606bcb72b4a\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4f9dee590854a00291633c98ff8fe368\transformed\jetified-lifecycle-runtime-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4f9dee590854a00291633c98ff8fe368\transformed\jetified-lifecycle-runtime-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\9fe2a41276fce311cda608511fbdd69b\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\9fe2a41276fce311cda608511fbdd69b\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-base:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\62168bfc7de2695ff9dd2159c899a5da\transformed\jetified-animated-base-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-base:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\62168bfc7de2695ff9dd2159c899a5da\transformed\jetified-animated-base-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-drawable:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\2492b52a9e204f5550cb4c834a4ca36c\transformed\jetified-animated-drawable-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-drawable:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\2492b52a9e204f5550cb4c834a4ca36c\transformed\jetified-animated-drawable-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:middleware:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\71d7f704609524711cb8dfa845a61119\transformed\jetified-middleware-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:middleware:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\71d7f704609524711cb8dfa845a61119\transformed\jetified-middleware-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:soloader:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\d4f01c0ad0089eb7aa6682ec6997faa3\transformed\jetified-soloader-2.5.0\jars\classes.jar"
      resolved="com.facebook.fresco:soloader:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\d4f01c0ad0089eb7aa6682ec6997faa3\transformed\jetified-soloader-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup:kotlinpoet-ksp:1.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup\kotlinpoet-ksp\1.12.0\a94d004b878f04abefeae4db52828fc7ce11dbb9\kotlinpoet-ksp-1.12.0.jar"
      resolved="com.squareup:kotlinpoet-ksp:1.12.0"/>
  <library
      name="com.squareup:kotlinpoet:1.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup\kotlinpoet\1.12.0\2b116a78080106e6fc11fd13bc0f1c8c818a7205\kotlinpoet-1.12.0.jar"
      resolved="com.squareup:kotlinpoet:1.12.0"/>
  <library
      name="com.parse.bolts:bolts-tasks:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.parse.bolts\bolts-tasks\1.4.0\d85884acf6810a3bbbecb587f239005cbc846dc4\bolts-tasks-1.4.0.jar"
      resolved="com.parse.bolts:bolts-tasks:1.4.0"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\509cad9e024e34683798c3572b16898f\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\509cad9e024e34683798c3572b16898f\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="D:\CadetVocab - Copy\android@@:expo-updates-interface::release"
      jars="D:\CadetVocab - Copy\node_modules\expo-updates-interface\android\build\.transforms\a01fc459f0be5d494d606d86766e2501\transformed\out\jars\classes.jar;D:\CadetVocab - Copy\node_modules\expo-updates-interface\android\build\.transforms\a01fc459f0be5d494d606d86766e2501\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-updates-interface:unspecified"
      folder="D:\CadetVocab - Copy\node_modules\expo-updates-interface\android\build\.transforms\a01fc459f0be5d494d606d86766e2501\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-reflect:1.8.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.8.10\1e90b778ea4669b6bcbfaca57313665ddd804779\kotlin-reflect-1.8.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-reflect:1.8.10"/>
  <library
      name="com.google.devtools.ksp:symbol-processing-api:1.8.10-1.0.9@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.devtools.ksp\symbol-processing-api\1.8.10-1.0.9\cb23eb105730f05919bd182fe91d709650d328a6\symbol-processing-api-1.8.10-1.0.9.jar"
      resolved="com.google.devtools.ksp:symbol-processing-api:1.8.10-1.0.9"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.7.0\bdd55d654d63f008fd87f08dd166727bfdb6450a\kotlin-parcelize-runtime-1.7.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.7.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.7.0\3ff1451c5bb42b21e2efbcddad9188ac213518ca\kotlin-android-extensions-runtime-1.7.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.7.0"/>
  <library
      name="com.android.installreferrer:installreferrer:1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4fa6ead3d9a5c128fcee92bb95f998fc\transformed\jetified-installreferrer-1.0\jars\classes.jar"
      resolved="com.android.installreferrer:installreferrer:1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4fa6ead3d9a5c128fcee92bb95f998fc\transformed\jetified-installreferrer-1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.gson:gson:2.8.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.8.6\9180733b7df8542621dc12e21e87557e8c99b8cb\gson-2.8.6.jar"
      resolved="com.google.code.gson:gson:2.8.6"/>
</libraries>
