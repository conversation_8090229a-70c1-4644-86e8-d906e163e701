const createExpoWebpackConfigAsync = require('@expo/webpack-config');
const path = require('path');

module.exports = async function (env, argv) {
  // Get the default Expo Webpack config
  const config = await createExpoWebpackConfigAsync(env, argv);

  // Ensure resolve and alias objects exist
  if (!config.resolve) {
    config.resolve = {};
  }
  if (!config.resolve.alias) {
    config.resolve.alias = {};
  }

  // Alias for Material Icons
  config.resolve.alias['@react-native-vector-icons/material-design-icons'] = '@expo/vector-icons/MaterialCommunityIcons';
  config.resolve.alias['react-native-vector-icons/MaterialIcons'] = '@expo/vector-icons/MaterialIcons';
  config.resolve.alias['react-native-vector-icons/MaterialCommunityIcons'] = '@expo/vector-icons/MaterialCommunityIcons';

  // Add fallbacks for web
  if (!config.resolve.fallback) {
    config.resolve.fallback = {};
  }
  config.resolve.fallback.crypto = require.resolve('crypto-browserify');
  config.resolve.fallback.stream = require.resolve('stream-browserify');

  // Return the modified config
  return config;
}; 