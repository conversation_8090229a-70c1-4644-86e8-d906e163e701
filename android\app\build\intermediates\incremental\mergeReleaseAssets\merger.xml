<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-updates-interface\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-json-utils\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-manifests\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo-dev-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-dev-client\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo-dev-menu-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-dev-menu-interface\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo-splash-screen" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-splash-screen\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo-keep-awake" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-keep-awake\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo-image-picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo-image-loader" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-image-loader\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo-font" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-font\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo-file-system" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo-dev-menu" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\out"><file name="dev-menu-packager-host" path="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\out\dev-menu-packager-host"/><file name="EXDevMenuApp.android.js" path="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\out\EXDevMenuApp.android.js"/><file name="Inter-Black.otf" path="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\out\Inter-Black.otf"/><file name="Inter-Bold.otf" path="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\out\Inter-Bold.otf"/><file name="Inter-ExtraBold.otf" path="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\out\Inter-ExtraBold.otf"/><file name="Inter-ExtraLight.otf" path="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\out\Inter-ExtraLight.otf"/><file name="Inter-Light.otf" path="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\out\Inter-Light.otf"/><file name="Inter-Medium.otf" path="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\out\Inter-Medium.otf"/><file name="Inter-Regular.otf" path="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\out\Inter-Regular.otf"/><file name="Inter-SemiBold.otf" path="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\out\Inter-SemiBold.otf"/><file name="Inter-Thin.otf" path="D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\out\Inter-Thin.otf"/></source></dataSet><dataSet config=":expo-dev-launcher" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-constants\android\build\intermediates\library_assets\release\out"><file name="app.config" path="D:\CadetVocab - Copy\node_modules\expo-constants\android\build\intermediates\library_assets\release\out\app.config"/></source></dataSet><dataSet config=":expo-application" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo-application\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\react-native-vector-icons\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\react-native-screens\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\expo\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-community_slider" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\@react-native-community\slider\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-community_netinfo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\android\app\src\main\assets"/><source path="D:\CadetVocab - Copy\android\app\build\intermediates\shader_assets\release\out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\android\app\src\release\assets"/></dataSet><dataSet config="assets-createBundleReleaseJsAndAssets" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CadetVocab - Copy\android\app\build\generated\assets\createBundleReleaseJsAndAssets"><file name="index.android.bundle" path="D:\CadetVocab - Copy\android\app\build\generated\assets\createBundleReleaseJsAndAssets\index.android.bundle"/></source></dataSet></merger>