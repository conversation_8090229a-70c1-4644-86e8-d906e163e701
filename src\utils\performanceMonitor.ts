/**
 * Performance Monitor for Firebase Reads/Writes Optimization
 * Track and optimize database operations
 */

interface PerformanceMetric {
  operation: string;
  timestamp: number;
  duration: number;
  cacheHit: boolean;
  dataSize?: number;
  error?: string;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private readonly MAX_METRICS = 100;

  /**
   * Track a database operation
   */
  async trackOperation<T>(
    operation: string,
    asyncOperation: () => Promise<T>,
    cacheHit: boolean = false
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await asyncOperation();
      const duration = Date.now() - startTime;
      
      this.addMetric({
        operation,
        timestamp: startTime,
        duration,
        cacheHit,
        dataSize: this.estimateDataSize(result)
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.addMetric({
        operation,
        timestamp: startTime,
        duration,
        cacheHit,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      throw error;
    }
  }

  /**
   * Add a performance metric
   */
  private addMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }
    
    // Log performance issues
    if (metric.duration > 2000 && !metric.cacheHit) {
      console.warn(`🐌 Slow operation: ${metric.operation} took ${metric.duration}ms`);
    }
    
    if (metric.cacheHit) {
      console.log(`⚡ Cache hit: ${metric.operation} (${metric.duration}ms)`);
    }
  }

  /**
   * Estimate data size for monitoring
   */
  private estimateDataSize(data: any): number {
    try {
      return JSON.stringify(data).length;
    } catch {
      return 0;
    }
  }

  /**
   * Get performance summary
   */
  getSummary() {
    const now = Date.now();
    const last24h = this.metrics.filter(m => now - m.timestamp < 24 * 60 * 60 * 1000);
    
    const cacheHits = last24h.filter(m => m.cacheHit).length;
    const totalOperations = last24h.length;
    const cacheHitRate = totalOperations > 0 ? (cacheHits / totalOperations) * 100 : 0;
    
    const avgDuration = last24h.reduce((sum, m) => sum + m.duration, 0) / totalOperations || 0;
    
    const operationCounts = last24h.reduce((acc, m) => {
      acc[m.operation] = (acc[m.operation] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalOperations,
      cacheHitRate: Math.round(cacheHitRate),
      avgDuration: Math.round(avgDuration),
      operationCounts,
      slowOperations: last24h.filter(m => m.duration > 1000).length
    };
  }

  /**
   * Log performance summary
   */
  logSummary() {
    const summary = this.getSummary();
    
    console.group('📊 Performance Summary (24h)');
    console.log(`Total Operations: ${summary.totalOperations}`);
    console.log(`Cache Hit Rate: ${summary.cacheHitRate}%`);
    console.log(`Avg Duration: ${summary.avgDuration}ms`);
    console.log(`Slow Operations: ${summary.slowOperations}`);
    console.log('Operation Breakdown:', summary.operationCounts);
    console.groupEnd();
  }

  /**
   * Check if performance is optimal
   */
  isPerformanceOptimal(): boolean {
    const summary = this.getSummary();
    return (
      summary.cacheHitRate >= 70 &&  // At least 70% cache hits
      summary.avgDuration <= 500 &&  // Average under 500ms
      summary.slowOperations <= 2    // Max 2 slow operations
    );
  }
}

export const performanceMonitor = new PerformanceMonitor();

// Auto-log summary every 10 minutes in development
if (__DEV__) {
  setInterval(() => {
    performanceMonitor.logSummary();
  }, 10 * 60 * 1000);
}
