/**
 * 🚀 Performance Monitoring Service for Production Scale
 * 
 * Features:
 * - Firebase read/write tracking
 * - Cache hit/miss analytics
 * - Performance metrics collection
 * - Memory usage monitoring
 * - Network efficiency tracking
 */

import { cacheData, getCachedData } from '../utils/cache';

interface PerformanceMetrics {
  firebaseReads: number;
  firebaseWrites: number;
  cacheHits: number;
  cacheMisses: number;
  networkRequests: number;
  averageResponseTime: number;
  memoryUsage: number;
  sessionStart: number;
  lastUpdated: number;
}

interface OperationMetric {
  operation: string;
  duration: number;
  timestamp: number;
  success: boolean;
  cacheHit?: boolean;
  firebaseReads?: number;
  firebaseWrites?: number;
}

class PerformanceMonitorService {
  private metrics: PerformanceMetrics;
  private operations: OperationMetric[] = [];
  private readonly MAX_OPERATIONS = 100; // Keep last 100 operations

  constructor() {
    this.metrics = {
      firebaseReads: 0,
      firebaseWrites: 0,
      cacheHits: 0,
      cacheMisses: 0,
      networkRequests: 0,
      averageResponseTime: 0,
      memoryUsage: 0,
      sessionStart: Date.now(),
      lastUpdated: Date.now()
    };
    
    this.loadPersistedMetrics();
    this.startMemoryMonitoring();
  }

  /**
   * Track Firebase read operation
   */
  trackFirebaseRead(count: number = 1, operation?: string): void {
    this.metrics.firebaseReads += count;
    this.metrics.lastUpdated = Date.now();
    
    if (operation) {
      console.log(`[Performance] 📖 Firebase Read: ${operation} (${count} reads) | Total: ${this.metrics.firebaseReads}`);
    }
    
    this.persistMetrics();
  }

  /**
   * Track Firebase write operation
   */
  trackFirebaseWrite(count: number = 1, operation?: string): void {
    this.metrics.firebaseWrites += count;
    this.metrics.lastUpdated = Date.now();
    
    if (operation) {
      console.log(`[Performance] ✍️ Firebase Write: ${operation} (${count} writes) | Total: ${this.metrics.firebaseWrites}`);
    }
    
    this.persistMetrics();
  }

  /**
   * Track cache hit
   */
  trackCacheHit(operation: string): void {
    this.metrics.cacheHits++;
    this.metrics.lastUpdated = Date.now();
    
    console.log(`[Performance] ✅ Cache Hit: ${operation} | Hit Rate: ${this.getCacheHitRate().toFixed(1)}%`);
    this.persistMetrics();
  }

  /**
   * Track cache miss
   */
  trackCacheMiss(operation: string): void {
    this.metrics.cacheMisses++;
    this.metrics.lastUpdated = Date.now();
    
    console.log(`[Performance] ❌ Cache Miss: ${operation} | Hit Rate: ${this.getCacheHitRate().toFixed(1)}%`);
    this.persistMetrics();
  }

  /**
   * Track operation with timing
   */
  async trackOperation<T>(
    operation: string,
    fn: () => Promise<T>,
    options?: {
      trackFirebaseReads?: number;
      trackFirebaseWrites?: number;
      trackCacheHit?: boolean;
    }
  ): Promise<T> {
    const startTime = Date.now();
    let success = false;
    
    try {
      const result = await fn();
      success = true;
      
      // Track specific metrics if provided
      if (options?.trackFirebaseReads) {
        this.trackFirebaseRead(options.trackFirebaseReads, operation);
      }
      if (options?.trackFirebaseWrites) {
        this.trackFirebaseWrite(options.trackFirebaseWrites, operation);
      }
      if (options?.trackCacheHit !== undefined) {
        if (options.trackCacheHit) {
          this.trackCacheHit(operation);
        } else {
          this.trackCacheMiss(operation);
        }
      }
      
      return result;
    } catch (error) {
      success = false;
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      
      this.addOperation({
        operation,
        duration,
        timestamp: startTime,
        success,
        cacheHit: options?.trackCacheHit,
        firebaseReads: options?.trackFirebaseReads,
        firebaseWrites: options?.trackFirebaseWrites
      });
      
      this.updateAverageResponseTime(duration);
      
      console.log(`[Performance] ⏱️ ${operation}: ${duration}ms (${success ? 'Success' : 'Failed'})`);
    }
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics & {
    cacheHitRate: number;
    operationsPerMinute: number;
    sessionDuration: number;
    firebaseEfficiency: number;
  } {
    const now = Date.now();
    const sessionDuration = now - this.metrics.sessionStart;
    const operationsPerMinute = (this.operations.length / (sessionDuration / 60000));
    
    return {
      ...this.metrics,
      cacheHitRate: this.getCacheHitRate(),
      operationsPerMinute,
      sessionDuration,
      firebaseEfficiency: this.getFirebaseEfficiency()
    };
  }

  /**
   * Get recent operations
   */
  getRecentOperations(limit: number = 20): OperationMetric[] {
    return this.operations.slice(-limit).reverse();
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    totalFirebaseOperations: number;
    cacheEfficiency: string;
    averageResponseTime: string;
    recommendedOptimizations: string[];
  } {
    const totalFirebaseOps = this.metrics.firebaseReads + this.metrics.firebaseWrites;
    const cacheHitRate = this.getCacheHitRate();
    const recommendations: string[] = [];

    // Generate recommendations
    if (cacheHitRate < 70) {
      recommendations.push('Increase cache durations to improve hit rate');
    }
    if (this.metrics.firebaseReads > 100) {
      recommendations.push('Consider implementing more aggressive caching');
    }
    if (this.metrics.averageResponseTime > 1000) {
      recommendations.push('Optimize slow operations or implement background loading');
    }
    if (this.getFirebaseEfficiency() < 0.8) {
      recommendations.push('Implement batch operations to reduce Firebase calls');
    }

    return {
      totalFirebaseOperations: totalFirebaseOps,
      cacheEfficiency: `${cacheHitRate.toFixed(1)}%`,
      averageResponseTime: `${this.metrics.averageResponseTime.toFixed(0)}ms`,
      recommendedOptimizations: recommendations
    };
  }

  /**
   * Reset metrics (useful for testing or new sessions)
   */
  resetMetrics(): void {
    this.metrics = {
      firebaseReads: 0,
      firebaseWrites: 0,
      cacheHits: 0,
      cacheMisses: 0,
      networkRequests: 0,
      averageResponseTime: 0,
      memoryUsage: 0,
      sessionStart: Date.now(),
      lastUpdated: Date.now()
    };
    this.operations = [];
    this.persistMetrics();
    console.log('[Performance] 🔄 Metrics reset');
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): string {
    const data = {
      metrics: this.getMetrics(),
      operations: this.operations,
      summary: this.getPerformanceSummary()
    };
    return JSON.stringify(data, null, 2);
  }

  // Private methods

  private getCacheHitRate(): number {
    const total = this.metrics.cacheHits + this.metrics.cacheMisses;
    return total > 0 ? (this.metrics.cacheHits / total) * 100 : 0;
  }

  private getFirebaseEfficiency(): number {
    // Efficiency = cache hits / (cache hits + firebase reads)
    const totalDataAccess = this.metrics.cacheHits + this.metrics.firebaseReads;
    return totalDataAccess > 0 ? this.metrics.cacheHits / totalDataAccess : 1;
  }

  private addOperation(operation: OperationMetric): void {
    this.operations.push(operation);
    
    // Keep only recent operations
    if (this.operations.length > this.MAX_OPERATIONS) {
      this.operations = this.operations.slice(-this.MAX_OPERATIONS);
    }
  }

  private updateAverageResponseTime(duration: number): void {
    const totalOperations = this.operations.length;
    if (totalOperations === 1) {
      this.metrics.averageResponseTime = duration;
    } else {
      // Rolling average
      this.metrics.averageResponseTime = 
        (this.metrics.averageResponseTime * (totalOperations - 1) + duration) / totalOperations;
    }
  }

  private async loadPersistedMetrics(): Promise<void> {
    try {
      const persisted = await getCachedData('performance_metrics');
      if (persisted && persisted.sessionStart > Date.now() - 24 * 60 * 60 * 1000) {
        // Only load if less than 24 hours old
        this.metrics = { ...this.metrics, ...persisted };
      }
    } catch (error) {
      console.warn('[Performance] Failed to load persisted metrics:', error);
    }
  }

  private async persistMetrics(): Promise<void> {
    try {
      await cacheData('performance_metrics', this.metrics);
    } catch (error) {
      console.warn('[Performance] Failed to persist metrics:', error);
    }
  }

  private startMemoryMonitoring(): void {
    // Monitor memory usage every 30 seconds
    setInterval(() => {
      if (typeof performance !== 'undefined' && (performance as any).memory) {
        const memory = (performance as any).memory;
        this.metrics.memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // MB
      }
    }, 30000);
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitorService();
