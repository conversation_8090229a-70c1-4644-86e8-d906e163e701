{"name": "CadetVocab", "slug": "cadetvocabquiz", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.cadetvocab.quiz", "buildNumber": "1.0.0", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.cadetvocab.quiz", "versionCode": 1, "permissions": ["android.permission.RECORD_AUDIO"], "buildType": "apk"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-image-picker"], "extra": {"eas": {"projectId": "b88941df-82cb-421b-aecf-341885f111a3"}}, "sdkVersion": "49.0.0", "platforms": ["ios", "android", "web"]}