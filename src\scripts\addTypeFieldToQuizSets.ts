/**
 * Migration script to add 'type' field to existing quiz sets in Firebase
 * Run this script once to update all existing quiz sets with the default type 'Hunter'
 */

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, doc, updateDoc } from 'firebase/firestore';
import { firebaseConfig } from '../config/firebase';

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const COLLECTION_NAME = 'quizSets';

async function addTypeFieldToQuizSets() {
  try {
    console.log('🚀 Starting migration: Adding type field to quiz sets...');
    
    const quizSetsCollection = collection(db, COLLECTION_NAME);
    const querySnapshot = await getDocs(quizSetsCollection);
    
    console.log(`📊 Found ${querySnapshot.docs.length} quiz sets to update`);
    
    let updatedCount = 0;
    let skippedCount = 0;
    
    for (const docSnapshot of querySnapshot.docs) {
      const data = docSnapshot.data();
      
      // Check if type field already exists
      if (data.type) {
        console.log(`⏭️  Skipping ${docSnapshot.id} - already has type: ${data.type}`);
        skippedCount++;
        continue;
      }
      
      // Add type field with default value 'Hunter'
      const docRef = doc(db, COLLECTION_NAME, docSnapshot.id);
      await updateDoc(docRef, {
        type: 'Hunter'
      });
      
      console.log(`✅ Updated ${docSnapshot.id} - added type: Hunter`);
      updatedCount++;
    }
    
    console.log('\n🎉 Migration completed!');
    console.log(`✅ Updated: ${updatedCount} quiz sets`);
    console.log(`⏭️  Skipped: ${skippedCount} quiz sets (already had type field)`);
    console.log(`📊 Total processed: ${querySnapshot.docs.length} quiz sets`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

// Run the migration
if (require.main === module) {
  addTypeFieldToQuizSets()
    .then(() => {
      console.log('✨ Migration script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration script failed:', error);
      process.exit(1);
    });
}

export { addTypeFieldToQuizSets };
