{"cli": {"version": ">= 5.2.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal"}, "preview": {"distribution": "internal", "android": {"buildType": "apk"}}, "production": {"android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "local": {"android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "keystore": {"keystorePath": "./cadetvocab.keystore", "keystorePassword": "your_keystore_password", "keyAlias": "cadetvocab", "keyPassword": "your_key_password"}}}}, "submit": {"production": {}}}