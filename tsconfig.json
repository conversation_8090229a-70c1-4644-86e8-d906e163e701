{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "jsx": "react-native", "target": "esnext", "lib": ["dom", "esnext"], "allowJs": true, "skipLibCheck": true, "noEmit": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "moduleResolution": "bundler", "esModuleInterop": true, "isolatedModules": true}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js"]}