import AsyncStorage from '@react-native-async-storage/async-storage';

export const CACHE_KEYS = {
  // Hierarchical quiz data
  QUIZ_TYPES_METADATA: 'quiz_types_metadata_v2',
  QUIZ_TYPE_SETS: (type: string) => `quiz_type_${type}_v2`,
  QUIZ_DETAILS: (id: string) => `quiz_details_${id}_v2`,

  // User data
  USER_PROGRESS: 'user_progress_cache_',
  USER_PREFERENCES: 'user_preferences',
  USER_ACTIVITY: (userId: string) => `user_activity_${userId}_v2`,

  // System data
  LAST_SYNC: 'last_sync_timestamp',
  PENDING_WRITES: 'pending_writes_queue_v2',

  // Legacy (for migration)
  ALL_QUIZ_SETS: 'all_quiz_sets_cache',
  QUIZ_QUESTIONS: 'quiz_questions_cache_',
};

const CACHE_EXPIRY = {
  // Static data - longer cache
  QUIZ_TYPES_METADATA: 7 * 24 * 60 * 60 * 1000, // 7 days
  QUIZ_TYPE_SETS: 24 * 60 * 60 * 1000, // 24 hours
  QUIZ_DETAILS: 2 * 60 * 60 * 1000, // 2 hours

  // Dynamic data - shorter cache
  USER_PROGRESS: 2 * 60 * 60 * 1000, // 2 hours (was 15 min)
  USER_ACTIVITY: 30 * 60 * 1000, // 30 minutes

  // Legacy
  QUIZ_QUESTIONS: 24 * 60 * 60 * 1000,
  ALL_QUIZ_SETS: 24 * 60 * 60 * 1000,
};

export const cacheData = async (key: string, data: any, userId?: string) => {
  try {
    const cacheKey = userId ? `${key}_${userId}` : key;
    const cacheItem = {
      data,
      timestamp: Date.now(),
    };
    await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheItem));
  } catch (error) {
    console.error('Error caching data:', error);
  }
};

export const getCachedData = async (key: string, userId?: string) => {
  try {
    const cacheKey = userId ? `${key}_${userId}` : key;
    const cachedItem = await AsyncStorage.getItem(cacheKey);
    
    if (!cachedItem) return null;

    const { data, timestamp } = JSON.parse(cachedItem);
    const expiryTime = CACHE_EXPIRY[key as keyof typeof CACHE_EXPIRY] || 0;
    
    // Check if cache is expired
    if (expiryTime && Date.now() - timestamp > expiryTime) {
      await AsyncStorage.removeItem(cacheKey);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error retrieving cached data:', error);
    return null;
  }
};

export const clearCache = async (key: string, userId?: string) => {
  try {
    const cacheKey = userId ? `${key}_${userId}` : key;
    await AsyncStorage.removeItem(cacheKey);
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
};

export const clearAllCache = async () => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const cacheKeys = keys.filter(key =>
      Object.values(CACHE_KEYS).some(cacheKey =>
        typeof cacheKey === 'string' ? key.startsWith(cacheKey) : false
      ) || key.includes('quiz_') || key.includes('user_')
    );
    await AsyncStorage.multiRemove(cacheKeys);
    console.log('[Cache] All app-specific cache cleared.');
  } catch (error) {
    console.error('[Cache] Error clearing all cache:', error);
  }
};

// Advanced cache management
export const getCacheStats = async () => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const cacheKeys = keys.filter(key =>
      key.includes('quiz_') || key.includes('user_') || key.includes('cache')
    );

    let totalSize = 0;
    const stats = {
      totalKeys: cacheKeys.length,
      totalSize: 0,
      keysByType: {} as Record<string, number>
    };

    for (const key of cacheKeys) {
      try {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          const size = value.length;
          totalSize += size;

          const type = key.split('_')[0];
          stats.keysByType[type] = (stats.keysByType[type] || 0) + 1;
        }
      } catch (error) {
        // Skip corrupted keys
      }
    }

    stats.totalSize = totalSize;
    return stats;
  } catch (error) {
    console.error('[Cache] Error getting cache stats:', error);
    return { totalKeys: 0, totalSize: 0, keysByType: {} };
  }
};

// Smart cache invalidation
export const invalidateCachePattern = async (pattern: string) => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const keysToRemove = keys.filter(key => key.includes(pattern));
    await AsyncStorage.multiRemove(keysToRemove);
    console.log(`[Cache] Invalidated ${keysToRemove.length} keys matching pattern: ${pattern}`);
  } catch (error) {
    console.error('[Cache] Error invalidating cache pattern:', error);
  }
};