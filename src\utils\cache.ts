import AsyncStorage from '@react-native-async-storage/async-storage';

export const CACHE_KEYS = {
  QUIZ_QUESTIONS: 'quiz_questions_cache_',
  USER_PROGRESS: 'user_progress_cache_',
  USER_PREFERENCES: 'user_preferences',
  LAST_SYNC: 'last_sync_timestamp',
  ALL_QUIZ_SETS: 'all_quiz_sets_cache',
};

const CACHE_EXPIRY = {
  QUIZ_QUESTIONS: 24 * 60 * 60 * 1000, // 24 hours
  USER_PROGRESS: 15 * 60 * 1000, // 15 minutes
  ALL_QUIZ_SETS: 24 * 60 * 60 * 1000, // 24 hours
};

export const cacheData = async (key: string, data: any, userId?: string) => {
  try {
    const cacheKey = userId ? `${key}_${userId}` : key;
    const cacheItem = {
      data,
      timestamp: Date.now(),
    };
    await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheItem));
  } catch (error) {
    console.error('Error caching data:', error);
  }
};

export const getCachedData = async (key: string, userId?: string) => {
  try {
    const cacheKey = userId ? `${key}_${userId}` : key;
    const cachedItem = await AsyncStorage.getItem(cacheKey);
    
    if (!cachedItem) return null;

    const { data, timestamp } = JSON.parse(cachedItem);
    const expiryTime = CACHE_EXPIRY[key as keyof typeof CACHE_EXPIRY] || 0;
    
    // Check if cache is expired
    if (expiryTime && Date.now() - timestamp > expiryTime) {
      await AsyncStorage.removeItem(cacheKey);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error retrieving cached data:', error);
    return null;
  }
};

export const clearCache = async (key: string, userId?: string) => {
  try {
    const cacheKey = userId ? `${key}_${userId}` : key;
    await AsyncStorage.removeItem(cacheKey);
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
};

export const clearAllCache = async () => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const cacheKeys = keys.filter(key => Object.values(CACHE_KEYS).some(cacheKey => key.startsWith(cacheKey)));
    await AsyncStorage.multiRemove(cacheKeys);
    console.log('[Cache] All app-specific cache cleared.');
  } catch (error) {
    console.error('[Cache] Error clearing all cache:', error);
  }
}; 