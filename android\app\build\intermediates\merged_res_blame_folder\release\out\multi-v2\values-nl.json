{"logs": [{"outputFile": "com.cadetvocab.quiz.app-mergeReleaseResources-45:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fc775a10259692585e5f093b80f9883\\transformed\\appcompat-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,3967", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,4045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6062a715f5a47472ef2162d3759bcdd0\\transformed\\browser-1.2.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,97", "endOffsets": "153,254,365,463"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2827,3161,3262,3373", "endColumns": "102,100,110,97", "endOffsets": "2925,3257,3368,3466"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb008d0d21366cb948c612bcf9fae1d\\transformed\\jetified-Android-Image-Cropper-4.3.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,226,286,335,409,479,550,605,658,727", "endColumns": "123,46,59,48,73,69,70,54,52,68,54", "endOffsets": "174,221,281,330,404,474,545,600,653,722,777"}, "to": {"startLines": "30,31,32,36,37,38,39,40,41,42,43", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2930,3054,3101,3471,3520,3594,3664,3735,3790,3843,3912", "endColumns": "123,46,59,48,73,69,70,54,52,68,54", "endOffsets": "3049,3096,3156,3515,3589,3659,3730,3785,3838,3907,3962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d930b3380c76b7657baee5387c2188a\\transformed\\core-1.9.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4050", "endColumns": "100", "endOffsets": "4146"}}]}]}