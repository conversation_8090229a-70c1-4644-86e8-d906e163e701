import React, { useEffect, useState, useCallback } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, StatusBar, Platform, Alert, ActivityIndicator } from 'react-native';
import { Text } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../theme/colors';
import { QuizSet, RootStackParamList } from '../types';
import { quizService } from '../services/quizService';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { TAB_BAR_HEIGHT, BOTTOM_SPACING } from '../constants/layout';
import { getAuth } from 'firebase/auth';
import { useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useProgressCache } from '../hooks/useProgressCache';
import { dailyChallengeService, DailyChallenge } from '../services/dailyChallengeService';

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Main'>;

interface RecentQuiz {
  id: string;
  title: string;
  difficulty: string;
  wordCount: number;
  timestamp: number;
}

const RECENT_QUIZZES_KEY = 'recent_quizzes';
const MAX_RECENT_QUIZZES = 3;

const HomeScreen = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const insets = useSafeAreaInsets();
  const [quizzes, setQuizzes] = useState<QuizSet[]>([]);
  const [recentQuizzes, setRecentQuizzes] = useState<RecentQuiz[]>([]);
  const [loading, setLoading] = useState(true);
  const [displayName, setDisplayName] = useState('');
  const [dailyChallenge, setDailyChallenge] = useState<DailyChallenge | null>(null);
  const [loadingChallenge, setLoadingChallenge] = useState(true);

  // Progress cache to know completed quizzes
  const { progress } = useProgressCache();

  useEffect(() => {
    const auth = getAuth();
    setDisplayName(auth.currentUser?.displayName || 'Cadet');
  }, []);

  useEffect(() => {
    loadRecentQuizzes();
  }, []);

  const loadRecentQuizzes = async () => {
    try {
      const stored = await AsyncStorage.getItem(RECENT_QUIZZES_KEY);
      if (stored) {
        const parsed = JSON.parse(stored) as RecentQuiz[];
        setRecentQuizzes(parsed);
      }
    } catch (error) {
      console.error('Error loading recent quizzes:', error);
    }
  };

  const addToRecentQuizzes = async (quiz: QuizSet) => {
    try {
      const newRecent: RecentQuiz = {
        id: quiz.id,
        title: quiz.title,
        difficulty: quiz.difficulty,
        wordCount: quiz.questions?.length || 0,
        timestamp: Date.now(),
      };

      const currentRecents = [...recentQuizzes];
      const existingIndex = currentRecents.findIndex(q => q.id === quiz.id);

      if (existingIndex > -1) {
        currentRecents.splice(existingIndex, 1);
      }
      
      currentRecents.unshift(newRecent);
      
      const trimmed = currentRecents.slice(0, MAX_RECENT_QUIZZES);
      
      await AsyncStorage.setItem(RECENT_QUIZZES_KEY, JSON.stringify(trimmed));
      setRecentQuizzes(trimmed);
    } catch (error) {
      console.error('Error updating recent quizzes:', error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      loadQuizzes();
      loadRecentQuizzes();
      loadDailyChallenge();
    }, [])
  );

  const loadDailyChallenge = async () => {
    setLoadingChallenge(true);
    const challenge = await dailyChallengeService.getDailyChallenge();
    setDailyChallenge(challenge);
    setLoadingChallenge(false);
  };

  const loadQuizzes = async () => {
    try {
      const allQuizzes = await quizService.getAllQuizSets();
      setQuizzes(allQuizzes);
      setLoading(false);
    } catch (error) {
      console.error('Error loading quizzes:', error);
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Loading quizzes...</Text>
        </View>
      </View>
    );
  }

  const hasQuizzes = quizzes && quizzes.length > 0;

  const isQuizCompleted = (quizId: string) => {
    return progress?.history?.some(h => h.quizId === quizId);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Hello {displayName}, Welcome Back!</Text>
      </View>

      <ScrollView 
        style={styles.content} 
        contentContainerStyle={[
          styles.contentContainer, 
          { 
            paddingBottom: TAB_BAR_HEIGHT + 
                           (Platform.OS === 'ios' ? insets.bottom : BOTTOM_SPACING) + 
                           16
          }
        ]}
      >
        {/* Daily Challenge */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Daily Challenge</Text>
          {loadingChallenge ? (
            <View style={[styles.challengeCard, {justifyContent: 'center'}]}>
               <ActivityIndicator color={colors.text.onPrimary} />
            </View>
          ) : dailyChallenge ? (
            <TouchableOpacity 
              style={[
                styles.challengeCard,
                dailyChallenge.completed && styles.completedChallengeCard
              ]}
              onPress={() => {
                if (dailyChallenge.completed) {
                  Alert.alert(
                    'Challenge Completed',
                    "You have already completed today's challenge. Come back tomorrow for a new one!",
                  );
                  return;
                }
                navigation.navigate('Quiz', { 
                  quizSetId: dailyChallenge.quizSetId,
                  isDailyChallenge: true,
                  dailyQuestion: dailyChallenge.question,
                });
              }}
              disabled={dailyChallenge.completed}
            >
              <View>
                <Text style={[
                  styles.challengeTitle,
                  dailyChallenge.completed && styles.completedText
                ]}>
                  {dailyChallenge.completed ? 'Daily Challenge Completed!' : "Today's Vocabulary Challenge"}
                </Text>
                <Text style={[
                  styles.challengeDescription,
                  dailyChallenge.completed && styles.completedText
                ]}>
                  {dailyChallenge.completed 
                    ? 'Great job! Come back tomorrow for a new challenge.'
                    : "Complete today's challenge to earn bonus points!"}
                </Text>
              </View>
              {!dailyChallenge.completed && (
                <Icon name="chevron-right" size={24} color={colors.text.onPrimary} />
              )}
            </TouchableOpacity>
          ) : (
            <View style={styles.emptyCard}>
              <Text style={styles.emptyText}>No challenges available</Text>
            </View>
          )}
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          {recentQuizzes.length > 0 ? (
            recentQuizzes.map((quiz) => (
              <TouchableOpacity
                key={quiz.id}
                style={styles.quizCard}
                onPress={() => {
                  const goToQuiz = () => {
                    const fullQuiz = quizzes.find(q => q.id === quiz.id);
                    if (fullQuiz) {
                      addToRecentQuizzes(fullQuiz);
                    }
                    navigation.navigate('Quiz', { quizSetId: quiz.id });
                  };

                  if (isQuizCompleted(quiz.id)) {
                    Alert.alert(
                      'Reattempt Quiz',
                      'You already completed this quiz. Do you want to reattempt?',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Reattempt', onPress: goToQuiz },
                      ]
                    );
                  } else {
                    goToQuiz();
                  }
                }}
              >
                <View>
                  <Text style={styles.quizTitle}>{quiz.title}</Text>
                  <Text style={styles.quizDescription}>
                    {quiz.wordCount} words • {quiz.difficulty}
                  </Text>
                  {isQuizCompleted(quiz.id) && (
                    <Text style={styles.completedLabel}>Completed</Text>
                  )}
                </View>
                <Icon name="chevron-right" size={24} color={colors.text.secondary} />
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.emptyCard}>
              <Text style={styles.emptyText}>No recent quizzes</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.secondary,
  },
  header: {
    padding: 16,
    paddingTop: StatusBar.currentHeight || 16,
    backgroundColor: colors.dark.medium,
  },
  headerTitle: {
    color: colors.text.primary,
    fontSize: 24,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    // paddingBottom handled in style prop
  },
  section: {
    padding: 16,
  },
  sectionTitle: {
    color: colors.text.primary,
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  challengeCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    backgroundColor: colors.primary,
    marginBottom: 8,
  },
  challengeTitle: {
    color: colors.text.onPrimary,
    fontSize: 16,
    fontWeight: 'bold',
    maxWidth: '80%',
  },
  challengeDescription: {
    color: colors.text.onPrimary,
    fontSize: 14,
    marginTop: 4,
    maxWidth: '80%',
  },
  completedChallengeCard: {
    backgroundColor: colors.dark.medium,
    opacity: 0.8,
  },
  completedText: {
    color: colors.text.secondary,
    fontWeight: 'bold',
  },
  loadingCard: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 100, // Or appropriate height
  },
  completedContainer: {
    alignItems: 'center',
    gap: 4,
  },
  emptyCard: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: colors.dark.medium,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  emptyText: {
    color: colors.text.secondary,
    fontSize: 14,
  },
  quizCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    backgroundColor: colors.dark.medium,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: colors.dark.border,
  },
  quizTitle: {
    color: colors.text.primary,
    fontSize: 16,
    fontWeight: 'bold',
    maxWidth: '80%',
  },
  quizDescription: {
    color: colors.text.secondary,
    fontSize: 14,
    marginTop: 4,
    maxWidth: '80%',
  },
  completedLabel: {
    color: colors.primary,
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default HomeScreen; 