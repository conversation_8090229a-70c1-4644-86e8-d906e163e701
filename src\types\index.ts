import type { NavigatorScreenParams } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { RouteProp } from '@react-navigation/native';

export type TabParamList = {
  HomeTab: undefined;
  LibraryTab: undefined;
  ProgressTab: undefined;
  ProfileTab: undefined;
};

export type RootStackParamList = {
  Main: NavigatorScreenParams<TabParamList>;
  Login: undefined;
  Register: undefined;
  Quiz: {
    quizSetId: string;
    isDailyChallenge?: boolean;
    dailyQuestion?: QuizQuestion;
    isBookmarkQuestion?: boolean;
    bookmarkedQuestion?: QuizQuestion;
  };
  QuizComplete: {
    score: number;
    totalQuestions: number;
    quizTitle: string;
    timeSpent: number;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
  };
  Admin: undefined;
  EditProfile: undefined;
  ChangePassword: undefined;
  ForgotPassword: undefined;
  PrivacyPolicy: undefined;
};

export type NavigationProps<T extends keyof RootStackParamList> = {
  navigation: NativeStackNavigationProp<RootStackParamList, T>;
  route: RouteProp<RootStackParamList, T>;
};

export type TabNavigationProps<T extends keyof TabParamList> = {
  navigation: BottomTabNavigationProp<TabParamList, T>;
  route: RouteProp<TabParamList, T>;
};

export interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: string;
  explanation?: string;
  solution?: string;
}

export interface QuizSet {
  id: string;
  title: string;
  description?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  questions: QuizQuestion[];
}

export interface UserStats {
  totalQuizzes: number;
  correctAnswers: number;
  totalQuestions: number;
  bestCategory: string;
};

export interface UserProfile {
  id: string;
  stats: {
    wordsLearned: number;
    quizzesTaken: number;
    correctAnswers: number;
    totalQuestions: number;
    accuracy: number;
    totalXP: number;
    streak: number;
    lastQuizDate: Date | null;
  };
  settings?: {
    notifications: boolean;
    soundEffects: boolean;
  };
} 