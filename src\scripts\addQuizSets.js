const { initializeApp } = require('firebase/app');
const { getFirestore, collection, addDoc, updateDoc, doc } = require('firebase/firestore');
const { getAuth, signInWithEmailAndPassword } = require('firebase/auth');

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDJbMKkDPHRxUo1luvFzKkeG_te3SjWqGk",
  authDomain: "cadetvocab.firebaseapp.com",
  projectId: "cadetvocab",
  storageBucket: "cadetvocab.firebasestorage.app",
  messagingSenderId: "1018168044752",
  appId: "1:1018168044752:web:1c5eb10d5018f3069382d5",
  measurementId: "G-VECGVPX5Q9"
};

// Initialize Firebase
console.log('Initializing Firebase...');
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);
console.log('Firebase initialized successfully');

const quizSet = {
  title: "Quiz Set 46",
  description: "Advanced",
  category: "Advanced",
  difficulty: "advanced",
  wordCount: 10,
  icon: "default-icon",
  questions: [
    {
      "id": "79",
      "question": "Monstrous",
      "options": [
        "Severe, harsh, or excessive",
        "Refuse to accept or be associated with",
        "Extremely unfair or wicked",
        "Feeble or shaky in condition"
      ],
      "correctAnswer": "Extremely unfair or wicked",
      "solution": "\"Monstrous\" means extremely unfair or wicked, or sometimes huge. For example, the monstrous act of cruelty shocked everyone. It’s used for heinous deeds or vast sizes."
    },
    {
      "id": "81",
      "question": "Stoical",
      "options": [
        "Enduring hardship without showing feelings",
        "Having a great deal of wealth",
        "Hostile behavior; unfriendliness or opposition",
        "Having necessary skill or ability"
      ],
      "correctAnswer": "Enduring hardship without showing feelings",
      "solution": "\"Stoical\" means enduring hardship without showing feelings, often calmly. For example, her stoical response to pain inspired respect. It’s derived from Stoicism, emphasizing restraint."
    },
    {
      "id": "82",
      "question": "Worldly",
      "options": [
        "Of undisputed origin; genuine",
        "Severe, harsh, or excessive",
        "A thing that provides resistance",
        "Concerned with material values"
      ],
      "correctAnswer": "Concerned with material values",
      "solution": "\"Worldly\" means concerned with material values or practical matters, often experienced. For example, his worldly ambitions focused on wealth. It contrasts with spiritual or idealistic concerns."
    },
    {
      "id": "83",
      "question": "Disparage",
      "options": [
        "Speaking incessantly and fluently",
        "Refusing to change one's opinion",
        "Regard as being of little worth",
        "Having a great deal of wealth"
      ],
      "correctAnswer": "Regard as being of little worth",
      "solution": "\"Disparage\" means to regard as being of little worth or belittle. For example, she disparaged his efforts as insignificant. It’s used for critical, dismissive remarks."
    },
    {
      "id": "84",
      "question": "Swingeing",
      "options": [
        "Extremely unfair or extreme",
        "Imitate the appearance or character of",
        "Severe, harsh, or excessive",
        "Lacking strength to move"
      ],
      "correctAnswer": "Severe, harsh, or excessive",
      "solution": "\"Swingeing\" means severe, harsh, or excessive, often for penalties. For example, the swingeing taxes burdened the poor. It’s a rare term for drastic or punishing measures."
    },
    {
      "id": "86",
      "question": "Ricket",
      "options": [
        "Easily achieved or attained",
        "Feeble or shaky in condition",
        "A ruler with absolute power",
        "The quality of being well-meaning"
      ],
      "correctAnswer": "Feeble or shaky in condition",
      "solution": "\"Ricket\" likely refers to 'rickety,' meaning feeble or shaky in condition. For example, the rickety ladder was unsafe to use. It’s used for unstable or weak structures. Note: 'Ricket' may be a typo for 'rickety.'"
    },
    {
      "id": "75",
      "question": "Equanimity",
      "options": [
        "Military observation of a region",
        "The process of scraping away",
        "Mental calmness",
        "To articulate or enunciate"
      ],
      "correctAnswer": "Mental calmness",
      "solution": "\"Equanimity\" means mental calmness, especially under stress. For example, she faced the crisis with remarkable equanimity. It’s a formal term for composure. To remember it, think of 'equanimity' as equal (equa) calmness of mind (animus)."
    },
    {
      "id": "76",
      "question": "Futile",
      "options": [
        "Decisive or critical",
        "Relating to an early stage in history",
        "Incapable of producing useful results",
        "Very attentive to detail"
      ],
      "correctAnswer": "Incapable of producing useful results",
      "solution": "\"Futile\" means incapable of producing useful results, often pointless. For example, their futile attempts to fix the machine wasted time. It describes efforts doomed to fail. To remember it, think of 'futile' as fruitless, yielding no results."
    },
    {
      "id": "86",
      "question": "Sarcastic",
      "options": [
        "Extremely unpleasant or offensive",
        "The art of making persuasive speeches",
        "Full of energy and enthusiasm",
        "Marked by irony to mock"
      ],
      "correctAnswer": "Marked by irony to mock",
      "solution": "\"Sarcastic\" means marked by irony to mock or tease, often humorously. For example, his sarcastic comment about her punctuality stung. It’s used for witty, cutting remarks. To remember it, think of 'sarcastic' as sharp, like a 'sarcasm' that cuts with words."
    },
    {
      "id": "75",
      "question": "Vindictive",
      "options": [
        "Unable to keep still",
        "Loyalty or commitment to a cause",
        "To feel or express strong disapproval",
        "Having or showing a strong desire for revenge"
      ],
      "correctAnswer": "Having or showing a strong desire for revenge",
      "solution": "\"Vindictive\" means having or showing a strong desire for revenge. For example, her vindictive behavior caused tension."
    }
  ]
};

async function addQuizSet(email, password) {
  try {
    if (!email || !password) {
      throw new Error('Email and password are required');
    }

    console.log('Attempting to sign in with:', email);
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    console.log('Successfully signed in as:', userCredential.user.email);

    console.log('Starting to add Quiz Set ...');
    const quizSetsCollection = collection(db, 'quizSets');
    console.log('Collection reference created');
    
    // First add the document
    const docRef = await addDoc(quizSetsCollection, quizSet);
    console.log('Successfully added Quiz Set  with ID:', docRef.id);

    // Then update it to include its own ID
    await updateDoc(doc(db, 'quizSets', docRef.id), {
      id: docRef.id
    });
    console.log('Successfully updated document with its ID');
    
    // Exit the process after successful completion
    process.exit(0);
  } catch (error) {
    console.error('Error:', error.message);
    if (error.code) {
      console.error('Error code:', error.code);
    }
    // Exit with error code
    process.exit(1);
  }
}

// Get admin credentials from command line arguments
const email = process.argv[2];
const password = process.argv[3];

if (!email || !password) {
  console.error('Please provide admin email and password as command line arguments');
  console.error('Usage: node addQuizSet.js <admin_email> <admin_password>');
  process.exit(1);
}

// Add a small delay before starting to ensure Firebase is initialized
setTimeout(() => {
  console.log('Starting the add operation...');
  addQuizSet(email, password);
}, 1000); 