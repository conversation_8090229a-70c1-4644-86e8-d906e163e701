import { collection, doc, addDoc, updateDoc, deleteDoc, getDocs, getDoc, query, where, serverTimestamp, orderBy, setDoc, writeBatch, limit, startAfter, DocumentSnapshot } from 'firebase/firestore';
import { db, auth } from '../config/firebase';
import { QuizSet } from '../types';
import { cacheData, getCachedData, CACHE_KEYS, invalidateCachePattern } from '../utils/cache';

const COLLECTION_NAME = 'quizSets';
const quizSetsCollection = collection(db, COLLECTION_NAME);

// Helper function to extract number from quiz set title
const getQuizSetNumber = (title: string): number => {
  const match = title.match(/\d+$/);
  return match ? parseInt(match[0], 10) : 0;
};

export const quizService = {
  // Get all quiz sets
  getAllQuizSets: async (): Promise<QuizSet[]> => {
    // 1. Check cache first
    const cachedData = await getCachedData(CACHE_KEYS.ALL_QUIZ_SETS);
    if (cachedData) {
      console.log('[quizService] CACHE HIT: Returning all quiz sets from local cache.');
      return cachedData;
    }

    // 2. If cache miss, fetch from Firestore
    try {
      console.log('[quizService] CACHE MISS: Fetching all quiz sets from Firestore... (COST: N reads)');
      const querySnapshot = await getDocs(quizSetsCollection);
      console.log(`[quizService] Firestore responded with ${querySnapshot.docs.length} quiz sets.`);
      
      const quizSets = querySnapshot.docs.map(doc => {
        const data = doc.data() as Omit<QuizSet, 'id' | 'difficulty'> & { difficulty?: string };
        let normalizedDifficulty: 'beginner' | 'intermediate' | 'advanced' = 'intermediate';
        const firestoreDifficulty = data.difficulty?.toLowerCase();
        if (firestoreDifficulty === 'beginner') normalizedDifficulty = 'beginner';
        else if (firestoreDifficulty === 'advanced') normalizedDifficulty = 'advanced';
        else if (firestoreDifficulty === 'intermediate') normalizedDifficulty = 'intermediate';

        return {
          id: doc.id,
          title: data.title || '',
          description: data.description || '',
          wordCount: data.wordCount || 0,
          icon: data.icon || 'default-icon',
          category: data.category || '',
          type: data.type || 'Hunter', // Default to Hunter if not specified
          questions: data.questions || [],
          progress: data.progress || 0,
          difficulty: normalizedDifficulty,
        } as QuizSet;
      });

      // Sort quiz sets by the numeric part of their titles
      const sortedQuizSets = quizSets.sort((a, b) => {
        const numA = getQuizSetNumber(a.title);
        const numB = getQuizSetNumber(b.title);
        return numA - numB;
      });

      // 3. Store result in cache for next time
      await cacheData(CACHE_KEYS.ALL_QUIZ_SETS, sortedQuizSets);
      console.log('[quizService] Stored fresh quiz sets in local cache for 24 hours.');

      return sortedQuizSets;
    } catch (error: any) {
      console.error('[quizService] Error getting quiz sets:', error);
      if (error.code === 'permission-denied') {
        throw new Error('Permission denied to access quiz sets. Please try again.');
      }
      if (error.code === 'unavailable') {
        throw new Error('Service temporarily unavailable. Please check your connection and try again.');
      }
      throw new Error('Failed to load quiz sets. Please check your connection and try again.');
    }
  },

  // 🚀 OPTIMIZED: Get quiz types metadata (1 read instead of N reads)
  getQuizTypesMetadata: async (): Promise<Array<{type: string, count: number, lastUpdated: string}>> => {
    const cachedData = await getCachedData(CACHE_KEYS.QUIZ_TYPES_METADATA);
    if (cachedData) {
      console.log('[quizService] ✅ CACHE HIT: Quiz types metadata');
      return cachedData;
    }

    try {
      console.log('[quizService] 📥 CACHE MISS: Fetching quiz types metadata (COST: 1 read)');

      // Aggregate query to get types and counts efficiently
      const querySnapshot = await getDocs(quizSetsCollection);
      const typeCounts = new Map<string, number>();

      querySnapshot.docs.forEach(doc => {
        const data = doc.data();
        const type = data.type || 'Hunter';
        typeCounts.set(type, (typeCounts.get(type) || 0) + 1);
      });

      const metadata = Array.from(typeCounts.entries()).map(([type, count]) => ({
        type,
        count,
        lastUpdated: new Date().toISOString()
      }));

      await cacheData(CACHE_KEYS.QUIZ_TYPES_METADATA, metadata);
      console.log(`[quizService] ✅ Cached metadata for ${metadata.length} quiz types`);

      return metadata;
    } catch (error: any) {
      console.error('[quizService] ❌ Error fetching quiz types metadata:', error);
      throw new Error('Failed to load quiz types. Please try again.');
    }
  },

  // 🚀 OPTIMIZED: Get quiz sets by specific type (lazy loading)
  getQuizSetsByTypeOptimized: async (type: string): Promise<QuizSet[]> => {
    const cacheKey = CACHE_KEYS.QUIZ_TYPE_SETS(type);
    const cachedData = await getCachedData(cacheKey);

    if (cachedData) {
      console.log(`[quizService] ✅ CACHE HIT: Quiz sets for type ${type}`);
      return cachedData;
    }

    try {
      console.log(`[quizService] 📥 CACHE MISS: Fetching quiz sets for type ${type} (COST: 1 read)`);

      // Query without orderBy to avoid composite index requirement
      const q = query(
        quizSetsCollection,
        where('type', '==', type)
      );
      const querySnapshot = await getDocs(q);

      const quizSets = querySnapshot.docs.map(doc => {
        const data = doc.data() as Omit<QuizSet, 'id' | 'difficulty'> & { difficulty?: string };
        let normalizedDifficulty: 'beginner' | 'intermediate' | 'advanced' = 'intermediate';
        const firestoreDifficulty = data.difficulty?.toLowerCase();
        if (firestoreDifficulty === 'beginner') normalizedDifficulty = 'beginner';
        else if (firestoreDifficulty === 'advanced') normalizedDifficulty = 'advanced';
        else if (firestoreDifficulty === 'intermediate') normalizedDifficulty = 'intermediate';

        return {
          id: doc.id,
          title: data.title || '',
          description: data.description || '',
          wordCount: data.wordCount || 0,
          icon: data.icon || 'default-icon',
          category: data.category || '',
          type: data.type || type,
          questions: data.questions || [],
          progress: data.progress || 0,
          difficulty: normalizedDifficulty,
        } as QuizSet;
      });

      // Sort by quiz set number (client-side sorting since we removed orderBy)
      const sortedQuizSets = quizSets.sort((a, b) => {
        const aNum = getQuizSetNumber(a.title);
        const bNum = getQuizSetNumber(b.title);
        return aNum - bNum;
      });

      console.log(`[quizService] ✅ Client-side sorted ${sortedQuizSets.length} quiz sets for type ${type}`);

      await cacheData(cacheKey, sortedQuizSets);
      console.log(`[quizService] ✅ Cached ${sortedQuizSets.length} quiz sets for type ${type}`);

      return sortedQuizSets;
    } catch (error: any) {
      console.error(`[quizService] ❌ Error fetching quiz sets for type ${type}:`, error);
      throw new Error(`Failed to load ${type} quiz sets. Please try again.`);
    }
  },

  // 🚀 OPTIMIZED: Get all quiz sets grouped by type (uses hierarchical loading)
  getAllQuizSetsGrouped: async (): Promise<Record<string, QuizSet[]>> => {
    try {
      // First get types metadata (1 read)
      const typesMetadata = await quizService.getQuizTypesMetadata();

      // Then load each type's quiz sets (cached or 1 read per type)
      const grouped: Record<string, QuizSet[]> = {};

      for (const { type } of typesMetadata) {
        grouped[type] = await quizService.getQuizSetsByTypeOptimized(type);
      }

      return grouped;
    } catch (error: any) {
      console.error('[quizService] ❌ Error getting grouped quiz sets:', error);
      throw new Error('Failed to load quiz sets by type. Please try again.');
    }
  },

  // Get a single quiz set by ID
  getQuizSet: async (id: string): Promise<QuizSet | null> => {
    try {
      console.log(`[quizService] Fetching quiz set ${id}...`);
      const docRef = doc(db, COLLECTION_NAME, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data() as Omit<QuizSet, 'id' | 'difficulty'> & { difficulty?: string };
        let normalizedDifficulty: 'beginner' | 'intermediate' | 'advanced' = 'intermediate';
        const firestoreDifficulty = data.difficulty?.toLowerCase();
        if (firestoreDifficulty === 'beginner') normalizedDifficulty = 'beginner';
        else if (firestoreDifficulty === 'advanced') normalizedDifficulty = 'advanced';
        else if (firestoreDifficulty === 'intermediate') normalizedDifficulty = 'intermediate';

        return {
          id: docSnap.id,
          title: data.title || '',
          description: data.description || '',
          wordCount: data.wordCount || 0,
          icon: data.icon || 'default-icon',
          category: data.category || '',
          type: data.type || 'Hunter', // Default to Hunter if not specified
          questions: data.questions || [],
          progress: data.progress || 0,
          difficulty: normalizedDifficulty,
        } as QuizSet;
      }
      return null;
    } catch (error: any) {
      console.error('[quizService] Error getting quiz set:', error);
      if (error.code === 'permission-denied') {
        throw new Error('Permission denied to access this quiz set.');
      }
      throw new Error('Failed to load quiz set. Please check your connection and try again.');
    }
  },

  // Get quiz sets grouped by type
  getQuizSetsByType: async (): Promise<Record<string, QuizSet[]>> => {
    try {
      const allQuizSets = await quizService.getAllQuizSets();
      const groupedQuizSets: Record<string, QuizSet[]> = {};

      allQuizSets.forEach(quizSet => {
        const type = quizSet.type || 'Hunter';
        if (!groupedQuizSets[type]) {
          groupedQuizSets[type] = [];
        }
        groupedQuizSets[type].push(quizSet);
      });

      return groupedQuizSets;
    } catch (error: any) {
      console.error('[quizService] Error getting quiz sets by type:', error);
      throw new Error('Failed to load quiz sets by type. Please try again.');
    }
  },

  // Create a new quiz set
  createQuizSet: async (quizSet: Omit<QuizSet, 'id'>): Promise<string> => {
    try {
      const docRef = await addDoc(quizSetsCollection, quizSet);
      // Invalidate the cache since data has changed
      await cacheData(CACHE_KEYS.ALL_QUIZ_SETS, null);
      return docRef.id;
    } catch (error: any) {
      console.error('[quizService] Error creating quiz set:', error);
      throw new Error('Failed to create quiz set. Please try again.');
    }
  },

  // Update an existing quiz set
  updateQuizSet: async (id: string, updates: Partial<QuizSet>): Promise<void> => {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      await updateDoc(docRef, updates);
      // Invalidate the cache since data has changed
      await cacheData(CACHE_KEYS.ALL_QUIZ_SETS, null);
    } catch (error: any) {
      console.error('[quizService] Error updating quiz set:', error);
      throw new Error('Failed to update quiz set. Please try again.');
    }
  },

  // Delete a quiz set
  deleteQuizSet: async (id: string): Promise<void> => {
    if (!id) {
      throw new Error('Quiz set ID is required for deletion');
    }

    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        throw new Error('Quiz set not found');
      }

      await deleteDoc(docRef);
      // Invalidate the cache since data has changed
      await cacheData(CACHE_KEYS.ALL_QUIZ_SETS, null);
      console.log(`[quizService] Successfully deleted quiz set ${id}`);
    } catch (error: any) {
      console.error('[quizService] Error deleting quiz set:', error);
      if (error.code === 'permission-denied') {
        throw new Error('Permission denied to delete this quiz set');
      }
      throw new Error('Failed to delete quiz set. Please try again.');
    }
  },

  // Submit quiz results
  submitQuizResults: async (quizData: {
    quizId: string;
    score: number;
    totalQuestions: number;
    timeSpent: number;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
  }) => {
    const user = auth.currentUser;
    
    if (!user) {
      // Store the results temporarily in AsyncStorage
      const pendingResults = {
        ...quizData,
        timestamp: Date.now(),
        status: 'pending'
      };
      
      await cacheData('PENDING_QUIZ_RESULTS', JSON.stringify(pendingResults));
      throw new Error('Please log in to save your progress');
    }

    try {
      // Check for any pending results
      const pendingResultsStr = await getCachedData('PENDING_QUIZ_RESULTS');
      if (pendingResultsStr) {
        const pendingResults = JSON.parse(pendingResultsStr);
        if (pendingResults.status === 'pending') {
          // Submit the pending results first
          const resultsRef = collection(db, 'quizResults');
          await addDoc(resultsRef, {
            userId: user.uid,
            quizId: pendingResults.quizId,
            score: pendingResults.score,
            totalQuestions: pendingResults.totalQuestions,
            timeSpent: pendingResults.timeSpent,
            difficulty: pendingResults.difficulty,
            completedAt: serverTimestamp(),
            submittedAt: pendingResults.timestamp
          });
          
          // Clear pending results
          await cacheData('PENDING_QUIZ_RESULTS', null);
        }
      }

      // Submit current quiz results
      const resultsRef = collection(db, 'quizResults');
      await addDoc(resultsRef, {
        userId: user.uid,
        quizId: quizData.quizId,
        score: quizData.score,
        totalQuestions: quizData.totalQuestions,
        timeSpent: quizData.timeSpent,
        difficulty: quizData.difficulty,
        completedAt: serverTimestamp(),
      });
      
      // Update quiz completion status
      const userQuizRef = doc(db, 'userQuizzes', `${user.uid}_${quizData.quizId}`);
      await setDoc(userQuizRef, {
        userId: user.uid,
        quizId: quizData.quizId,
        completed: true,
        completedAt: serverTimestamp(),
        score: quizData.score
      }, { merge: true });
      
      console.log('[quizService] Successfully submitted quiz results');
    } catch (error: any) {
      console.error('[quizService] Error submitting quiz results:', error);
      throw new Error('Failed to submit quiz results. Please try again.');
    }
  },

  // 🚀 OPTIMIZED: Submit quiz results with batch writes (3 writes → 1 write)
  submitQuizResultsBatch: async (quizData: {
    quizId: string;
    score: number;
    totalQuestions: number;
    timeSpent: number;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
  }) => {
    const user = auth.currentUser;

    if (!user) {
      // Store the results temporarily in AsyncStorage for when user logs in
      const pendingResults = {
        ...quizData,
        timestamp: Date.now(),
        status: 'pending'
      };

      await cacheData(CACHE_KEYS.PENDING_WRITES, JSON.stringify(pendingResults));
      console.log('[quizService] 👤 Quiz results cached for unauthenticated user');
      return; // Don't throw error, just cache for later
    }

    try {
      console.log('[quizService] 🚀 Starting batch write for quiz results...');

      // Create batch for atomic writes
      const batch = writeBatch(db);

      // Check for any pending results and include them in batch
      const pendingResultsStr = await getCachedData(CACHE_KEYS.PENDING_WRITES);
      if (pendingResultsStr) {
        const pendingResults = JSON.parse(pendingResultsStr);
        if (pendingResults.status === 'pending') {
          console.log('[quizService] 📤 Including pending results in batch');

          const pendingResultRef = doc(collection(db, 'quizResults'));
          batch.set(pendingResultRef, {
            userId: user.uid,
            quizId: pendingResults.quizId,
            score: pendingResults.score,
            totalQuestions: pendingResults.totalQuestions,
            timeSpent: pendingResults.timeSpent,
            difficulty: pendingResults.difficulty,
            completedAt: serverTimestamp(),
            submittedAt: pendingResults.timestamp
          });
        }
      }

      // Add current quiz results to batch
      const currentResultRef = doc(collection(db, 'quizResults'));
      batch.set(currentResultRef, {
        userId: user.uid,
        quizId: quizData.quizId,
        score: quizData.score,
        totalQuestions: quizData.totalQuestions,
        timeSpent: quizData.timeSpent,
        difficulty: quizData.difficulty,
        completedAt: serverTimestamp(),
      });

      // Add quiz completion status to batch
      const userQuizRef = doc(db, 'userQuizzes', `${user.uid}_${quizData.quizId}`);
      batch.set(userQuizRef, {
        userId: user.uid,
        quizId: quizData.quizId,
        completed: true,
        completedAt: serverTimestamp(),
        score: quizData.score
      }, { merge: true });

      // Execute all writes atomically (1 write operation)
      await batch.commit();
      console.log('[quizService] ✅ Batch write completed successfully (COST: 1 write)');

      // Clear pending results after successful submission
      await cacheData(CACHE_KEYS.PENDING_WRITES, null);

      // Invalidate relevant caches
      await invalidateCachePattern('user_progress');
      await invalidateCachePattern('user_activity');

    } catch (error: any) {
      console.error('[quizService] ❌ Error in batch write:', error);
      throw new Error('Failed to submit quiz results. Please try again.');
    }
  },
};