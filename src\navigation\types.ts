import { QuizQuestion } from '../types';

export type RootStackParamList = {
  Home: undefined;
  Library: undefined;
  Profile: undefined;
  Progress: undefined;
  Main: undefined;
  Quiz: {
    quizSetId: string;
    isDailyChallenge?: boolean;
    dailyQuestion?: QuizQuestion;
    isBookmarkQuestion?: boolean;
    bookmarkedQuestion?: QuizQuestion;
  };
  QuizComplete: {
    score: number;
    totalQuestions: number;
    quizTitle: string;
    timeSpent: number;
  };
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  EditProfile: undefined;
  ChangePassword: undefined;
  Admin: undefined;
  PrivacyPolicy: undefined;
}; 