import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { colors } from '../theme/colors';

interface OfflineErrorProps {
  message?: string;
  onRetry?: () => void;
  showRetryButton?: boolean;
}

const OfflineError: React.FC<OfflineErrorProps> = ({
  message = 'No internet connection',
  onRetry,
  showRetryButton = true,
}) => {
  return (
    <View style={styles.container}>
      <Icon name="wifi-off" size={64} color={colors.status.error} />
      <Text style={styles.title}>No Internet Connection</Text>
      <Text style={styles.message}>
        {message}
      </Text>
      <Text style={styles.subMessage}>
        Please check your network settings and try again.
      </Text>
      
      {showRetryButton && onRetry && (
        <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
          <Icon name="refresh" size={20} color={colors.text.onPrimary} />
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: colors.secondary,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.status.error,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 8,
  },
  subMessage: {
    fontSize: 14,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  retryButtonText: {
    color: colors.text.onPrimary,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default OfflineError; 