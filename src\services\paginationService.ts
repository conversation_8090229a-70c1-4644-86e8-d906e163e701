/**
 * 🚀 Pagination Service for Large Datasets (500+ Quiz Sets)
 * 
 * Features:
 * - Virtual scrolling support
 * - Memory-efficient pagination
 * - Intelligent prefetching
 * - Search with pagination
 * - Cache-aware pagination
 */

import { collection, query, where, orderBy, limit, startAfter, getDocs, DocumentSnapshot } from 'firebase/firestore';
import { db } from '../config/firebase';
import { QuizSet } from '../types';
import { cacheData, getCachedData, CACHE_KEYS } from '../utils/cache';

export interface PaginationOptions {
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
}

export interface PaginatedResult<T> {
  items: T[];
  hasMore: boolean;
  nextCursor?: DocumentSnapshot;
  totalCount?: number;
  currentPage: number;
}

export interface SearchOptions extends PaginationOptions {
  searchTerm: string;
  searchFields: string[];
}

class PaginationService {
  private readonly DEFAULT_PAGE_SIZE = 20;
  private readonly PREFETCH_THRESHOLD = 5; // Prefetch when 5 items from end
  private readonly MAX_MEMORY_PAGES = 3; // Keep max 3 pages in memory

  /**
   * Get paginated quiz sets by type with intelligent caching
   */
  async getPaginatedQuizSets(
    type: string,
    page: number = 1,
    options: PaginationOptions = { pageSize: this.DEFAULT_PAGE_SIZE }
  ): Promise<PaginatedResult<QuizSet>> {
    const cacheKey = `${CACHE_KEYS.QUIZ_TYPE_SETS(type)}_page_${page}_${options.pageSize}`;
    
    try {
      // Check cache first
      const cachedResult = await getCachedData(cacheKey);
      if (cachedResult) {
        console.log(`[Pagination] ✅ CACHE HIT: Page ${page} for type ${type}`);
        
        // Trigger prefetch for next page if near end
        if (cachedResult.hasMore && this.shouldPrefetch(cachedResult, page)) {
          this.prefetchNextPage(type, page + 1, options);
        }
        
        return cachedResult;
      }

      console.log(`[Pagination] 📥 CACHE MISS: Fetching page ${page} for type ${type} (COST: 1 read)`);
      
      // Build query
      let q = query(
        collection(db, 'quizSets'),
        where('type', '==', type),
        orderBy(options.sortBy || 'title', options.sortOrder || 'asc'),
        limit(options.pageSize)
      );

      // Add pagination cursor for pages > 1
      if (page > 1) {
        const previousPageCursor = await this.getPreviousPageCursor(type, page - 1, options);
        if (previousPageCursor) {
          q = query(q, startAfter(previousPageCursor));
        }
      }

      // Execute query
      const querySnapshot = await getDocs(q);
      const items = querySnapshot.docs.map(doc => this.mapDocToQuizSet(doc));
      
      const result: PaginatedResult<QuizSet> = {
        items,
        hasMore: items.length === options.pageSize,
        nextCursor: querySnapshot.docs[querySnapshot.docs.length - 1],
        currentPage: page
      };

      // Cache result
      await cacheData(cacheKey, result);
      
      // Store cursor for next page
      if (result.nextCursor) {
        await this.storeCursor(type, page, result.nextCursor, options);
      }

      console.log(`[Pagination] ✅ Cached page ${page} with ${items.length} items`);
      
      // Trigger prefetch if needed
      if (result.hasMore && this.shouldPrefetch(result, page)) {
        this.prefetchNextPage(type, page + 1, options);
      }

      return result;

    } catch (error) {
      console.error(`[Pagination] ❌ Error fetching page ${page}:`, error);
      throw error;
    }
  }

  /**
   * Search quiz sets with pagination
   */
  async searchQuizSets(
    searchOptions: SearchOptions,
    page: number = 1
  ): Promise<PaginatedResult<QuizSet>> {
    const { searchTerm, searchFields, ...paginationOptions } = searchOptions;
    const cacheKey = `search_${searchTerm}_page_${page}_${paginationOptions.pageSize}`;

    try {
      // Check cache first
      const cachedResult = await getCachedData(cacheKey);
      if (cachedResult) {
        console.log(`[Pagination] ✅ SEARCH CACHE HIT: "${searchTerm}" page ${page}`);
        return cachedResult;
      }

      console.log(`[Pagination] 🔍 SEARCH CACHE MISS: "${searchTerm}" page ${page}`);

      // For search, we need to fetch more data and filter client-side
      // This is a limitation of Firestore's search capabilities
      const allResults = await this.performClientSideSearch(searchTerm, searchFields, paginationOptions);
      
      // Paginate results
      const startIndex = (page - 1) * paginationOptions.pageSize;
      const endIndex = startIndex + paginationOptions.pageSize;
      const paginatedItems = allResults.slice(startIndex, endIndex);

      const result: PaginatedResult<QuizSet> = {
        items: paginatedItems,
        hasMore: endIndex < allResults.length,
        totalCount: allResults.length,
        currentPage: page
      };

      // Cache search result
      await cacheData(cacheKey, result);
      
      return result;

    } catch (error) {
      console.error(`[Pagination] ❌ Error searching:`, error);
      throw error;
    }
  }

  /**
   * Get virtual scrolling data for large lists
   */
  async getVirtualScrollData(
    type: string,
    startIndex: number,
    endIndex: number,
    itemHeight: number = 100
  ): Promise<{
    items: QuizSet[];
    totalHeight: number;
    visibleRange: { start: number; end: number };
  }> {
    const pageSize = Math.ceil((endIndex - startIndex) / this.DEFAULT_PAGE_SIZE) * this.DEFAULT_PAGE_SIZE;
    const page = Math.floor(startIndex / this.DEFAULT_PAGE_SIZE) + 1;

    try {
      const result = await this.getPaginatedQuizSets(type, page, { pageSize });
      
      // Calculate virtual scroll metrics
      const totalCount = await this.getApproximateCount(type);
      const totalHeight = totalCount * itemHeight;
      
      return {
        items: result.items.slice(startIndex % this.DEFAULT_PAGE_SIZE, endIndex % this.DEFAULT_PAGE_SIZE),
        totalHeight,
        visibleRange: { start: startIndex, end: endIndex }
      };

    } catch (error) {
      console.error('[Pagination] ❌ Error getting virtual scroll data:', error);
      throw error;
    }
  }

  /**
   * Prefetch next page in background
   */
  private async prefetchNextPage(
    type: string,
    nextPage: number,
    options: PaginationOptions
  ): Promise<void> {
    try {
      console.log(`[Pagination] 🔄 Prefetching page ${nextPage} for type ${type}`);
      await this.getPaginatedQuizSets(type, nextPage, options);
    } catch (error) {
      console.warn(`[Pagination] ⚠️ Prefetch failed for page ${nextPage}:`, error);
    }
  }

  /**
   * Check if we should prefetch next page
   */
  private shouldPrefetch(result: PaginatedResult<QuizSet>, currentPage: number): boolean {
    return result.hasMore && result.items.length >= this.PREFETCH_THRESHOLD;
  }

  /**
   * Store pagination cursor
   */
  private async storeCursor(
    type: string,
    page: number,
    cursor: DocumentSnapshot,
    options: PaginationOptions
  ): Promise<void> {
    const cursorKey = `cursor_${type}_page_${page}_${options.pageSize}`;
    await cacheData(cursorKey, cursor);
  }

  /**
   * Get cursor for previous page
   */
  private async getPreviousPageCursor(
    type: string,
    page: number,
    options: PaginationOptions
  ): Promise<DocumentSnapshot | null> {
    const cursorKey = `cursor_${type}_page_${page}_${options.pageSize}`;
    return await getCachedData(cursorKey);
  }

  /**
   * Perform client-side search (limitation of Firestore)
   */
  private async performClientSideSearch(
    searchTerm: string,
    searchFields: string[],
    options: PaginationOptions
  ): Promise<QuizSet[]> {
    // This is a simplified implementation
    // In production, you'd want to use Algolia, Elasticsearch, or similar
    const allQuizSets = await this.getAllQuizSetsForSearch();
    
    const lowerSearchTerm = searchTerm.toLowerCase();
    
    return allQuizSets.filter(quizSet => {
      return searchFields.some(field => {
        const fieldValue = (quizSet as any)[field];
        return fieldValue && fieldValue.toLowerCase().includes(lowerSearchTerm);
      });
    });
  }

  /**
   * Get all quiz sets for search (cached)
   */
  private async getAllQuizSetsForSearch(): Promise<QuizSet[]> {
    const cacheKey = 'all_quiz_sets_for_search';
    const cached = await getCachedData(cacheKey);
    
    if (cached) {
      return cached;
    }

    // Fetch all quiz sets (this should be optimized with proper search service)
    const querySnapshot = await getDocs(collection(db, 'quizSets'));
    const allQuizSets = querySnapshot.docs.map(doc => this.mapDocToQuizSet(doc));
    
    // Cache for 1 hour
    await cacheData(cacheKey, allQuizSets);
    
    return allQuizSets;
  }

  /**
   * Get approximate count for virtual scrolling
   */
  private async getApproximateCount(type: string): Promise<number> {
    const cacheKey = `count_${type}`;
    const cached = await getCachedData(cacheKey);
    
    if (cached) {
      return cached;
    }

    // This is an approximation - in production you'd maintain counts in a separate document
    const q = query(collection(db, 'quizSets'), where('type', '==', type));
    const snapshot = await getDocs(q);
    const count = snapshot.size;
    
    await cacheData(cacheKey, count);
    return count;
  }

  /**
   * Map Firestore document to QuizSet
   */
  private mapDocToQuizSet(doc: any): QuizSet {
    const data = doc.data();
    let normalizedDifficulty: 'beginner' | 'intermediate' | 'advanced' = 'intermediate';
    const firestoreDifficulty = data.difficulty?.toLowerCase();
    if (firestoreDifficulty === 'beginner') normalizedDifficulty = 'beginner';
    else if (firestoreDifficulty === 'advanced') normalizedDifficulty = 'advanced';
    else if (firestoreDifficulty === 'intermediate') normalizedDifficulty = 'intermediate';

    return {
      id: doc.id,
      title: data.title || '',
      description: data.description || '',
      wordCount: data.wordCount || 0,
      icon: data.icon || 'default-icon',
      category: data.category || '',
      type: data.type || 'Hunter',
      questions: data.questions || [],
      progress: data.progress || 0,
      difficulty: normalizedDifficulty,
    };
  }

  /**
   * Clear pagination cache for a type
   */
  async clearPaginationCache(type?: string): Promise<void> {
    if (type) {
      const pattern = `quiz_type_${type}`;
      // Clear all pages for this type
      // Implementation would clear all cache keys matching the pattern
    } else {
      // Clear all pagination cache
      // Implementation would clear all pagination-related cache
    }
  }
}

// Export singleton instance
export const paginationService = new PaginationService();
