import { Platform } from 'react-native';

// Calculate bottom tab height based on platform and device
export const TAB_BAR_HEIGHT = Platform.select({
  ios: 49,
  android: 56,
}) || 56;

export const BOTTOM_SPACING_IOS = 34; // For iPhone X and newer models with safe areas
export const BOTTOM_SPACING_ANDROID = 16;

export const BOTTOM_SPACING = Platform.select({
  ios: BOTTOM_SPACING_IOS,
  android: BOTTOM_SPACING_ANDROID,
}) || BOTTOM_SPACING_ANDROID;

export const BOTTOM_TAB_BAR_TOTAL_HEIGHT = TAB_BAR_HEIGHT + BOTTOM_SPACING; 