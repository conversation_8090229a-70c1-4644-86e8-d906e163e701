import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  sendEmailVerification,
  sendPasswordResetEmail,
  User,
  updateProfile,
  signOut,
  ActionCodeSettings,
} from 'firebase/auth';
import { auth } from '../config/firebase';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../config/firebase';
import { sessionService } from './sessionService';

// Email verification settings
const actionCodeSettings: ActionCodeSettings = {
  url: 'https://cadetvocab.firebaseapp.com/verify-email',
  handleCodeInApp: true,
  iOS: {
    bundleId: 'com.cadetvocab.quiz'
  },
  android: {
    packageName: 'com.cadetvocab.quiz',
    installApp: true,
    minimumVersion: '12'
  },
  dynamicLinkDomain: 'cadetvocab.page.link'
};

export const authService = {
  // Initialize auth state listener with session management
  initializeAuthStateListener(callback: (user: User | null) => void) {
    return sessionService.setupAuthStateListener(callback);
  },

  // Check if user is already logged in (from persisted session)
  async getCurrentUser(): Promise<User | null> {
    return sessionService.getCurrentUser();
  },

  // Email/Password Sign In
  async signInWithEmail(email: string, password: string, rememberMe: boolean = false): Promise<void> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Check email verification
      if (!user.emailVerified) {
        // Send a new verification email if needed
        try {
          await sendEmailVerification(user, actionCodeSettings);
          throw new Error('Please verify your email before signing in. A new verification email has been sent.');
        } catch (verificationError: any) {
          if (verificationError.code === 'auth/too-many-requests') {
            throw new Error('Please check your email for the verification link or try again later.');
          }
          throw verificationError;
        }
      }

      // Create persistent session (also handles Remember-me storage)
      await sessionService.createSession(user, rememberMe);

      // Store credentials if remember-me is enabled so we can perform a silent sign-in later
      if (rememberMe) {
        await AsyncStorage.setItem('userCredentials', JSON.stringify({ email, password }));
      }
    } catch (error: any) {
      console.error('Sign in error:', error);
      throw new Error(this.getAuthErrorMessage(error.code || error.message));
    }
  },

  // Register with email
  async registerWithEmail(
    email: string,
    password: string,
    firstName: string,
    lastName: string,
  ): Promise<User> {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Update profile with first and last name
      await updateProfile(user, {
        displayName: `${firstName} ${lastName}`,
      });

      // Send verification email with action code settings
      try {
        await sendEmailVerification(user, actionCodeSettings);
      } catch (verificationError: any) {
        console.error('Error sending verification email:', verificationError);
        // If verification email fails, we should still create the temporary profile
        // but make sure to log the error and handle appropriately
      }

      // Create temporary user profile in Firestore
      // This profile will be updated with full access once email is verified
      await setDoc(doc(db, 'users', user.uid), {
        email: user.email,
        firstName,
        lastName,
        displayName: `${firstName} ${lastName}`,
        createdAt: serverTimestamp(),
        emailVerified: false,
        stats: {
          totalXP: 0,
          quizzesTaken: 0,
          accuracy: 0,
          wordsLearned: 0,
          correctAnswers: 0,
          totalQuestions: 0,
          streak: 0,
          lastQuizDate: null,
        },
      });

      return user;
    } catch (error: any) {
      console.error('Registration error:', error);
      throw new Error(this.getAuthErrorMessage(error.code || error.message));
    }
  },

  // Resend verification email
  async resendVerificationEmail(user: User): Promise<void> {
    try {
      await sendEmailVerification(user, actionCodeSettings);
    } catch (error: any) {
      if (error.code === 'auth/too-many-requests') {
        throw new Error('Too many attempts. Please wait a few minutes before trying again.');
      }
      throw new Error('Failed to send verification email. Please try again later.');
    }
  },

  // Send password reset email
  async sendPasswordReset(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email, actionCodeSettings);
    } catch (error: any) {
      throw new Error(this.getAuthErrorMessage(error.code || error.message));
    }
  },

  // Check Remember Me (returns stored credentials if they exist)
  async checkRememberMe(): Promise<{ email: string; password: string } | null> {
    try {
      const credentials = await AsyncStorage.getItem('userCredentials');
      return credentials ? JSON.parse(credentials) : null;
    } catch {
      return null;
    }
  },

  // Clear Remember Me storage
  async clearRememberMe(): Promise<void> {
    await AsyncStorage.removeItem('userCredentials');
  },

  // Sign Out
  async signOut(): Promise<void> {
    try {
      // Clear session and stored credentials before signing out
      await sessionService.clearSession();
      await AsyncStorage.removeItem('userCredentials');
      await AsyncStorage.removeItem('userProfile');

      // Sign out from Firebase
      await signOut(auth);
    } catch (error) {
      console.error('Sign Out Error:', error);
      throw error;
    }
  },

  // Get current user profile saved locally (if any)
  async getCurrentUserProfile(): Promise<any> {
    try {
      const profile = await AsyncStorage.getItem('userProfile');
      return profile ? JSON.parse(profile) : null;
    } catch {
      return null;
    }
  },

  // Map Firebase auth error codes to user-friendly messages
  getAuthErrorMessage(code: string): string {
    switch (code) {
      case 'auth/user-not-found':
        return 'No account found with this email.';
      case 'auth/wrong-password':
        return 'Invalid password.';
      case 'auth/invalid-email':
        return 'Invalid email address.';
      case 'auth/email-already-in-use':
        return 'This email is already registered.';
      case 'auth/weak-password':
        return 'Password must be at least 6 characters.';
      case 'auth/network-request-failed':
        return 'Network error. Please check your internet connection.';
      case 'auth/too-many-requests':
        return 'Too many attempts. Please try again later.';
      case 'auth/popup-closed-by-user':
        return 'Authentication cancelled. Please try again.';
      case 'auth/requires-recent-login':
        return 'Please sign in again to complete this action.';
      case 'auth/user-disabled':
        return 'This account has been disabled. Please contact support.';
      case 'auth/operation-not-allowed':
        return 'This operation is not allowed. Please contact support.';
      case 'auth/invalid-verification-code':
        return 'Invalid verification code. Please try again.';
      case 'auth/invalid-verification-id':
        return 'Invalid verification. Please try again.';
      case 'auth/missing-verification-code':
        return 'Please enter the verification code.';
      case 'auth/missing-verification-id':
        return 'Missing verification ID. Please try again.';
      default:
        return 'An error occurred. Please try again later.';
    }
  },
}; 