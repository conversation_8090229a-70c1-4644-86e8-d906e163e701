import React from 'react';
import NetInfo from '@react-native-community/netinfo';
import { Alert } from 'react-native';

export interface NetworkState {
  isConnected: boolean;
  isInternetReachable: boolean | null;
  type: string | null;
  isWifi: boolean;
  isCellular: boolean;
}

class NetworkService {
  private listeners: ((state: NetworkState) => void)[] = [];
  private currentState: NetworkState = {
    isConnected: false,
    isInternetReachable: null,
    type: null,
    isWifi: false,
    isCellular: false,
  };

  constructor() {
    this.initializeNetworkListener();
  }

  private initializeNetworkListener() {
    NetInfo.addEventListener((state) => {
      this.currentState = {
        isConnected: state.isConnected || false,
        isInternetReachable: state.isInternetReachable,
        type: state.type,
        isWifi: state.type === 'wifi',
        isCellular: state.type === 'cellular',
      };

      // Notify all listeners
      this.listeners.forEach(listener => listener(this.currentState));
    });
  }

  // Get current network state
  async getNetworkState(): Promise<NetworkState> {
    const state = await NetInfo.fetch();
    this.currentState = {
      isConnected: state.isConnected || false,
      isInternetReachable: state.isInternetReachable,
      type: state.type,
      isWifi: state.type === 'wifi',
      isCellular: state.type === 'cellular',
    };
    return this.currentState;
  }

  // Check if device is online
  async isOnline(): Promise<boolean> {
    const state = await this.getNetworkState();
    return state.isConnected && state.isInternetReachable === true;
  }

  // Add network state listener
  addListener(listener: (state: NetworkState) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // Show offline error message
  showOfflineError(message?: string): void {
    const defaultMessage = 'No internet connection. Please check your network settings and try again.';
    
    Alert.alert(
      'No Internet Connection',
      message || defaultMessage,
      [
        {
          text: 'OK',
          style: 'default',
        },
      ],
      { cancelable: true }
    );
  }

  // Execute function with network check
  async executeWithNetworkCheck<T>(
    operation: () => Promise<T>,
    errorMessage?: string
  ): Promise<T> {
    const isOnline = await this.isOnline();
    
    if (!isOnline) {
      this.showOfflineError(errorMessage);
      throw new Error('No internet connection');
    }
    
    return operation();
  }

  // Get current state synchronously
  getCurrentState(): NetworkState {
    return { ...this.currentState };
  }
}

// Export singleton instance
export const networkService = new NetworkService();

// Export hook for React components
export const useNetworkState = () => {
  const [networkState, setNetworkState] = React.useState<NetworkState>({
    isConnected: false,
    isInternetReachable: null,
    type: null,
    isWifi: false,
    isCellular: false,
  });

  React.useEffect(() => {
    // Get initial state
    networkService.getNetworkState().then(setNetworkState);
    
    // Add listener
    const unsubscribe = networkService.addListener(setNetworkState);
    
    return unsubscribe;
  }, []);

  return networkState;
}; 