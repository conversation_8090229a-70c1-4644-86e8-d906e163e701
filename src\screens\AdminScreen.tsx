import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput as RNTextInput,
  StatusBar,
} from 'react-native';
import { Text, TextInput, Button, IconButton, Portal, Modal, ActivityIndicator } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../types';
import { quizService } from '../services/quizService';
import { QuizSet, QuizQuestion } from '../types';
import { colors } from '../theme/colors';
import { auth } from '../config/firebase';

type AdminScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Admin'>;
type Difficulty = 'beginner' | 'intermediate' | 'advanced';

const AdminScreen = () => {
  const navigation = useNavigation<AdminScreenNavigationProp>();
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [quizSets, setQuizSets] = useState<QuizSet[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingQuizSet, setEditingQuizSet] = useState<Partial<QuizSet> | null>(null);
  const [currentQuestion, setCurrentQuestion] = useState<Partial<QuizQuestion> | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    const checkAdminAccess = async () => {
      const user = auth.currentUser;
      if (!user || !user.email || !user.email.endsWith('@admin.com')) {
        Alert.alert('Access Denied', 'You do not have permission to access the admin panel.');
        navigation.goBack();
        return;
      }
      setIsAdmin(true);
      setLoading(false);
    };

    checkAdminAccess();
  }, [navigation]);

  useEffect(() => {
    loadQuizSets();
  }, []);

  const loadQuizSets = async () => {
    try {
      setLoading(true);
      setError(null);
      const sets = await quizService.getAllQuizSets();
      // Filter out any invalid quiz sets
      const validSets = sets.filter(quiz => 
        quiz && 
        quiz.id && 
        quiz.title && 
        Array.isArray(quiz.questions)
      );
      setQuizSets(validSets);
    } catch (error) {
      console.error('Error loading quiz sets:', error);
      setError('Failed to load quiz sets. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const validateQuizSet = (quizSet: Partial<QuizSet>): string | null => {
    if (!quizSet.title?.trim()) {
      return 'Title is required';
    }
    if (!quizSet.category?.trim()) {
      return 'Category is required';
    }
    if (!quizSet.difficulty) {
      return 'Difficulty level is required';
    }
    if (!Array.isArray(quizSet.questions) || quizSet.questions.length === 0) {
      return 'At least one question is required';
    }
    return null;
  };

  const validateQuestion = (question: Partial<QuizQuestion>): string | null => {
    if (!question.question?.trim()) {
      return 'Question text is required';
    }
    if (!Array.isArray(question.options) || question.options.filter(opt => opt.trim()).length < 2) {
      return 'At least two non-empty options are required';
    }
    if (question.correctAnswer?.trim() && !question.options.includes(question.correctAnswer)) {
      return 'Correct answer must be one of the provided options';
    }
    return null;
  };

  const handleCreateQuizSet = () => {
    setEditingQuizSet({
      title: '',
      description: '',
      category: '',
      difficulty: 'intermediate' as Difficulty,
      questions: [],
    });
    setModalVisible(true);
  };

  const handleEditQuizSet = (quizSet: QuizSet) => {
    if (!quizSet?.id) {
      Alert.alert('Error', 'Invalid quiz set');
      return;
    }
    setEditingQuizSet(quizSet);
    setModalVisible(true);
  };

  const handleDeleteQuizSet = async (id: string) => {
    if (!id) {
      Alert.alert('Error', 'Invalid quiz set ID');
      return;
    }

    Alert.alert(
      'Delete Quiz Set',
      'Are you sure you want to delete this quiz set? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            console.log('[AdminScreen] Delete confirmation pressed for ID:', id);
            try {
              setLoading(true);
              console.log('[AdminScreen] Starting deletion of quiz set:', id);

              const quizToDelete = await quizService.getQuizSet(id);
              if (!quizToDelete) {
                console.log('[AdminScreen] Quiz set not found prior to delete attempt.');
                throw new Error('Quiz set not found. It may have already been deleted.');
              }
              console.log('[AdminScreen] Quiz set found, proceeding to delete.');

              await quizService.deleteQuizSet(id);
              console.log('[AdminScreen] quizService.deleteQuizSet call completed for ID:', id);

              setQuizSets(prevSets => prevSets.filter(set => set.id !== id));
              Alert.alert('Success', 'Quiz set deleted successfully');
              console.log('[AdminScreen] UI updated and success alert shown for ID:', id);
              
              await loadQuizSets(); // Refresh the list from server
            } catch (error: any) { // Ensure error is typed to access message
              console.error('[AdminScreen] Failed to delete quiz set. Raw error object:', error);
              Alert.alert(
                'Error Deleting Quiz', // More specific title
                error.message || 'An unknown error occurred while deleting the quiz set. Please try again.'
              );
              await loadQuizSets(); // Refresh list to ensure UI is in sync
            } finally {
              console.log('[AdminScreen] Delete operation finally block reached for ID:', id);
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleSaveQuizSet = async () => {
    if (!editingQuizSet) {
      Alert.alert('Error', 'No quiz set to save');
      return;
    }

    const validationError = validateQuizSet(editingQuizSet);
    if (validationError) {
      Alert.alert('Validation Error', validationError);
      return;
    }

    try {
      setIsSaving(true);
      if (editingQuizSet.id) {
        await quizService.updateQuizSet(editingQuizSet.id, editingQuizSet);
      } else {
        await quizService.createQuizSet(editingQuizSet as Omit<QuizSet, 'id'>);
      }
      await loadQuizSets();
      setModalVisible(false);
      setEditingQuizSet(null);
      setCurrentQuestion(null);
      Alert.alert('Success', `Quiz set ${editingQuizSet.id ? 'updated' : 'created'} successfully`);
    } catch (error) {
      console.error('Error saving quiz set:', error);
      Alert.alert('Error', 'Failed to save quiz set');
    } finally {
      setIsSaving(false);
    }
  };

  const handleAddQuestion = () => {
    if (!editingQuizSet) return;

    setCurrentQuestion({
      id: Date.now().toString(),
      question: '',
      options: ['', '', '', ''] as string[],
      correctAnswer: '',
      solution: '',
    });
  };

  const handleSaveQuestion = () => {
    if (!currentQuestion || !editingQuizSet) {
      Alert.alert('Error', 'Invalid question data');
      return;
    }

    const validationError = validateQuestion(currentQuestion);
    if (validationError) {
      Alert.alert('Validation Error', validationError);
      return;
    }

    const updatedQuestions = [
      ...(editingQuizSet.questions || []),
      currentQuestion as QuizQuestion,
    ];

    setEditingQuizSet(prev => ({
      ...prev,
      questions: updatedQuestions,
    }));
    setCurrentQuestion(null);
  };

  const handleDeleteQuestion = (questionId: string) => {
    if (!editingQuizSet?.questions) return;

    const updatedQuestions = editingQuizSet.questions.filter(q => q.id !== questionId);
    setEditingQuizSet(prev => ({
      ...prev,
      questions: updatedQuestions,
    }));
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  const renderQuizSetItem = (quizSet: QuizSet) => {
    if (!quizSet?.id || !quizSet?.title) return null;

    const questionCount = Array.isArray(quizSet.questions) ? quizSet.questions.length : 0;
    const difficulty = quizSet.difficulty || 'intermediate';

    return (
      <View key={quizSet.id} style={styles.quizSetItem}>
        <View style={styles.quizSetInfo}>
          <Text style={styles.quizSetTitle}>{quizSet.title}</Text>
          <Text style={styles.quizSetDescription}>{quizSet.description || ''}</Text>
          <View style={styles.quizSetMeta}>
            <Text style={styles.quizSetMetaText}>
              {questionCount} questions • {difficulty}
            </Text>
          </View>
        </View>
        <View style={styles.quizSetActions}>
          <IconButton
            icon="pencil"
            size={20}
            onPress={() => handleEditQuizSet(quizSet)}
            iconColor={colors.primary}
          />
          <IconButton
            icon="delete"
            size={24}
            onPress={() => handleDeleteQuizSet(quizSet.id)}
            iconColor={colors.status.error}
            style={styles.deleteButton}
          />
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  if (!isAdmin) {
    return null;
  }

  if (error) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadQuizSets}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.secondary} />
      <View style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={handleGoBack}
          iconColor={colors.text.primary}
          style={styles.backButton}
        />
        <Text style={styles.headerTitle}>Quiz Management</Text>
        <IconButton
          icon="plus"
          size={24}
          onPress={handleCreateQuizSet}
          iconColor={colors.text.primary}
        />
      </View>

      <ScrollView style={styles.content}>
        {quizSets.length > 0 ? (
          quizSets.map(renderQuizSetItem)
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No quiz sets available</Text>
            <TouchableOpacity style={styles.createButton} onPress={handleCreateQuizSet}>
              <Text style={styles.createButtonText}>Create New Quiz Set</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => {
            if (isSaving) return;
            setModalVisible(false);
            setEditingQuizSet(null);
            setCurrentQuestion(null);
          }}
          contentContainerStyle={styles.modalContainer}
        >
          <ScrollView style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {editingQuizSet?.id ? 'Edit Quiz Set' : 'Create Quiz Set'}
              </Text>
              {!isSaving && (
                <IconButton
                  icon="close"
                  size={24}
                  onPress={() => {
                    setModalVisible(false);
                    setEditingQuizSet(null);
                    setCurrentQuestion(null);
                  }}
                />
              )}
            </View>

            <TextInput
              label="Title"
              value={editingQuizSet?.title}
              onChangeText={text =>
                setEditingQuizSet(prev => ({ ...prev, title: text }))
              }
              style={styles.input}
              disabled={isSaving}
            />

            <TextInput
              label="Description"
              value={editingQuizSet?.description}
              onChangeText={text =>
                setEditingQuizSet(prev => ({ ...prev, description: text }))
              }
              multiline
              style={styles.input}
              disabled={isSaving}
            />

            <TextInput
              label="Category"
              value={editingQuizSet?.category}
              onChangeText={text =>
                setEditingQuizSet(prev => ({ ...prev, category: text }))
              }
              style={styles.input}
              disabled={isSaving}
            />

            <View style={styles.difficultyContainer}>
              <Text style={styles.difficultyLabel}>Difficulty</Text>
              <View style={styles.difficultyButtons}>
                {(['beginner', 'intermediate', 'advanced'] as const).map(level => (
                  <TouchableOpacity
                    key={level}
                    style={[
                      styles.difficultyButton,
                      editingQuizSet?.difficulty === level && styles.activeDifficultyButton
                    ]}
                    onPress={() => setEditingQuizSet(prev => ({ ...prev, difficulty: level }))}
                    disabled={isSaving}
                  >
                    <Text style={[
                      styles.difficultyButtonText,
                      editingQuizSet?.difficulty === level && styles.activeDifficultyButtonText
                    ]}>
                      {level.charAt(0).toUpperCase() + level.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.questionsSection}>
              <Text style={styles.sectionTitle}>Questions</Text>
              {editingQuizSet?.questions?.map((question, index) => (
                <View key={question.id} style={styles.questionItem}>
                  <View style={styles.questionHeader}>
                    <Text style={styles.questionNumber}>Question {index + 1}</Text>
                    <IconButton
                      icon="delete"
                      size={20}
                      iconColor={colors.status.error}
                      onPress={() => handleDeleteQuestion(question.id)}
                      disabled={isSaving}
                    />
                  </View>
                  <Text style={styles.questionText}>{question.question}</Text>
                </View>
              ))}

              {currentQuestion ? (
                <View style={styles.questionForm}>
                  <TextInput
                    label="Question"
                    value={currentQuestion.question}
                    onChangeText={text =>
                      setCurrentQuestion(prev => ({ ...prev, question: text }))
                    }
                    style={styles.input}
                    disabled={isSaving}
                  />

                  <Text style={styles.optionsLabel}>Options</Text>
                  {(currentQuestion.options || []).map((option, index) => (
                    <TextInput
                      key={index}
                      label={`Option ${index + 1}`}
                      value={option}
                      onChangeText={text => {
                        const newOptions = [...(currentQuestion.options || [])];
                        newOptions[index] = text;
                        setCurrentQuestion(prev => ({ ...prev, options: newOptions }));
                      }}
                      style={styles.input}
                      disabled={isSaving}
                    />
                  ))}

                  <TextInput
                    label="Correct Answer"
                    value={currentQuestion.correctAnswer}
                    onChangeText={text =>
                      setCurrentQuestion(prev => ({ ...prev, correctAnswer: text }))
                    }
                    style={styles.input}
                    disabled={isSaving}
                  />

                  <TextInput
                    label="Solution Explanation"
                    value={currentQuestion.solution}
                    onChangeText={text =>
                      setCurrentQuestion(prev => ({ ...prev, solution: text }))
                    }
                    style={styles.input}
                    multiline
                    numberOfLines={4}
                    disabled={isSaving}
                  />

                  <View style={styles.questionFormActions}>
                    <TouchableOpacity
                      style={[styles.button, styles.cancelButton]}
                      onPress={() => setCurrentQuestion(null)}
                      disabled={isSaving}
                    >
                      <Text style={styles.buttonText}>Cancel</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.button, styles.saveButton]}
                      onPress={handleSaveQuestion}
                      disabled={isSaving}
                    >
                      <Text style={[styles.buttonText, styles.saveButtonText]}>
                        Save Question
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <TouchableOpacity
                  style={styles.addQuestionButton}
                  onPress={handleAddQuestion}
                  disabled={isSaving}
                >
                  <Text style={styles.addQuestionText}>Add Question</Text>
                </TouchableOpacity>
              )}
            </View>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={() => {
                  setModalVisible(false);
                  setEditingQuizSet(null);
                  setCurrentQuestion(null);
                }}
                disabled={isSaving}
              >
                <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.saveButton]}
                onPress={handleSaveQuizSet}
                disabled={isSaving}
              >
                {isSaving ? (
                  <ActivityIndicator color={colors.text.onPrimary} />
                ) : (
                  <Text style={[styles.buttonText, styles.saveButtonText]}>
                    {editingQuizSet?.id ? 'Update Quiz Set' : 'Create Quiz Set'}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </ScrollView>
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.secondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: StatusBar.currentHeight ? StatusBar.currentHeight + 10 : 20,
    paddingBottom: 10,
    backgroundColor: colors.dark.medium,
    borderBottomWidth: 1,
    borderBottomColor: colors.dark.border,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  backButton: {
    // Styles for back button, adjust as needed
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: colors.text.secondary,
    fontSize: 16,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: colors.status.error,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.text.onPrimary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    color: colors.text.secondary,
    fontSize: 16,
    marginBottom: 16,
  },
  createButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createButtonText: {
    color: colors.text.onPrimary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  quizSetItem: {
    backgroundColor: colors.dark.medium,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.dark.border,
  },
  quizSetInfo: {
    flex: 1,
  },
  quizSetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  quizSetDescription: {
    color: colors.text.secondary,
    marginTop: 4,
  },
  quizSetMeta: {
    marginTop: 8,
  },
  quizSetMetaText: {
    fontSize: 12,
    color: colors.text.secondary,
  },
  quizSetActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: colors.dark.medium,
    margin: 20,
    borderRadius: 12,
    maxHeight: '90%',
  },
  modalContent: {
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    color: colors.text.primary,
    textAlign: 'center',
  },
  input: {
    marginBottom: 15,
    backgroundColor: colors.dark.dark,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  difficultyContainer: {
    marginBottom: 15,
  },
  difficultyLabel: {
    fontSize: 16,
    marginBottom: 5,
    color: colors.text.secondary,
  },
  difficultyButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  difficultyButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  activeDifficultyButton: {
    backgroundColor: colors.primary,
  },
  difficultyButtonText: {
    color: colors.primary,
  },
  activeDifficultyButtonText: {
    color: colors.text.onPrimary,
  },
  questionsSection: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 12,
  },
  questionItem: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: colors.dark.border,
    marginBottom: 10,
    backgroundColor: colors.dark.medium,
    borderRadius: 8,
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  questionNumber: {
    color: colors.text.secondary,
    fontSize: 14,
    fontWeight: 'bold',
  },
  questionText: {
    color: colors.text.primary,
    fontSize: 16,
  },
  questionForm: {
    backgroundColor: colors.dark.dark,
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  optionsLabel: {
    color: colors.text.primary,
    fontSize: 16,
    marginBottom: 8,
  },
  questionFormActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 16,
  },
  addQuestionButton: {
    marginTop: 10,
    backgroundColor: colors.primary,
  },
  addQuestionText: {
    color: colors.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 24,
  },
  button: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: colors.dark.dark,
  },
  saveButton: {
    backgroundColor: colors.primary,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text.secondary,
  },
  saveButtonText: {
    color: colors.text.onPrimary,
  },
  deleteButton: {
    marginLeft: 8,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default AdminScreen; 