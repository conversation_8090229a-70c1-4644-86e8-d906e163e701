/**
 * Advanced Recent Activity Management for Production Scale
 * 
 * Features:
 * - Minimal Firebase writes
 * - Local-first with smart sync
 * - Activity deduplication
 * - Efficient storage
 * - User-specific activity tracking
 * - Background aggregation
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { QuizSet } from '../types';
import { productionCache } from './productionCacheService';
import { offlineService } from './offlineService';

interface RecentActivity {
  id: string;
  type: 'QUIZ_COMPLETED' | 'QUIZ_STARTED' | 'BOOKMARK_ADDED' | 'DAILY_CHALLENGE';
  quizId: string;
  quizTitle: string;
  quizType: string; // Romeo, Juliet, Hunter
  difficulty: string;
  score?: number;
  timeSpent?: number;
  timestamp: number;
  userId: string;
}

interface ActivitySummary {
  totalQuizzes: number;
  totalTimeSpent: number;
  averageScore: number;
  favoriteType: string;
  streakDays: number;
  lastActivity: number;
}

interface ActivityBatch {
  activities: RecentActivity[];
  lastBatchSync: number;
  batchId: string;
}

class RecentActivityService {
  private readonly MAX_LOCAL_ACTIVITIES = 100;
  private readonly MAX_RECENT_DISPLAY = 10;
  private readonly BATCH_SIZE = 20;
  private readonly SYNC_INTERVAL = 5 * 60 * 1000; // 5 minutes
  
  private readonly STORAGE_KEYS = {
    RECENT_ACTIVITIES: (userId: string) => `recent_activities_${userId}_v2`,
    ACTIVITY_SUMMARY: (userId: string) => `activity_summary_${userId}_v2`,
    PENDING_BATCH: (userId: string) => `pending_batch_${userId}_v2`,
    LAST_SYNC: (userId: string) => `last_activity_sync_${userId}_v2`
  };

  private currentUserId: string | null = null;
  private localActivities: RecentActivity[] = [];
  private pendingBatch: RecentActivity[] = [];
  private syncTimer: NodeJS.Timeout | null = null;

  /**
   * Initialize service with user context
   */
  async initialize(userId: string): Promise<void> {
    this.currentUserId = userId;
    await this.loadLocalActivities();
    await this.loadPendingBatch();
    this.startBatchSync();
    
    console.log(`🎯 Recent activity service initialized for user: ${userId}`);
  }

  /**
   * Add activity with intelligent deduplication
   */
  async addActivity(
    type: RecentActivity['type'],
    quiz: QuizSet,
    additionalData?: Partial<RecentActivity>
  ): Promise<void> {
    if (!this.currentUserId) {
      console.warn('Recent activity service not initialized');
      return;
    }

    const activity: RecentActivity = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      quizId: quiz.id,
      quizTitle: quiz.title,
      quizType: quiz.type || 'Hunter',
      difficulty: quiz.difficulty || 'intermediate',
      timestamp: Date.now(),
      userId: this.currentUserId,
      ...additionalData
    };

    // Deduplication logic
    const isDuplicate = this.isDuplicateActivity(activity);
    if (isDuplicate) {
      console.log('🔄 Skipping duplicate activity');
      return;
    }

    // Add to local storage immediately
    this.localActivities.unshift(activity);
    
    // Trim to max size
    if (this.localActivities.length > this.MAX_LOCAL_ACTIVITIES) {
      this.localActivities = this.localActivities.slice(0, this.MAX_LOCAL_ACTIVITIES);
    }

    // Add to pending batch for sync
    this.pendingBatch.push(activity);

    // Save locally
    await this.saveLocalActivities();
    await this.savePendingBatch();

    // Update activity summary
    await this.updateActivitySummary(activity);

    console.log(`📝 Added ${type} activity for quiz: ${quiz.title}`);
  }

  /**
   * Get recent activities for display
   */
  async getRecentActivities(limit: number = this.MAX_RECENT_DISPLAY): Promise<RecentActivity[]> {
    if (!this.currentUserId) return [];

    // Try cache first
    const cacheKey = this.STORAGE_KEYS.RECENT_ACTIVITIES(this.currentUserId);
    const cached = await productionCache.get<RecentActivity[]>(cacheKey, this.currentUserId);
    
    if (cached && cached.length > 0) {
      return cached.slice(0, limit);
    }

    // Fallback to local storage
    return this.localActivities.slice(0, limit);
  }

  /**
   * Get activity summary for user dashboard
   */
  async getActivitySummary(): Promise<ActivitySummary | null> {
    if (!this.currentUserId) return null;

    const cacheKey = this.STORAGE_KEYS.ACTIVITY_SUMMARY(this.currentUserId);
    return await productionCache.get<ActivitySummary>(cacheKey, this.currentUserId);
  }

  /**
   * Check if activity is duplicate (within last 5 minutes)
   */
  private isDuplicateActivity(newActivity: RecentActivity): boolean {
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    
    return this.localActivities.some(existing => 
      existing.type === newActivity.type &&
      existing.quizId === newActivity.quizId &&
      existing.timestamp > fiveMinutesAgo
    );
  }

  /**
   * Update activity summary with new activity
   */
  private async updateActivitySummary(activity: RecentActivity): Promise<void> {
    if (!this.currentUserId) return;

    const cacheKey = this.STORAGE_KEYS.ACTIVITY_SUMMARY(this.currentUserId);
    let summary = await productionCache.get<ActivitySummary>(cacheKey, this.currentUserId);

    if (!summary) {
      summary = {
        totalQuizzes: 0,
        totalTimeSpent: 0,
        averageScore: 0,
        favoriteType: 'Hunter',
        streakDays: 0,
        lastActivity: 0
      };
    }

    // Update summary based on activity type
    if (activity.type === 'QUIZ_COMPLETED') {
      summary.totalQuizzes++;
      summary.totalTimeSpent += activity.timeSpent || 0;
      
      if (activity.score !== undefined) {
        // Recalculate average score
        const totalScore = (summary.averageScore * (summary.totalQuizzes - 1)) + activity.score;
        summary.averageScore = totalScore / summary.totalQuizzes;
      }
    }

    summary.lastActivity = activity.timestamp;

    // Calculate favorite type
    summary.favoriteType = await this.calculateFavoriteType();

    // Calculate streak (simplified - would need more complex logic)
    summary.streakDays = await this.calculateStreak();

    await productionCache.set(cacheKey, summary, undefined, this.currentUserId);
  }

  /**
   * Calculate user's favorite quiz type
   */
  private async calculateFavoriteType(): Promise<string> {
    const typeCount = new Map<string, number>();
    
    this.localActivities
      .filter(a => a.type === 'QUIZ_COMPLETED')
      .forEach(activity => {
        const count = typeCount.get(activity.quizType) || 0;
        typeCount.set(activity.quizType, count + 1);
      });

    let favoriteType = 'Hunter';
    let maxCount = 0;
    
    for (const [type, count] of typeCount.entries()) {
      if (count > maxCount) {
        maxCount = count;
        favoriteType = type;
      }
    }

    return favoriteType;
  }

  /**
   * Calculate activity streak
   */
  private async calculateStreak(): Promise<number> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    let streak = 0;
    let currentDate = new Date(today);
    
    // Check each day backwards
    for (let i = 0; i < 30; i++) { // Check last 30 days max
      const dayStart = currentDate.getTime();
      const dayEnd = dayStart + (24 * 60 * 60 * 1000);
      
      const hasActivity = this.localActivities.some(activity => 
        activity.timestamp >= dayStart && 
        activity.timestamp < dayEnd &&
        activity.type === 'QUIZ_COMPLETED'
      );
      
      if (hasActivity) {
        streak++;
      } else if (i > 0) { // Don't break on first day (today might not have activity yet)
        break;
      }
      
      currentDate.setDate(currentDate.getDate() - 1);
    }

    return streak;
  }

  /**
   * Start batch sync timer
   */
  private startBatchSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(async () => {
      await this.syncPendingBatch();
    }, this.SYNC_INTERVAL);
  }

  /**
   * Sync pending activities in batches
   */
  private async syncPendingBatch(): Promise<void> {
    if (!this.currentUserId || this.pendingBatch.length === 0) {
      return;
    }

    const batch: ActivityBatch = {
      activities: [...this.pendingBatch],
      lastBatchSync: Date.now(),
      batchId: `batch_${Date.now()}_${this.currentUserId}`
    };

    try {
      // Queue for offline sync
      await offlineService.queueAction({
        type: 'UPDATE',
        collection: 'userActivities',
        data: batch,
        priority: 'LOW', // Activity sync is low priority
        userId: this.currentUserId
      });

      // Clear pending batch after successful queue
      this.pendingBatch = [];
      await this.savePendingBatch();

      console.log(`📤 Queued ${batch.activities.length} activities for sync`);
    } catch (error) {
      console.warn('Failed to queue activity batch:', error);
    }
  }

  /**
   * Load local activities from storage
   */
  private async loadLocalActivities(): Promise<void> {
    if (!this.currentUserId) return;

    try {
      const key = this.STORAGE_KEYS.RECENT_ACTIVITIES(this.currentUserId);
      const stored = await AsyncStorage.getItem(key);
      
      if (stored) {
        this.localActivities = JSON.parse(stored);
        console.log(`📥 Loaded ${this.localActivities.length} local activities`);
      }
    } catch (error) {
      console.warn('Failed to load local activities:', error);
    }
  }

  /**
   * Save local activities to storage
   */
  private async saveLocalActivities(): Promise<void> {
    if (!this.currentUserId) return;

    try {
      const key = this.STORAGE_KEYS.RECENT_ACTIVITIES(this.currentUserId);
      await AsyncStorage.setItem(key, JSON.stringify(this.localActivities));
    } catch (error) {
      console.warn('Failed to save local activities:', error);
    }
  }

  /**
   * Load pending batch from storage
   */
  private async loadPendingBatch(): Promise<void> {
    if (!this.currentUserId) return;

    try {
      const key = this.STORAGE_KEYS.PENDING_BATCH(this.currentUserId);
      const stored = await AsyncStorage.getItem(key);
      
      if (stored) {
        this.pendingBatch = JSON.parse(stored);
        console.log(`📥 Loaded ${this.pendingBatch.length} pending activities`);
      }
    } catch (error) {
      console.warn('Failed to load pending batch:', error);
    }
  }

  /**
   * Save pending batch to storage
   */
  private async savePendingBatch(): Promise<void> {
    if (!this.currentUserId) return;

    try {
      const key = this.STORAGE_KEYS.PENDING_BATCH(this.currentUserId);
      await AsyncStorage.setItem(key, JSON.stringify(this.pendingBatch));
    } catch (error) {
      console.warn('Failed to save pending batch:', error);
    }
  }

  /**
   * Force sync all pending activities
   */
  async forceSyncActivities(): Promise<void> {
    await this.syncPendingBatch();
  }

  /**
   * Clear all activity data (for logout)
   */
  async clearActivityData(): Promise<void> {
    if (!this.currentUserId) return;

    this.localActivities = [];
    this.pendingBatch = [];

    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }

    const keysToRemove = [
      this.STORAGE_KEYS.RECENT_ACTIVITIES(this.currentUserId),
      this.STORAGE_KEYS.ACTIVITY_SUMMARY(this.currentUserId),
      this.STORAGE_KEYS.PENDING_BATCH(this.currentUserId),
      this.STORAGE_KEYS.LAST_SYNC(this.currentUserId)
    ];

    await AsyncStorage.multiRemove(keysToRemove);
    this.currentUserId = null;
  }

  /**
   * Get activity statistics for debugging
   */
  getActivityStats() {
    return {
      localActivities: this.localActivities.length,
      pendingBatch: this.pendingBatch.length,
      userId: this.currentUserId,
      syncTimerActive: !!this.syncTimer
    };
  }
}

export const recentActivityService = new RecentActivityService();
