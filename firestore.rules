rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Helper function to check if the user is the owner of the document.
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }

    // Helper function for admin check (can be adapted).
    function isAdmin() {
      // Example: Check for a custom claim. This is more secure than checking email.
      return request.auth != null && request.auth.token.admin == true;
    }

    // Users can only read/write their own profile and progress data.
    match /users/{userId} {
      allow read, update, delete: if isOwner(userId);
      // Allow any authenticated user to create their own user document.
      allow create: if request.auth.uid == userId;
    }
    
    match /userProgress/{userId} {
      allow read, write: if isOwner(userId);
    }

    // Quiz sets are public to read, but write-protected for admins.
    match /quizSets/{quizId} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // Quiz results - users can write their own results
    match /quizResults/{resultId} {
       allow read, write: if isOwner(resource.data.userId);
       allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }

    // User quiz completion status
    match /userQuizzes/{userQuizId} {
       allow read, write: if isOwner(resource.data.userId);
       allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }

    // User activity tracking
    match /userActivity/{activityId} {
       allow read, write: if isOwner(resource.data.userId);
       allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }

    // Daily challenges
    match /dailyChallenges/{challengeId} {
       allow read: if true;
       allow write: if isAdmin();
    }

    // User bookmarks
    match /userBookmarks/{userId} {
       allow read, write: if isOwner(userId);
    }
  }
}