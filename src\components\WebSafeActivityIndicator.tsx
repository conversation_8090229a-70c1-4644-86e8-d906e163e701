import React from 'react';
import { ActivityIndicator, Platform, StyleSheet, View } from 'react-native';
import type { ActivityIndicatorProps } from 'react-native';

export const WebSafeActivityIndicator: React.FC<ActivityIndicatorProps> = ({
  animating,
  color,
  size,
  style,
  hidesWhenStopped,
  ...props
}) => {
  if (Platform.OS === 'web') {
    return (
      <View style={[styles.container, style]}>
        <ActivityIndicator
          animating={animating}
          color={color}
          size={size}
          hidesWhenStopped={hidesWhenStopped}
          {...props}
        />
      </View>
    );
  }

  return (
    <ActivityIndicator
      animating={animating}
      color={color}
      size={size}
      style={style}
      hidesWhenStopped={hidesWhenStopped}
      {...props}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
}); 