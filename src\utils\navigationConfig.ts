import { Platform } from 'react-native';
import { NativeStackNavigationOptions } from '@react-navigation/native-stack';

export const getNavigationConfig = (): { screenOptions: NativeStackNavigationOptions } => {
  if (Platform.OS === 'web') {
    return {
      screenOptions: {
        animation: 'none',
        headerShown: false,
      }
    };
  }
  
  return {
    screenOptions: {
      animation: 'default',
      headerShown: false,
    }
  };
}; 