-- Merging decision tree log ---
manifest
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:1:1-35:12
INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:1:1-35:12
INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:1:1-35:12
INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:1:1-35:12
MERGED from [:react-native-async-storage_async-storage] D:\CadetVocab - Copy\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_netinfo] D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-10:12
MERGED from [:react-native-community_slider] D:\CadetVocab - Copy\node_modules\@react-native-community\slider\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:expo] D:\CadetVocab - Copy\node_modules\expo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] D:\CadetVocab - Copy\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] D:\CadetVocab - Copy\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] D:\CadetVocab - Copy\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-application] D:\CadetVocab - Copy\node_modules\expo-application\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-constants] D:\CadetVocab - Copy\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-launcher] D:\CadetVocab - Copy\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-menu] D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-33:12
MERGED from [:expo-font] D:\CadetVocab - Copy\node_modules\expo-font\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-image-loader] D:\CadetVocab - Copy\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-59:12
MERGED from [:expo-keep-awake] D:\CadetVocab - Copy\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-splash-screen] D:\CadetVocab - Copy\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-18:12
MERGED from [:expo-dev-menu-interface] D:\CadetVocab - Copy\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.72.6] C:\Users\<USER>\.gradle\caches\transforms-3\4115d94f0331b651ff9a931638b09a6b\transformed\jetified-react-android-0.72.6-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\156cc80b8e827be23a6f83eedea3693c\transformed\jetified-fresco-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\708563591f53d5a31de1906d830ad0e4\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:animated-gif:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\42ea4c6e1a66a113079b1b4d69f83f54\transformed\jetified-animated-gif-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:webpsupport:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa0420fa807ca9e64936073561133e90\transformed\jetified-webpsupport-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.react:hermes-android:0.72.6] C:\Users\<USER>\.gradle\caches\transforms-3\7b52d1ae4d5c74a25ca6959d5bf0b4e2\transformed\jetified-hermes-android-0.72.6-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2c95790bbae57615f21c79472117e61f\transformed\material-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:2:1-38:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8fc775a10259692585e5f093b80f9883\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bfb57a730389e05991612d1bc8a291e2\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b5aa25914fa2b78ed667de0ddd139f3f\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a654646302f385221d786cebcdebb3\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f58386d8ce2cbfe1eff192583b03fb0\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3125dda4c537b5379be4b8059f12fd05\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\019468a1a1d0c86c3457ec29bd73c429\transformed\jetified-tracing-ktx-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:glide:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-3\0a43de56c5261a993837e8c0dbd0df27\transformed\jetified-glide-4.13.2\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.fragment:fragment-ktx:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\3f9978ed181811ff7376b8cb86863f17\transformed\jetified-fragment-ktx-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\931d105cc246d52d3f08762f609d61bf\transformed\jetified-activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f52e543fca820718464f2c82e71274f\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ff927c381813b5e2b9b314f7d6c57346\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\3fc7415478e4731dd9355d61543956b8\transformed\fragment-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\4dfec2d3282dc05e74257e73dac5bcbd\transformed\jetified-activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e019764ccce31df5238fed8a7fd8a0b\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\388d7a49ace0c78216797aa6078fd027\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\680cc59621be334d9f131292c916b416\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a618a4ec6e9a0f031159e531016d8bad\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\27abba2b290b4a6f70e4bbb29befb79f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\36dc0d2607f0871053495442ce0bdfcc\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6062a715f5a47472ef2162d3759bcdd0\transformed\browser-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5548385058946391f436f92302dcc195\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\446db2806f88813e5f9b4e2af57b1330\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc45c20841ea83600d34b10ff3d24920\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fcb8f471d2218871705213dafab8a687\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f65a7f778b1c6e118cd2a5cf8624e92\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e46f44a0276ba8aafbcfba1cd5c69733\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\37315463ce468fae5a77bd90c79d2ce3\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd1dc4f00037cece7752e3dc78a9a6fa\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bdf6b0bf865be6d537eba5d61fa0e42\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\356767d41a07ee2a91639a8f928dc465\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a1d09347030c2d407badbecd54e1c2a\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\74f887f2d992a24d7a2a9bb57befd934\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f7ee4c3c3649e24a15110985e00a4f7\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eb4dbdbd7c7cbc5881d361f54e0425c1\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7762cadebcbb43f1c3c494b3fca017db\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\665343db0ab5b61bdccec606bcb72b4a\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f087c01a1d51cb2a0941640443975f6a\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\49389fe05353e9e81c6c6ad561e8629c\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9dee590854a00291633c98ff8fe368\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9fe2a41276fce311cda608511fbdd69b\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad11f7f0dbf15cc0a31ab383e6f0c7c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a099fac719a29a6e56fc26417453923e\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fbjni:fbjni:0.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8ff2350ef33c7e890a553e73e65eb5\transformed\jetified-fbjni-0.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:animated-base:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\62168bfc7de2695ff9dd2159c899a5da\transformed\jetified-animated-base-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:animated-drawable:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2492b52a9e204f5550cb4c834a4ca36c\transformed\jetified-animated-drawable-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:drawee:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8bfb6d3e02b19f2d01d43ee6c3a793b\transformed\jetified-drawee-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:middleware:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\71d7f704609524711cb8dfa845a61119\transformed\jetified-middleware-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:ui-common:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\df050fcf2b47cf37bdedf1566e952bfe\transformed\jetified-ui-common-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c1317cc5096b21074f2ffb700914b78\transformed\jetified-nativeimagefilters-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1de27ebd63d53c54e8b5a351e4db6e9c\transformed\jetified-memory-type-native-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f95b8d3ac0a210f8a663c3c13b08176\transformed\jetified-memory-type-java-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\26468ca1e8bcd6fc1a98d799567f85a2\transformed\jetified-imagepipeline-native-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\b23291a3572e2ad52d5a701c18ec7520\transformed\jetified-memory-type-ashmem-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6bf6755a84cc53add610ec5742230ad9\transformed\jetified-imagepipeline-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\be38081d50c9033aa72a21e7b57f5f87\transformed\jetified-nativeimagetranscoder-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab0524f3419901175c6809ae5b5e9cac\transformed\jetified-imagepipeline-base-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:soloader:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4f01c0ad0089eb7aa6682ec6997faa3\transformed\jetified-soloader-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\ed97f8b44ced4959eaa5e6c1cfa2c0c8\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:2:1-17:12
MERGED from [:expo-dev-client] D:\CadetVocab - Copy\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9de9d7e9c63cab3e93d43532ac2dfc0\transformed\jetified-fbcore-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba9616361a748d9cdeb40c9f673d0ba5\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4709c8cba410ac406ee6c4772d795ca6\transformed\jetified-viewbinding-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\cba7334e691c68d9c84ada86f43b2db6\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c8d2f2de7a0a96e39bb78957cbf4b94b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\509cad9e024e34683798c3572b16898f\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-3\bb0da6daa198b190ee8dcf2ad93287fd\transformed\jetified-gifdecoder-4.13.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\357d8662b6a39a477d8ca0dba99ca9a9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f87173cf67f339b39bd12524d493b97a\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\852a0494fe875070e33262d714741b8c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\983f915e781ebb6b2906b60d1f8b5829\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1133259983c67cf09649b4496d5fee52\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [:expo-manifests] D:\CadetVocab - Copy\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] D:\CadetVocab - Copy\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] D:\CadetVocab - Copy\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60439acb367ee91ded2be0e83721b8e8\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4fa6ead3d9a5c128fcee92bb95f998fc\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:2:1-13:12
INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:1:1-35:12
INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:1:1-35:12
INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:1:1-35:12
	package
		INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:1:1-35:12
		INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:1:1-35:12
		INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:1:70-116
	android:versionCode
		INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:1:1-35:12
		INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:2:3-64
MERGED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:5-67
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:2:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:3:3-77
MERGED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:10:5-80
MERGED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:5-80
MERGED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:5-80
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:3:20-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:4:3-68
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:4:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:5:3-75
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:5:20-73
uses-permission#android.permission.VIBRATE
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:6:3-63
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:6:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:7:3-78
MERGED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-81
MERGED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:5-81
MERGED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:5-81
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:7:20-76
queries
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:8:3-14:13
MERGED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:5-27:15
MERGED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:5-27:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:9:5-20:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:9:5-20:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:9:5-13:14
action#android.intent.action.VIEW
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:10:7-58
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:10:15-56
category#android.intent.category.BROWSABLE
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:11:7-67
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:11:17-65
data
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:12:7-37
	android:scheme
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:12:13-35
application
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:3-34:17
MERGED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:29:5-57:19
MERGED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:29:5-57:19
MERGED from [:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2c95790bbae57615f21c79472117e61f\transformed\material-1.2.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2c95790bbae57615f21c79472117e61f\transformed\material-1.2.1\AndroidManifest.xml:22:5-20
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:22:5-36:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:22:5-36:19
MERGED from [com.github.bumptech.glide:glide:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-3\0a43de56c5261a993837e8c0dbd0df27\transformed\jetified-glide-4.13.2\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-3\0a43de56c5261a993837e8c0dbd0df27\transformed\jetified-glide-4.13.2\AndroidManifest.xml:10:5-20
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f52e543fca820718464f2c82e71274f\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f52e543fca820718464f2c82e71274f\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad11f7f0dbf15cc0a31ab383e6f0c7c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad11f7f0dbf15cc0a31ab383e6f0c7c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\ed97f8b44ced4959eaa5e6c1cfa2c0c8\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\ed97f8b44ced4959eaa5e6c1cfa2c0c8\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c8d2f2de7a0a96e39bb78957cbf4b94b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c8d2f2de7a0a96e39bb78957cbf4b94b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-3\bb0da6daa198b190ee8dcf2ad93287fd\transformed\jetified-gifdecoder-4.13.2\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-3\bb0da6daa198b190ee8dcf2ad93287fd\transformed\jetified-gifdecoder-4.13.2\AndroidManifest.xml:9:5-20
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4fa6ead3d9a5c128fcee92bb95f998fc\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4fa6ead3d9a5c128fcee92bb95f998fc\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:label
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:48-80
	android:roundIcon
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:116-161
	android:icon
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:81-115
	android:allowBackup
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:162-188
	android:theme
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:189-220
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:15:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:16:5-83
	android:value
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:16:60-81
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:16:16-59
meta-data#expo.modules.updates.EXPO_SDK_VERSION
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:17:5-93
	android:value
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:17:69-91
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:17:16-68
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:18:5-105
	android:value
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:18:81-103
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:18:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:19:5-99
	android:value
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:19:80-97
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:19:16-79
activity#com.cadetvocab.quiz.MainActivity
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:5-32:16
	android:screenOrientation
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:300-336
	android:label
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:44-76
	android:launchMode
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:155-186
	android:windowSoftInputMode
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:187-229
	android:exported
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:276-299
	android:configChanges
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:77-154
	android:theme
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:230-275
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:20:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:21:7-24:23
action#android.intent.action.MAIN
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:22:9-60
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:22:17-58
category#android.intent.category.LAUNCHER
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:23:9-68
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:23:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:com.cadetvocab.quiz+data:scheme:exp+cadetvocabquiz
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:25:7-31:23
category#android.intent.category.DEFAULT
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:27:9-67
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:27:19-65
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:33:5-106
	android:exported
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:33:80-104
	android:name
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml:33:15-79
uses-sdk
INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
MERGED from [:react-native-async-storage_async-storage] D:\CadetVocab - Copy\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] D:\CadetVocab - Copy\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_slider] D:\CadetVocab - Copy\node_modules\@react-native-community\slider\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_slider] D:\CadetVocab - Copy\node_modules\@react-native-community\slider\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo] D:\CadetVocab - Copy\node_modules\expo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo] D:\CadetVocab - Copy\node_modules\expo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\CadetVocab - Copy\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\CadetVocab - Copy\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\CadetVocab - Copy\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\CadetVocab - Copy\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] D:\CadetVocab - Copy\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] D:\CadetVocab - Copy\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] D:\CadetVocab - Copy\node_modules\expo-application\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] D:\CadetVocab - Copy\node_modules\expo-application\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] D:\CadetVocab - Copy\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] D:\CadetVocab - Copy\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] D:\CadetVocab - Copy\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] D:\CadetVocab - Copy\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] D:\CadetVocab - Copy\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:6:5-44
MERGED from [:expo-font] D:\CadetVocab - Copy\node_modules\expo-font\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-font] D:\CadetVocab - Copy\node_modules\expo-font\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] D:\CadetVocab - Copy\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] D:\CadetVocab - Copy\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:6:5-44
MERGED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:6:5-44
MERGED from [:expo-keep-awake] D:\CadetVocab - Copy\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] D:\CadetVocab - Copy\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] D:\CadetVocab - Copy\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] D:\CadetVocab - Copy\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:6:5-44
MERGED from [:expo-dev-menu-interface] D:\CadetVocab - Copy\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] D:\CadetVocab - Copy\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.72.6] C:\Users\<USER>\.gradle\caches\transforms-3\4115d94f0331b651ff9a931638b09a6b\transformed\jetified-react-android-0.72.6-release\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.72.6] C:\Users\<USER>\.gradle\caches\transforms-3\4115d94f0331b651ff9a931638b09a6b\transformed\jetified-react-android-0.72.6-release\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\156cc80b8e827be23a6f83eedea3693c\transformed\jetified-fresco-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fresco:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\156cc80b8e827be23a6f83eedea3693c\transformed\jetified-fresco-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\708563591f53d5a31de1906d830ad0e4\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\708563591f53d5a31de1906d830ad0e4\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-gif:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\42ea4c6e1a66a113079b1b4d69f83f54\transformed\jetified-animated-gif-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-gif:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\42ea4c6e1a66a113079b1b4d69f83f54\transformed\jetified-animated-gif-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:webpsupport:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa0420fa807ca9e64936073561133e90\transformed\jetified-webpsupport-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:webpsupport:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa0420fa807ca9e64936073561133e90\transformed\jetified-webpsupport-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.react:hermes-android:0.72.6] C:\Users\<USER>\.gradle\caches\transforms-3\7b52d1ae4d5c74a25ca6959d5bf0b4e2\transformed\jetified-hermes-android-0.72.6-release\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.72.6] C:\Users\<USER>\.gradle\caches\transforms-3\7b52d1ae4d5c74a25ca6959d5bf0b4e2\transformed\jetified-hermes-android-0.72.6-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2c95790bbae57615f21c79472117e61f\transformed\material-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2c95790bbae57615f21c79472117e61f\transformed\material-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8fc775a10259692585e5f093b80f9883\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8fc775a10259692585e5f093b80f9883\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bfb57a730389e05991612d1bc8a291e2\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bfb57a730389e05991612d1bc8a291e2\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b5aa25914fa2b78ed667de0ddd139f3f\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b5aa25914fa2b78ed667de0ddd139f3f\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a654646302f385221d786cebcdebb3\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a654646302f385221d786cebcdebb3\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f58386d8ce2cbfe1eff192583b03fb0\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f58386d8ce2cbfe1eff192583b03fb0\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3125dda4c537b5379be4b8059f12fd05\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3125dda4c537b5379be4b8059f12fd05\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\019468a1a1d0c86c3457ec29bd73c429\transformed\jetified-tracing-ktx-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing-ktx:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\019468a1a1d0c86c3457ec29bd73c429\transformed\jetified-tracing-ktx-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:glide:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-3\0a43de56c5261a993837e8c0dbd0df27\transformed\jetified-glide-4.13.2\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-3\0a43de56c5261a993837e8c0dbd0df27\transformed\jetified-glide-4.13.2\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.fragment:fragment-ktx:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\3f9978ed181811ff7376b8cb86863f17\transformed\jetified-fragment-ktx-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\3f9978ed181811ff7376b8cb86863f17\transformed\jetified-fragment-ktx-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\931d105cc246d52d3f08762f609d61bf\transformed\jetified-activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\931d105cc246d52d3f08762f609d61bf\transformed\jetified-activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f52e543fca820718464f2c82e71274f\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f52e543fca820718464f2c82e71274f\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ff927c381813b5e2b9b314f7d6c57346\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ff927c381813b5e2b9b314f7d6c57346\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\3fc7415478e4731dd9355d61543956b8\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\3fc7415478e4731dd9355d61543956b8\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\4dfec2d3282dc05e74257e73dac5bcbd\transformed\jetified-activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\4dfec2d3282dc05e74257e73dac5bcbd\transformed\jetified-activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e019764ccce31df5238fed8a7fd8a0b\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e019764ccce31df5238fed8a7fd8a0b\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\388d7a49ace0c78216797aa6078fd027\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\388d7a49ace0c78216797aa6078fd027\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\680cc59621be334d9f131292c916b416\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\680cc59621be334d9f131292c916b416\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a618a4ec6e9a0f031159e531016d8bad\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a618a4ec6e9a0f031159e531016d8bad\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\27abba2b290b4a6f70e4bbb29befb79f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\27abba2b290b4a6f70e4bbb29befb79f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\36dc0d2607f0871053495442ce0bdfcc\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\36dc0d2607f0871053495442ce0bdfcc\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6062a715f5a47472ef2162d3759bcdd0\transformed\browser-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6062a715f5a47472ef2162d3759bcdd0\transformed\browser-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5548385058946391f436f92302dcc195\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5548385058946391f436f92302dcc195\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\446db2806f88813e5f9b4e2af57b1330\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\446db2806f88813e5f9b4e2af57b1330\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc45c20841ea83600d34b10ff3d24920\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc45c20841ea83600d34b10ff3d24920\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fcb8f471d2218871705213dafab8a687\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fcb8f471d2218871705213dafab8a687\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f65a7f778b1c6e118cd2a5cf8624e92\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f65a7f778b1c6e118cd2a5cf8624e92\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e46f44a0276ba8aafbcfba1cd5c69733\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e46f44a0276ba8aafbcfba1cd5c69733\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\37315463ce468fae5a77bd90c79d2ce3\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\37315463ce468fae5a77bd90c79d2ce3\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd1dc4f00037cece7752e3dc78a9a6fa\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd1dc4f00037cece7752e3dc78a9a6fa\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bdf6b0bf865be6d537eba5d61fa0e42\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bdf6b0bf865be6d537eba5d61fa0e42\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\356767d41a07ee2a91639a8f928dc465\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\356767d41a07ee2a91639a8f928dc465\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a1d09347030c2d407badbecd54e1c2a\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a1d09347030c2d407badbecd54e1c2a\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\74f887f2d992a24d7a2a9bb57befd934\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\74f887f2d992a24d7a2a9bb57befd934\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f7ee4c3c3649e24a15110985e00a4f7\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f7ee4c3c3649e24a15110985e00a4f7\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eb4dbdbd7c7cbc5881d361f54e0425c1\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eb4dbdbd7c7cbc5881d361f54e0425c1\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7762cadebcbb43f1c3c494b3fca017db\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7762cadebcbb43f1c3c494b3fca017db\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\665343db0ab5b61bdccec606bcb72b4a\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\665343db0ab5b61bdccec606bcb72b4a\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f087c01a1d51cb2a0941640443975f6a\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f087c01a1d51cb2a0941640443975f6a\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\49389fe05353e9e81c6c6ad561e8629c\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\49389fe05353e9e81c6c6ad561e8629c\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9dee590854a00291633c98ff8fe368\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9dee590854a00291633c98ff8fe368\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9fe2a41276fce311cda608511fbdd69b\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9fe2a41276fce311cda608511fbdd69b\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad11f7f0dbf15cc0a31ab383e6f0c7c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad11f7f0dbf15cc0a31ab383e6f0c7c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a099fac719a29a6e56fc26417453923e\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a099fac719a29a6e56fc26417453923e\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fbjni:fbjni:0.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8ff2350ef33c7e890a553e73e65eb5\transformed\jetified-fbjni-0.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fbjni:fbjni:0.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b8ff2350ef33c7e890a553e73e65eb5\transformed\jetified-fbjni-0.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-base:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\62168bfc7de2695ff9dd2159c899a5da\transformed\jetified-animated-base-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-base:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\62168bfc7de2695ff9dd2159c899a5da\transformed\jetified-animated-base-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-drawable:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2492b52a9e204f5550cb4c834a4ca36c\transformed\jetified-animated-drawable-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-drawable:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2492b52a9e204f5550cb4c834a4ca36c\transformed\jetified-animated-drawable-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8bfb6d3e02b19f2d01d43ee6c3a793b\transformed\jetified-drawee-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8bfb6d3e02b19f2d01d43ee6c3a793b\transformed\jetified-drawee-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:middleware:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\71d7f704609524711cb8dfa845a61119\transformed\jetified-middleware-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:middleware:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\71d7f704609524711cb8dfa845a61119\transformed\jetified-middleware-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\df050fcf2b47cf37bdedf1566e952bfe\transformed\jetified-ui-common-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\df050fcf2b47cf37bdedf1566e952bfe\transformed\jetified-ui-common-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c1317cc5096b21074f2ffb700914b78\transformed\jetified-nativeimagefilters-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c1317cc5096b21074f2ffb700914b78\transformed\jetified-nativeimagefilters-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1de27ebd63d53c54e8b5a351e4db6e9c\transformed\jetified-memory-type-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1de27ebd63d53c54e8b5a351e4db6e9c\transformed\jetified-memory-type-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f95b8d3ac0a210f8a663c3c13b08176\transformed\jetified-memory-type-java-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f95b8d3ac0a210f8a663c3c13b08176\transformed\jetified-memory-type-java-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\26468ca1e8bcd6fc1a98d799567f85a2\transformed\jetified-imagepipeline-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\26468ca1e8bcd6fc1a98d799567f85a2\transformed\jetified-imagepipeline-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\b23291a3572e2ad52d5a701c18ec7520\transformed\jetified-memory-type-ashmem-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\b23291a3572e2ad52d5a701c18ec7520\transformed\jetified-memory-type-ashmem-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6bf6755a84cc53add610ec5742230ad9\transformed\jetified-imagepipeline-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6bf6755a84cc53add610ec5742230ad9\transformed\jetified-imagepipeline-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\be38081d50c9033aa72a21e7b57f5f87\transformed\jetified-nativeimagetranscoder-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\be38081d50c9033aa72a21e7b57f5f87\transformed\jetified-nativeimagetranscoder-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab0524f3419901175c6809ae5b5e9cac\transformed\jetified-imagepipeline-base-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab0524f3419901175c6809ae5b5e9cac\transformed\jetified-imagepipeline-base-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:soloader:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4f01c0ad0089eb7aa6682ec6997faa3\transformed\jetified-soloader-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:soloader:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4f01c0ad0089eb7aa6682ec6997faa3\transformed\jetified-soloader-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\ed97f8b44ced4959eaa5e6c1cfa2c0c8\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\ed97f8b44ced4959eaa5e6c1cfa2c0c8\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [:expo-dev-client] D:\CadetVocab - Copy\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] D:\CadetVocab - Copy\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9de9d7e9c63cab3e93d43532ac2dfc0\transformed\jetified-fbcore-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fbcore:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9de9d7e9c63cab3e93d43532ac2dfc0\transformed\jetified-fbcore-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba9616361a748d9cdeb40c9f673d0ba5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba9616361a748d9cdeb40c9f673d0ba5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4709c8cba410ac406ee6c4772d795ca6\transformed\jetified-viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4709c8cba410ac406ee6c4772d795ca6\transformed\jetified-viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\cba7334e691c68d9c84ada86f43b2db6\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\cba7334e691c68d9c84ada86f43b2db6\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c8d2f2de7a0a96e39bb78957cbf4b94b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c8d2f2de7a0a96e39bb78957cbf4b94b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\509cad9e024e34683798c3572b16898f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\509cad9e024e34683798c3572b16898f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-3\bb0da6daa198b190ee8dcf2ad93287fd\transformed\jetified-gifdecoder-4.13.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-3\bb0da6daa198b190ee8dcf2ad93287fd\transformed\jetified-gifdecoder-4.13.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\357d8662b6a39a477d8ca0dba99ca9a9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\357d8662b6a39a477d8ca0dba99ca9a9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f87173cf67f339b39bd12524d493b97a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f87173cf67f339b39bd12524d493b97a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\852a0494fe875070e33262d714741b8c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\852a0494fe875070e33262d714741b8c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\983f915e781ebb6b2906b60d1f8b5829\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\983f915e781ebb6b2906b60d1f8b5829\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1133259983c67cf09649b4496d5fee52\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1133259983c67cf09649b4496d5fee52\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [:expo-manifests] D:\CadetVocab - Copy\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] D:\CadetVocab - Copy\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] D:\CadetVocab - Copy\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] D:\CadetVocab - Copy\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] D:\CadetVocab - Copy\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] D:\CadetVocab - Copy\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60439acb367ee91ded2be0e83721b8e8\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60439acb367ee91ded2be0e83721b8e8\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4fa6ead3d9a5c128fcee92bb95f998fc\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4fa6ead3d9a5c128fcee92bb95f998fc\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
		ADDED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\CadetVocab - Copy\android\app\src\main\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:react-native-community_netinfo] D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [:react-native-community_netinfo] D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:react-native-community_netinfo] D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:5-76
	android:name
		ADDED from [:react-native-community_netinfo] D:\CadetVocab - Copy\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:22-73
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:22:13-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:27:13-29:70
	android:resource
		ADDED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:29:17-67
	android:name
		ADDED from [:expo-file-system] D:\CadetVocab - Copy\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:28:17-67
uses-permission#android.permission.CAMERA
ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:5-65
	android:name
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:22-62
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:5-76
	android:name
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:5-75
	android:name
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:22-72
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:17:9-21:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:20:13-73
	android:name
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:20:21-70
intent#action:name:android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:22:9-26:18
action#android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:13-80
	android:name
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:21-77
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:30:9-42:19
	android:enabled
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:32:13-36
	android:exported
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:33:13-37
	tools:ignore
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:34:13-40
	android:name
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:31:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:35:13-37:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:36:17-94
	android:name
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:36:25-91
meta-data#photopicker_activity:0:required
ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:39:13-41:36
	android:value
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:41:17-33
	android:name
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:40:17-63
activity#com.canhub.cropper.CropImageActivity
ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:44:9-46:59
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:33:9-35:39
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:33:9-35:39
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:46:13-56
	android:name
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:45:13-64
provider#expo.modules.imagepicker.fileprovider.ImagePickerFileProvider
ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:48:9-56:20
	android:grantUriPermissions
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:52:13-47
	android:authorities
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:50:13-75
	android:exported
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:51:13-37
	android:name
		ADDED from [:expo-image-picker] D:\CadetVocab - Copy\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:49:13-89
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\ed97f8b44ced4959eaa5e6c1cfa2c0c8\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\ed97f8b44ced4959eaa5e6c1cfa2c0c8\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\ed97f8b44ced4959eaa5e6c1cfa2c0c8\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] D:\CadetVocab - Copy\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:13-57
intent#action:name:android.intent.action.GET_CONTENT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
category#android.intent.category.OPENABLE
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
provider#com.canhub.cropper.CropFileProvider
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
	android:grantUriPermissions
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
	android:authorities
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bcb008d0d21366cb948c612bcf9fae1d\transformed\jetified-Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad11f7f0dbf15cc0a31ab383e6f0c7c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad11f7f0dbf15cc0a31ab383e6f0c7c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db3c11bab3ba2a324b0325c1fa29ef2f\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.cadetvocab.quiz.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.cadetvocab.quiz.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d930b3380c76b7657baee5387c2188a\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3cb99c8228f7b004acc305315f9f395\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd0619271537d84f07f6b6e89c9c818c\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4fa6ead3d9a5c128fcee92bb95f998fc\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4fa6ead3d9a5c128fcee92bb95f998fc\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
