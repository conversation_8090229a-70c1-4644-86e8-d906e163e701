import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, TouchableOpacity, StatusBar, ScrollView, Alert } from 'react-native';
import { Text, ActivityIndicator, ProgressBar } from 'react-native-paper';
import { useNavigation, useRoute, CommonActions } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { RouteProp } from '@react-navigation/native';
import { RootStackParamList, QuizSet, QuizQuestion } from '../types';
import { quizService } from '../services/quizService';
import { optimizedProgressService } from '../services/optimizedProgressService';
import { performanceMonitor } from '../services/performanceMonitorService';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../theme/colors';
import { getAuth } from 'firebase/auth';
import AuthModal from '../components/AuthModal';
import { useProgressCache } from '../hooks/useProgressCache';
import { networkService } from '../services/networkService';
import OfflineError from '../components/OfflineError';
import { bookmarkService } from '../services/bookmarkService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { dailyChallengeService } from '../services/dailyChallengeService';
import { userService } from '../services/userService';

type QuizScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;
type QuizScreenRouteProp = RouteProp<RootStackParamList, 'Quiz'>;

const QUESTION_TIME = 30; // 30 seconds per question

interface RecentQuiz {
  id: string;
  title: string;
  difficulty: string;
  wordCount: number;
  timestamp: number;
}

const RECENT_QUIZZES_KEY = 'recent_quizzes';
const MAX_RECENT_QUIZZES = 3;

const QuizScreen = () => {
  const navigation = useNavigation<QuizScreenNavigationProp>();
  const route = useRoute<QuizScreenRouteProp>();
  const { quizSetId, isDailyChallenge, dailyQuestion, isBookmarkQuestion, bookmarkedQuestion } = route.params;
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quizSet, setQuizSet] = useState<QuizSet | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [startTime] = useState(Date.now());
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [selectedAnswer, setSelectedAnswer] = useState<string>('');
  const [isAnswerSubmitted, setIsAnswerSubmitted] = useState(false);
  const [score, setScore] = useState(0);
  const [timeLeft, setTimeLeft] = useState(QUESTION_TIME);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [quizCompleteData, setQuizCompleteData] = useState<any>(null);
  const [isOnline, setIsOnline] = useState(true);
  const { updateProgress } = useProgressCache();

  const loadQuiz = useCallback(async () => {
    try {
      setLoading(true);
      const baseQuiz = await quizService.getQuizSet(quizSetId);
      if (!baseQuiz) {
        setError('Quiz not found');
        setLoading(false);
        return;
      }

      if (isDailyChallenge && dailyQuestion) {
        const dailyQuiz: QuizSet = {
          ...baseQuiz,
          questions: [dailyQuestion],
          title: 'Daily Challenge',
          description: "Today's vocabulary challenge",
          wordCount: 1,
          icon: 'star',
          category: 'Daily',
          progress: 0,
          difficulty: baseQuiz.difficulty,
        };
        setQuizSet(dailyQuiz);
        setQuestions([dailyQuestion]);
      } else if (isBookmarkQuestion && bookmarkedQuestion) {
        const bookmarkQuiz: QuizSet = {
          ...baseQuiz,
          questions: [bookmarkedQuestion],
          title: 'Bookmarked Question',
          description: 'Your bookmarked question',
          wordCount: 1,
          icon: 'bookmark',
          category: 'Bookmark',
          progress: 0,
          difficulty: baseQuiz.difficulty,
        };
        setQuizSet(bookmarkQuiz);
        setQuestions([bookmarkedQuestion]);
      } else {
        setQuizSet(baseQuiz);
        setQuestions(baseQuiz.questions);
      }

      setLoading(false);
    } catch (err) {
      console.error('Error loading quiz:', err);
      setError('Failed to load quiz');
      setLoading(false);
    }
  }, [quizSetId, isDailyChallenge, dailyQuestion, isBookmarkQuestion, bookmarkedQuestion]);

  useEffect(() => {
    loadQuiz();
  }, [loadQuiz]);

  useEffect(() => {
    // Check initial network state
    networkService.isOnline().then(setIsOnline);
    
    // Add network listener
    const unsubscribe = networkService.addListener((state) => {
      setIsOnline(state.isConnected && state.isInternetReachable === true);
    });
    
    return unsubscribe;
  }, []);

  useEffect(() => {
    if (!quizSet || !quizSet.questions || quizSet.questions.length === 0) return;

    // Reset timer when moving to next question
    setTimeLeft(QUESTION_TIME);

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 0) {
          clearInterval(timer);
          if (!isAnswerSubmitted) {
            handleSubmitAnswer(); // Auto-submit when time runs out
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [quizSet, currentQuestionIndex]);

  const getProgressPercentage = () => {
    if (!quizSet?.questions) return 0;
    return currentQuestionIndex / quizSet.questions.length;
  };

  const formatTime = (seconds: number) => {
    return seconds.toString().padStart(2, '0');
  };

  const handleExit = () => {
    Alert.alert(
      'Exit Quiz',
      'Are you sure you want to exit? Your progress will be lost.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Exit',
          style: 'destructive',
          onPress: () => {
            console.log('[QuizScreen] Exiting quiz...');
            // Reset quiz state
            setCurrentQuestionIndex(0);
            setSelectedAnswer('');
            setIsAnswerSubmitted(false);
            setScore(0);
            setTimeLeft(QUESTION_TIME);
            
            // Navigate back using CommonActions to ensure proper stack cleanup
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [{ name: 'Main' }],
              })
            );
          },
        },
      ]
    );
  };

  const handleAnswerSelect = (answer: string) => {
    if (!isAnswerSubmitted) {
      setSelectedAnswer(answer);
    }
  };

  const handleSubmitAnswer = () => {
    if (!quizSet?.questions) return;
    
    const currentQuestion = quizSet.questions[currentQuestionIndex];
    if (!currentQuestion) return;

    if (!selectedAnswer) {
      Alert.alert('Error', 'Please select an answer before submitting.');
      return;
    }

    setIsAnswerSubmitted(true);
    if (selectedAnswer === currentQuestion.correctAnswer) {
      setScore(prev => prev + 1);
    }
  };

  const handleNextQuestion = () => {
    if (!quizSet?.questions) return;

    if (currentQuestionIndex < quizSet.questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      setSelectedAnswer('');
      setIsAnswerSubmitted(false);
    } else {
      handleQuizComplete();
    }
  };

  const handleQuizComplete = async () => {
    if (!quizSet) return;

    if (isBookmarkQuestion) {
      navigation.goBack();
      return;
    }

    const completeData = {
      score,
      totalQuestions: quizSet.questions.length,
      quizTitle: quizSet.title || 'Quiz',
      timeSpent: Math.floor((Date.now() - startTime) / 1000),
      difficulty: quizSet.difficulty,
      quizSetId: route.params.quizSetId,
    };

    const user = getAuth().currentUser;

    if (!user) {
      setQuizCompleteData(completeData);
      setShowAuthModal(true);
      return;
    }

    try {
      console.log('🚀 [QuizScreen] Starting optimized quiz completion...');

      // 🚀 OPTIMIZED: Use batch submission for all quiz results
      await performanceMonitor.trackOperation(
        'quiz_completion',
        async () => {
          // Use optimized batch submission (3 writes → 1 write)
          await quizService.submitQuizResultsBatch({
            quizId: quizSetId,
            score: completeData.score,
            totalQuestions: completeData.totalQuestions,
            timeSpent: completeData.timeSpent,
            difficulty: completeData.difficulty,
          });

          // Update progress with offline support (only for authenticated users)
          if (!isDailyChallenge && user?.uid) {
            await optimizedProgressService.updateProgress(
              user.uid,
              {
                quizId: quizSetId,
                score: completeData.score,
                totalQuestions: completeData.totalQuestions,
                completedAt: new Date().toISOString(),
                timeSpent: completeData.timeSpent,
                difficulty: completeData.difficulty,
              },
              false // Use background sync for better performance
            );
          } else if (!user?.uid) {
            console.log('[QuizScreen] 👤 Skipping progress update - user not authenticated');
          }

          // Update user stats (this will be queued for background sync)
          if (user?.uid) {
            await userService.updateQuizStats(user.uid, {
              score: completeData.score,
              totalQuestions: completeData.totalQuestions,
              difficulty: completeData.difficulty,
              quizSetId: quizSetId,
            });
          }

          if (isDailyChallenge) {
            await dailyChallengeService.markChallengeCompleted();
          }
        },
        {
          trackFirebaseWrites: 1, // Batch write counts as 1
        }
      );

      console.log('✅ [QuizScreen] Quiz completion optimized successfully');
      navigation.replace('QuizComplete', completeData);
    } catch (error) {
      console.error('❌ [QuizScreen] Error completing quiz:', error);
      setError('Failed to save quiz results');
    }
  };

  const handleAuthSuccess = () => {
    // Close the auth modal and let the user tap "Finish Quiz" again to submit
    setShowAuthModal(false);
  };

  const currentBookmarkId = () => {
    if (!quizSet?.id || !quizSet?.questions) return '';
    const q = quizSet.questions[currentQuestionIndex];
    if (!q) return '';
    return `${quizSet.id}_${q.id}`;
  };

  const checkBookmarkStatus = useCallback(async () => {
    const id = currentBookmarkId();
    if (!id) return;
    const status = await bookmarkService.isBookmarked(id);
    setIsBookmarked(status);
  }, [quizSet, currentQuestionIndex]);

  useEffect(() => {
    checkBookmarkStatus();
  }, [checkBookmarkStatus]);

  const toggleBookmark = async () => {
    if (!quizSet?.questions) return;
    const q = quizSet.questions[currentQuestionIndex];
    const id = currentBookmarkId();
    if (!id) return;
    if (isBookmarked) {
      await bookmarkService.removeBookmark(id);
      setIsBookmarked(false);
    } else {
      await bookmarkService.addBookmark({
        id,
        quizSetId: quizSet.id,
        question: q,
        addedAt: Date.now(),
      });
      setIsBookmarked(true);
    }
  };

  const pushToRecentQuizzes = useCallback(async () => {
    if (!quizSet) return;
    try {
      const newItem: RecentQuiz = {
        id: quizSet.id,
        title: quizSet.title,
        difficulty: quizSet.difficulty || 'intermediate',
        wordCount: quizSet.questions?.length || 0,
        timestamp: Date.now(),
      };

      const stored = await AsyncStorage.getItem(RECENT_QUIZZES_KEY);
      const list: RecentQuiz[] = stored ? JSON.parse(stored) : [];
      const existingIndex = list.findIndex(q => q.id === newItem.id);
      if (existingIndex > -1) list.splice(existingIndex, 1);
      list.unshift(newItem);
      const trimmed = list.slice(0, MAX_RECENT_QUIZZES);
      await AsyncStorage.setItem(RECENT_QUIZZES_KEY, JSON.stringify(trimmed));
    } catch (e) {
      console.warn('Failed to update recent quizzes:', e);
    }
  }, [quizSet]);

  useEffect(() => {
    if (quizSet) {
      pushToRecentQuizzes();
    }
  }, [quizSet, pushToRecentQuizzes]);

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        {!isOnline ? (
          <OfflineError 
            message="Unable to load quiz. Please check your internet connection."
            onRetry={loadQuiz}
          />
        ) : (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Loading quiz...</Text>
          </View>
        )}
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        {!isOnline ? (
          <OfflineError 
            message="Unable to load quiz. Please check your internet connection."
            onRetry={loadQuiz}
          />
        ) : (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={loadQuiz}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  }

  if (!quizSet || !quizSet.questions || currentQuestionIndex >= quizSet.questions.length) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>No questions available.</Text>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const currentQuestion = quizSet.questions[currentQuestionIndex];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Top Header with Exit Button */}
      <View style={styles.header}>
        <TouchableOpacity 
          onPress={handleExit} 
          style={styles.exitButton}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Icon name="close" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.quizTitle}>{quizSet.title || 'Quiz'}</Text>
        <TouchableOpacity onPress={toggleBookmark} style={styles.exitButton}>
          <Icon
            name={isBookmarked ? 'bookmark' : 'bookmark-border'}
            size={24}
            color={colors.primary}
          />
        </TouchableOpacity>
      </View>

      {/* Timer and Progress Section */}
      <View style={styles.statusSection}>
        <View style={styles.timerContainer}>
          <Icon 
            name="timer" 
            size={24} 
            color={timeLeft <= 5 ? colors.status.error : colors.text.primary} 
          />
          <Text style={[
            styles.timerText,
            timeLeft <= 5 && styles.timerWarning
          ]}>
            {formatTime(timeLeft)}
          </Text>
        </View>
        <Text style={styles.questionCounter}>
          Question {currentQuestionIndex + 1}/{quizSet.questions.length}
        </Text>
      </View>

      <View style={styles.progressContainer}>
        <ProgressBar
          progress={getProgressPercentage()}
          color={colors.primary}
          style={styles.progressBar}
        />
      </View>

      {/* Question Content */}
      <ScrollView 
        style={styles.content} 
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.defineText}>Define the term</Text>
        <Text style={styles.questionText}>{currentQuestion.question}</Text>
        <View style={styles.optionsContainer}>
        {currentQuestion.options.map((option, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.optionButton,
              selectedAnswer === option && styles.selectedOption,
              isAnswerSubmitted && option === currentQuestion.correctAnswer && styles.correctOption,
                isAnswerSubmitted && selectedAnswer === option && selectedAnswer !== currentQuestion.correctAnswer && styles.wrongOption,
            ]}
            onPress={() => handleAnswerSelect(option)}
            disabled={isAnswerSubmitted}
          >
            <Text style={[
              styles.optionText,
              selectedAnswer === option && styles.selectedOptionText,
              isAnswerSubmitted && option === currentQuestion.correctAnswer && styles.correctOptionText,
                isAnswerSubmitted && selectedAnswer === option && selectedAnswer !== currentQuestion.correctAnswer && styles.wrongOptionText,
            ]}>
              {option}
            </Text>
            {isAnswerSubmitted && option === currentQuestion.correctAnswer && (
              <View style={styles.correctAnswerIndicator}>
                <Icon name="check-circle" size={20} color={colors.status.success} />
                <Text style={styles.correctAnswerText}>Correct Answer</Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
        </View>

        {/* Solution Section */}
        {isAnswerSubmitted && currentQuestion.solution && (
          <View style={styles.solutionContainer}>
            <Text style={styles.solutionTitle}>Solution:</Text>
            <Text style={styles.solutionText}>
              {currentQuestion.solution}
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Bottom Action Button */}
      <View style={styles.bottomContainer}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            !selectedAnswer && !isAnswerSubmitted && styles.disabledButton,
            isAnswerSubmitted && styles.nextButton
          ]}
          onPress={isAnswerSubmitted ? handleNextQuestion : handleSubmitAnswer}
          disabled={!selectedAnswer && !isAnswerSubmitted}
        >
          <Text style={styles.actionButtonText}>
            {isAnswerSubmitted ? 
              (currentQuestionIndex === quizSet.questions.length - 1 ? 'Finish Quiz' : 'Next Question') : 
              'Submit Answer'
            }
          </Text>
        </TouchableOpacity>
      </View>

      <AuthModal
        visible={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onSuccess={handleAuthSuccess}
        title="Login to Submit Quiz"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.secondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: StatusBar.currentHeight || 16,
    paddingBottom: 16,
  },
  exitButton: {
    padding: 8,
  },
  quizTitle: {
    color: colors.text.primary,
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 40, // Same width as exit button for balance
  },
  statusSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.dark.medium,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  timerText: {
    color: colors.text.primary,
    marginLeft: 8,
    fontWeight: 'bold',
    fontSize: 18,
  },
  timerWarning: {
    color: colors.status.error,
  },
  questionCounter: {
    color: colors.text.secondary,
    fontSize: 16,
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  questionText: {
    color: colors.text.primary,
    fontSize: 27,
    fontWeight: '600',
    marginBottom: 24,
    lineHeight: 28,
  },
  defineText: {
    color: colors.text.secondary,
    fontSize: 13,
    marginBottom: 8,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  optionsContainer: {
    gap: 12,
  },
  optionButton: {
    backgroundColor: colors.dark.light,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.dark.border,
  },
  selectedOption: {
    backgroundColor: colors.primary + '20',
    borderColor: colors.primary,
  },
  correctOption: {
    backgroundColor: colors.status.success + '20',
    borderColor: colors.status.success,
  },
  wrongOption: {
    backgroundColor: colors.status.error + '20',
    borderColor: colors.status.error,
  },
  optionText: {
    color: colors.text.primary,
    fontSize: 16,
  },
  selectedOptionText: {
    color: colors.primary,
  },
  correctOptionText: {
    color: colors.status.success,
  },
  wrongOptionText: {
    color: colors.status.error,
  },
  bottomContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.dark.border,
  },
  actionButton: {
    backgroundColor: colors.primary,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  actionButtonText: {
    color: colors.text.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.5,
  },
  nextButton: {
    backgroundColor: colors.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: colors.text.primary,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: colors.status.error,
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: colors.primary,
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  retryButtonText: {
    color: colors.text.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  backButton: {
    padding: 12,
  },
  backButtonText: {
    color: colors.text.secondary,
    fontSize: 16,
  },
  correctAnswerIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  correctAnswerText: {
    color: colors.status.success,
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  solutionContainer: {
    backgroundColor: colors.dark.medium,
    padding: 16,
    borderRadius: 12,
    marginTop: 24,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  solutionTitle: {
    color: colors.primary,
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  solutionText: {
    color: colors.text.primary,
    fontSize: 14,
    lineHeight: 20,
  },
});

export default QuizScreen; 