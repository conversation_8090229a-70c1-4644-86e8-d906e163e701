import AsyncStorage from '@react-native-async-storage/async-storage';
import { QuizQuestion } from '../types';

const STORAGE_KEY = 'bookmarked_questions';

export interface BookmarkedQuestion {
  id: string; // unique id
  quizSetId: string;
  question: QuizQuestion;
  addedAt: number;
}

export const bookmarkService = {
  async getBookmarks(): Promise<BookmarkedQuestion[]> {
    try {
      const data = await AsyncStorage.getItem(STORAGE_KEY);
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  },

  async isBookmarked(id: string): Promise<boolean> {
    const list = await this.getBookmarks();
    return list.some(b => b.id === id);
  },

  async addBookmark(item: BookmarkedQuestion): Promise<void> {
    const list = await this.getBookmarks();
    if (list.some(b => b.id === item.id)) return;
    list.push(item);
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(list));
  },

  async removeBookmark(id: string): Promise<void> {
    const list = await this.getBookmarks();
    const newList = list.filter(b => b.id !== id);
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(newList));
  },
};