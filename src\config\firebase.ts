import { Platform } from 'react-native';
// Note: Analytics is not supported in React Native by default
// import { getAnalytics } from 'firebase/analytics';

import { initializeApp, getApps, getApp } from 'firebase/app';
import {
  initializeAuth,
  getAuth,
  browserLocalPersistence,
  setPersistence,
} from 'firebase/auth';
import { getFirestore, initializeFirestore, CACHE_SIZE_UNLIMITED, enableIndexedDbPersistence } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Your web app's Firebase configuration
export const firebaseConfig = {
  apiKey: "AIzaSyDJbMKkDPHRxUo1luvFzKkeG_te3SjWqGk",
  authDomain: "cadetvocab.firebaseapp.com",
  projectId: "cadetvocab",
  storageBucket: "cadetvocab.firebasestorage.app",
  messagingSenderId: "1018168044752",
  appId: "1:1018168044752:web:1c5eb10d5018f3069382d5",
  measurementId: "G-VECGVPX5Q9"
};

// Initialize Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApp();

/* ----- Auth with proper persistence ----- */
let auth = getAuth(app);

if (Platform.OS === 'web') {
  // set browser persistence once
  setPersistence(auth, browserLocalPersistence).catch(console.warn);
} else {
  try {
    // Dynamically require to avoid bundling for web and TS errors
    let { getReactNativePersistence } = require('firebase/auth');
    if (getReactNativePersistence) {
      auth = initializeAuth(app, {
        persistence: getReactNativePersistence(AsyncStorage),
      });
    } else {
      console.warn('getReactNativePersistence not found – falling back to memory persistence');
      auth = getAuth(app);
    }
  } catch (e: any) {
    if (e?.code !== 'auth/already-initialized') {
      console.warn('Auth init warning:', e);
    }
    // use existing
    auth = getAuth(app);
  }
}

export { auth };

// Initialize Firestore with settings
export const db = initializeFirestore(app, {
  experimentalForceLongPolling: Platform.OS === 'android',
  cacheSizeBytes: CACHE_SIZE_UNLIMITED,
});

if (Platform.OS === 'web') {
  enableIndexedDbPersistence(db)
    .catch((err) => {
      if (err.code == 'failed-precondition') {
        console.warn(
          'Multiple tabs open, persistence can only be enabled in one tab at a time.'
        );
      } else if (err.code == 'unimplemented') {
        console.warn(
          'The current browser does not support all of the features required to enable persistence.'
        );
      }
    });
}

// Initialize Storage
export const storage = getStorage(app);

// Note: Analytics is not supported in React Native by default
// const analytics = getAnalytics(app);

export default app; 