const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const ASSETS_DIR = path.join(__dirname, '../assets');

// Ensure assets directory exists
if (!fs.existsSync(ASSETS_DIR)) {
  fs.mkdirSync(ASSETS_DIR, { recursive: true });
}

// Source logo path
const SOURCE_LOGO = path.join(__dirname, '../assets/source-logo.png');

// Asset configurations
const assets = [
  {
    name: 'icon.png',
    width: 1024,
    height: 1024,
    background: { r: 255, g: 255, b: 255, alpha: 0 }
  },
  {
    name: 'adaptive-icon.png',
    width: 1024,
    height: 1024,
    background: { r: 255, g: 255, b: 255, alpha: 0 }
  },
  {
    name: 'splash.png',
    width: 2048,
    height: 2048,
    background: { r: 255, g: 255, b: 255, alpha: 1 }
  },
  {
    name: 'favicon.png',
    width: 48,
    height: 48,
    background: { r: 255, g: 255, b: 255, alpha: 0 }
  }
];

async function generateAssets() {
  try {
    // Load the source image
    const image = sharp(SOURCE_LOGO);
    
    // Generate each asset
    for (const asset of assets) {
      console.log(`Generating ${asset.name}...`);
      
      await image
        .resize({
          width: asset.width,
          height: asset.height,
          fit: 'contain',
          background: asset.background
        })
        .toFile(path.join(ASSETS_DIR, asset.name));
    }
    
    console.log('Asset generation complete!');
  } catch (error) {
    console.error('Error generating assets:', error);
    process.exit(1);
  }
}

generateAssets(); 