/**
 * 🚀 Performance Dashboard Component
 * 
 * Shows real-time performance metrics for development and monitoring
 * Only visible in development mode or for admin users
 */

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Modal } from 'react-native';
import { performanceMonitor } from '../services/performanceMonitorService';
import { backgroundSyncService } from '../services/backgroundSyncService';
import { getCacheStats } from '../utils/cache';
import { colors } from '../theme/colors';

interface PerformanceDashboardProps {
  visible: boolean;
  onClose: () => void;
}

export const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  visible,
  onClose,
}) => {
  const [metrics, setMetrics] = useState<any>(null);
  const [syncStatus, setSyncStatus] = useState<any>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [recentOps, setRecentOps] = useState<any[]>([]);

  useEffect(() => {
    if (visible) {
      loadMetrics();
      const interval = setInterval(loadMetrics, 2000); // Update every 2 seconds
      return () => clearInterval(interval);
    }
  }, [visible]);

  const loadMetrics = async () => {
    try {
      const [perfMetrics, sync, cache] = await Promise.all([
        performanceMonitor.getMetrics(),
        backgroundSyncService.getSyncStatus(),
        getCacheStats(),
      ]);

      setMetrics(perfMetrics);
      setSyncStatus(sync);
      setCacheStats(cache);
      setRecentOps(performanceMonitor.getRecentOperations(10));
    } catch (error) {
      console.error('Error loading performance metrics:', error);
    }
  };

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const formatBytes = (bytes: number): string => {
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
  };

  const getEfficiencyColor = (rate: number): string => {
    if (rate >= 80) return colors.success;
    if (rate >= 60) return colors.warning;
    return colors.error;
  };

  if (!visible || !metrics) return null;

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>🚀 Performance Dashboard</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeText}>✕</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          {/* Firebase Operations */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🔥 Firebase Operations</Text>
            <View style={styles.row}>
              <Text style={styles.label}>Reads:</Text>
              <Text style={[styles.value, { color: colors.primary }]}>
                {metrics.firebaseReads}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Writes:</Text>
              <Text style={[styles.value, { color: colors.secondary }]}>
                {metrics.firebaseWrites}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Efficiency:</Text>
              <Text style={[styles.value, { color: getEfficiencyColor(metrics.firebaseEfficiency * 100) }]}>
                {(metrics.firebaseEfficiency * 100).toFixed(1)}%
              </Text>
            </View>
          </View>

          {/* Cache Performance */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>💾 Cache Performance</Text>
            <View style={styles.row}>
              <Text style={styles.label}>Hit Rate:</Text>
              <Text style={[styles.value, { color: getEfficiencyColor(metrics.cacheHitRate) }]}>
                {metrics.cacheHitRate.toFixed(1)}%
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Hits:</Text>
              <Text style={[styles.value, { color: colors.success }]}>
                {metrics.cacheHits}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Misses:</Text>
              <Text style={[styles.value, { color: colors.error }]}>
                {metrics.cacheMisses}
              </Text>
            </View>
            {cacheStats && (
              <>
                <View style={styles.row}>
                  <Text style={styles.label}>Total Keys:</Text>
                  <Text style={styles.value}>{cacheStats.totalKeys}</Text>
                </View>
                <View style={styles.row}>
                  <Text style={styles.label}>Cache Size:</Text>
                  <Text style={styles.value}>{formatBytes(cacheStats.totalSize)}</Text>
                </View>
              </>
            )}
          </View>

          {/* Background Sync */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🔄 Background Sync</Text>
            <View style={styles.row}>
              <Text style={styles.label}>Status:</Text>
              <Text style={[styles.value, { 
                color: syncStatus?.isOnline ? colors.success : colors.error 
              }]}>
                {syncStatus?.isOnline ? 'Online' : 'Offline'}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Pending:</Text>
              <Text style={[styles.value, { 
                color: syncStatus?.pendingWrites > 0 ? colors.warning : colors.success 
              }]}>
                {syncStatus?.pendingWrites || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Syncing:</Text>
              <Text style={[styles.value, { 
                color: syncStatus?.isSyncing ? colors.warning : colors.text 
              }]}>
                {syncStatus?.isSyncing ? 'Yes' : 'No'}
              </Text>
            </View>
          </View>

          {/* Performance Metrics */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>⚡ Performance</Text>
            <View style={styles.row}>
              <Text style={styles.label}>Avg Response:</Text>
              <Text style={styles.value}>
                {metrics.averageResponseTime.toFixed(0)}ms
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Ops/Min:</Text>
              <Text style={styles.value}>
                {metrics.operationsPerMinute.toFixed(1)}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Session:</Text>
              <Text style={styles.value}>
                {formatDuration(metrics.sessionDuration)}
              </Text>
            </View>
            {metrics.memoryUsage > 0 && (
              <View style={styles.row}>
                <Text style={styles.label}>Memory:</Text>
                <Text style={styles.value}>
                  {metrics.memoryUsage.toFixed(1)}MB
                </Text>
              </View>
            )}
          </View>

          {/* Recent Operations */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>📊 Recent Operations</Text>
            {recentOps.map((op, index) => (
              <View key={index} style={styles.operationRow}>
                <Text style={styles.operationName}>{op.operation}</Text>
                <Text style={[styles.operationDuration, {
                  color: op.success ? colors.success : colors.error
                }]}>
                  {op.duration}ms
                </Text>
                {op.cacheHit !== undefined && (
                  <Text style={[styles.cacheIndicator, {
                    color: op.cacheHit ? colors.success : colors.warning
                  }]}>
                    {op.cacheHit ? '💾' : '🌐'}
                  </Text>
                )}
              </View>
            ))}
          </View>

          {/* Actions */}
          <View style={styles.section}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => performanceMonitor.resetMetrics()}
            >
              <Text style={styles.actionButtonText}>Reset Metrics</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => backgroundSyncService.forcSync()}
            >
              <Text style={styles.actionButtonText}>Force Sync</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
  },
  closeButton: {
    padding: 8,
  },
  closeText: {
    fontSize: 18,
    color: colors.text,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: colors.surface,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  value: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
  },
  operationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  operationName: {
    flex: 1,
    fontSize: 12,
    color: colors.text,
  },
  operationDuration: {
    fontSize: 12,
    fontWeight: '600',
    marginRight: 8,
  },
  cacheIndicator: {
    fontSize: 12,
  },
  actionButton: {
    backgroundColor: colors.primary,
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  actionButtonText: {
    color: colors.white,
    textAlign: 'center',
    fontWeight: '600',
  },
});
