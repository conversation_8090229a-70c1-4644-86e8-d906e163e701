<dependencies>
  <compile
      roots="D:\CadetVocab - Copy\android@@:react-native-community_slider::release,com.facebook.react:react-android:0.72.6:release@aar,com.facebook.fresco:fresco:2.5.0@aar,com.facebook.fresco:imagepipeline-okhttp3:2.5.0@aar,com.facebook.fresco:animated-gif:2.5.0@aar,com.facebook.fresco:webpsupport:2.5.0@aar,com.facebook.react:hermes-android:0.72.6:release@aar,D:\CadetVocab - Copy\android@@:react-native-async-storage_async-storage::release,D:\CadetVocab - Copy\android@@:react-native-community_netinfo::release,D:\CadetVocab - Copy\android@@:expo::release,D:\CadetVocab - Copy\android@@:react-native-safe-area-context::release,D:\CadetVocab - Copy\android@@:react-native-screens::release,D:\CadetVocab - Copy\android@@:react-native-vector-icons::release,D:\CadetVocab - Copy\android@@:expo-dev-launcher::release,D:\CadetVocab - Copy\android@@:expo-dev-menu::release,androidx.appcompat:appcompat:1.6.1@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,D:\CadetVocab - Copy\android@@:expo-file-system::release,androidx.legacy:legacy-support-v4:1.0.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,D:\CadetVocab - Copy\android@@:expo-constants::release,D:\CadetVocab - Copy\android@@:expo-image-loader::release,com.github.bumptech.glide:glide:4.13.2@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.lifecycle:lifecycle-extensions:2.2.0@aar,androidx.fragment:fragment:1.5.7@aar,androidx.fragment:fragment:1.5.7@aar,androidx.activity:activity:1.7.2@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,D:\CadetVocab - Copy\android@@:expo-modules-core::release,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-service:2.6.1@aar,androidx.lifecycle:lifecycle-process:2.6.1@aar,androidx.lifecycle:lifecycle-livedata:2.6.1@aar,androidx.lifecycle:lifecycle-common:2.6.1@jar,androidx.lifecycle:lifecycle-livedata-core:2.6.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar,androidx.lifecycle:lifecycle-runtime:2.6.1@aar,androidx.lifecycle:lifecycle-runtime:2.6.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1@aar,androidx.core:core-ktx:1.9.0@aar,androidx.browser:browser:1.2.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.media:media:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.9.0@aar,androidx.core:core:1.9.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.interpolator:interpolator:1.0.0@aar,D:\CadetVocab - Copy\android@@:expo-dev-client::release,androidx.databinding:viewbinding:7.4.2@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,com.github.bumptech.glide:gifdecoder:4.13.2@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation:1.3.0@jar,androidx.annotation:annotation-experimental:1.3.0@aar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.squareup.okhttp3:okhttp:4.9.2@jar,com.squareup.okio:okio:2.9.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4@jar,io.insert-koin:koin-core-jvm:3.4.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.8.10@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.8.10@jar,org.jetbrains:annotations:13.0@jar,com.google.guava:listenablefuture:1.0@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.1.0@aar,androidx.autofill:autofill:1.1.0@aar,com.facebook.fbjni:fbjni:0.3.0@aar,com.facebook.soloader:soloader:0.10.5@aar,com.facebook.soloader:nativeloader:0.10.5@jar,com.facebook.fresco:fbcore:2.5.0@aar,com.facebook.soloader:annotation:0.10.5@jar,com.facebook.fresco:ui-common:2.5.0@aar,com.facebook.fresco:drawee:2.5.0@aar,com.facebook.fresco:imagepipeline:2.5.0@aar,com.facebook.fresco:imagepipeline-base:2.5.0@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.fresco:imagepipeline-native:2.5.0@aar,com.facebook.fresco:memory-type-ashmem:2.5.0@aar,com.facebook.fresco:memory-type-native:2.5.0@aar,com.facebook.fresco:memory-type-java:2.5.0@aar,com.facebook.fresco:nativeimagefilters:2.5.0@aar,com.facebook.fresco:nativeimagetranscoder:2.5.0@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,javax.inject:javax.inject:1@jar,commons-io:commons-io:2.6@jar,commons-codec:commons-codec:1.10@jar,com.github.bumptech.glide:disklrucache:4.13.2@jar,com.github.bumptech.glide:annotations:4.13.2@jar,androidx.exifinterface:exifinterface:1.3.3@aar,D:\CadetVocab - Copy\android@@:expo-application::release,D:\CadetVocab - Copy\android@@:expo-font::release,D:\CadetVocab - Copy\android@@:expo-image-picker::release,D:\CadetVocab - Copy\android@@:expo-json-utils::release,D:\CadetVocab - Copy\android@@:expo-keep-awake::release,D:\CadetVocab - Copy\android@@:expo-manifests::release,D:\CadetVocab - Copy\android@@:expo-modules-core$android-annotation,D:\CadetVocab - Copy\android@@:expo-modules-core$android-annotation-processor,D:\CadetVocab - Copy\android@@:expo-splash-screen::release">
    <dependency
        name="D:\CadetVocab - Copy\android@@:react-native-community_slider::release"
        simpleName="CadetVocab:react-native-community_slider"/>
    <dependency
        name="com.facebook.react:react-android:0.72.6:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name="com.facebook.fresco:fresco:2.5.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:2.5.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.facebook.fresco:animated-gif:2.5.0@aar"
        simpleName="com.facebook.fresco:animated-gif"/>
    <dependency
        name="com.facebook.fresco:webpsupport:2.5.0@aar"
        simpleName="com.facebook.fresco:webpsupport"/>
    <dependency
        name="com.facebook.react:hermes-android:0.72.6:release@aar"
        simpleName="com.facebook.react:hermes-android"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:react-native-async-storage_async-storage::release"
        simpleName="CadetVocab:react-native-async-storage_async-storage"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:react-native-community_netinfo::release"
        simpleName="CadetVocab:react-native-community_netinfo"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo::release"
        simpleName="host.exp.exponent:expo"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:react-native-safe-area-context::release"
        simpleName="CadetVocab:react-native-safe-area-context"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:react-native-screens::release"
        simpleName="CadetVocab:react-native-screens"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:react-native-vector-icons::release"
        simpleName="CadetVocab:react-native-vector-icons"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-dev-launcher::release"
        simpleName="CadetVocab:expo-dev-launcher"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-dev-menu::release"
        simpleName="host.exp.exponent:expo-dev-menu"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-file-system::release"
        simpleName="host.exp.exponent:expo-file-system"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-constants::release"
        simpleName="host.exp.exponent:expo-constants"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-image-loader::release"
        simpleName="host.exp.exponent:expo-image-loader"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.13.2@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-extensions"/>
    <dependency
        name="androidx.fragment:fragment:1.5.7@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.7.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-modules-core::release"
        simpleName="host.exp.exponent:expo-modules-core"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.9.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.browser:browser:1.2.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.media:media:1.0.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.9.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-dev-client::release"
        simpleName="CadetVocab:expo-dev-client"/>
    <dependency
        name="androidx.databinding:viewbinding:7.4.2@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.13.2@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation:1.3.0@jar"
        simpleName="androidx.annotation:annotation"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio:2.9.0@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="io.insert-koin:koin-core-jvm:3.4.0@jar"
        simpleName="io.insert-koin:koin-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.8.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.1.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.3.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.soloader:soloader:0.10.5@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.10.5@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="com.facebook.fresco:fbcore:2.5.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="com.facebook.soloader:annotation:0.10.5@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.facebook.fresco:ui-common:2.5.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name="com.facebook.fresco:drawee:2.5.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:2.5.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:2.5.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:2.5.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:2.5.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:2.5.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:2.5.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:2.5.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:2.5.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="commons-io:commons-io:2.6@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="commons-codec:commons-codec:1.10@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.13.2@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.13.2@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.3@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-application::release"
        simpleName="host.exp.exponent:expo-application"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-font::release"
        simpleName="host.exp.exponent:expo-font"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-image-picker::release"
        simpleName="host.exp.exponent:expo-image-picker"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-json-utils::release"
        simpleName="host.exp.exponent:expo-json-utils"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-keep-awake::release"
        simpleName="host.exp.exponent:expo-keep-awake"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-manifests::release"
        simpleName="host.exp.exponent:expo-manifests"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-modules-core$android-annotation"
        simpleName=":"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-modules-core$android-annotation-processor"
        simpleName=":"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-splash-screen::release"
        simpleName="host.exp.exponent:expo-splash-screen"/>
  </compile>
  <package
      roots="D:\CadetVocab - Copy\android@@:react-native-async-storage_async-storage::release,D:\CadetVocab - Copy\android@@:react-native-community_netinfo::release,D:\CadetVocab - Copy\android@@:react-native-community_slider::release,D:\CadetVocab - Copy\android@@:expo::release,D:\CadetVocab - Copy\android@@:react-native-safe-area-context::release,D:\CadetVocab - Copy\android@@:react-native-screens::release,D:\CadetVocab - Copy\android@@:react-native-vector-icons::release,D:\CadetVocab - Copy\android@@:expo-application::release,D:\CadetVocab - Copy\android@@:expo-constants::release,D:\CadetVocab - Copy\android@@:expo-dev-launcher::release,D:\CadetVocab - Copy\android@@:expo-dev-menu::release,D:\CadetVocab - Copy\android@@:expo-file-system::release,D:\CadetVocab - Copy\android@@:expo-font::release,D:\CadetVocab - Copy\android@@:expo-image-loader::release,D:\CadetVocab - Copy\android@@:expo-image-picker::release,D:\CadetVocab - Copy\android@@:expo-keep-awake::release,D:\CadetVocab - Copy\android@@:expo-splash-screen::release,D:\CadetVocab - Copy\android@@:expo-modules-core::release,D:\CadetVocab - Copy\android@@:expo-dev-menu-interface::release,com.facebook.react:react-android:0.72.6:release@aar,com.facebook.fresco:fresco:2.5.0@aar,com.facebook.fresco:imagepipeline-okhttp3:2.5.0@aar,com.facebook.fresco:animated-gif:2.5.0@aar,com.facebook.fresco:webpsupport:2.5.0@aar,com.facebook.react:hermes-android:0.72.6:release@aar,com.google.android.material:material:1.2.1@aar,com.github.CanHub:Android-Image-Cropper:4.3.1@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.autofill:autofill:1.1.0@aar,androidx.legacy:legacy-support-v4:1.0.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.tracing:tracing-ktx:1.1.0@aar,com.github.bumptech.glide:glide:4.13.2@aar,androidx.fragment:fragment-ktx:1.5.7@aar,androidx.activity:activity-ktx:1.7.2@aar,androidx.lifecycle:lifecycle-extensions:2.2.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.fragment:fragment:1.5.7@aar,androidx.fragment:fragment:1.5.7@aar,androidx.activity:activity:1.7.2@aar,androidx.emoji2:emoji2-views-helper:1.2.0@aar,androidx.emoji2:emoji2:1.2.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.browser:browser:1.2.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.transition:transition:1.2.0@aar,androidx.media:media:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.lifecycle:lifecycle-service:2.6.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1@aar,androidx.core:core-ktx:1.9.0@aar,androidx.core:core:1.9.0@aar,androidx.core:core:1.9.0@aar,androidx.lifecycle:lifecycle-runtime:2.6.1@aar,androidx.lifecycle:lifecycle-runtime:2.6.1@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.6.1@aar,androidx.lifecycle:lifecycle-livedata:2.6.1@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-common:2.6.1@jar,androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.6.1@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1@aar,androidx.lifecycle:lifecycle-process:2.6.1@aar,androidx.lifecycle:lifecycle-process:2.6.1@aar,androidx.profileinstaller:profileinstaller:1.3.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.1.0@aar,com.facebook.fbjni:fbjni:0.3.0@aar,com.facebook.fresco:animated-base:2.5.0@aar,com.facebook.fresco:animated-drawable:2.5.0@aar,com.facebook.fresco:drawee:2.5.0@aar,com.facebook.fresco:middleware:2.5.0@aar,com.facebook.fresco:ui-common:2.5.0@aar,com.facebook.fresco:nativeimagefilters:2.5.0@aar,com.facebook.fresco:memory-type-native:2.5.0@aar,com.facebook.fresco:memory-type-java:2.5.0@aar,com.facebook.fresco:imagepipeline-native:2.5.0@aar,com.facebook.fresco:memory-type-ashmem:2.5.0@aar,com.facebook.fresco:imagepipeline:2.5.0@aar,com.facebook.fresco:nativeimagetranscoder:2.5.0@aar,com.facebook.fresco:imagepipeline-base:2.5.0@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.facebook.fresco:soloader:2.5.0@aar,com.facebook.soloader:soloader:0.10.5@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.squareup.okhttp3:okhttp:4.9.2@jar,com.squareup.okio:okio:2.9.0@jar,javax.inject:javax.inject:1@jar,D:\CadetVocab - Copy\android@@:expo-dev-client::release,D:\CadetVocab - Copy\android@@:expo-modules-core$android-annotation-processor,com.squareup:kotlinpoet-ksp:1.12.0@jar,com.squareup:kotlinpoet:1.12.0@jar,io.insert-koin:koin-core-jvm:3.4.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10@jar,com.facebook.soloader:nativeloader:0.10.5@jar,com.facebook.fresco:fbcore:2.5.0@aar,com.parse.bolts:bolts-tasks:1.4.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.databinding:viewbinding:7.4.2@aar,androidx.exifinterface:exifinterface:1.3.3@aar,androidx.collection:collection-ktx:1.1.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.cardview:cardview:1.0.0@aar,com.github.bumptech.glide:gifdecoder:4.13.2@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.annotation:annotation:1.3.0@jar,D:\CadetVocab - Copy\android@@:expo-manifests::release,D:\CadetVocab - Copy\android@@:expo-json-utils::release,D:\CadetVocab - Copy\android@@:expo-modules-core$android-annotation,D:\CadetVocab - Copy\android@@:expo-updates-interface::release,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10@jar,org.jetbrains.kotlin:kotlin-reflect:1.8.10@jar,com.google.devtools.ksp:symbol-processing-api:1.8.10-1.0.9@jar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.7.0@jar,androidx.annotation:annotation-experimental:1.3.0@aar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.7.0@jar,org.jetbrains.kotlin:kotlin-stdlib:1.8.10@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.soloader:annotation:0.10.5@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.8.10@jar,com.android.installreferrer:installreferrer:1.0@aar,commons-io:commons-io:2.6@jar,com.google.code.gson:gson:2.8.6@jar,commons-codec:commons-codec:1.10@jar,org.jetbrains:annotations:13.0@jar,com.google.guava:listenablefuture:1.0@jar,com.github.bumptech.glide:disklrucache:4.13.2@jar,com.github.bumptech.glide:annotations:4.13.2@jar">
    <dependency
        name="D:\CadetVocab - Copy\android@@:react-native-async-storage_async-storage::release"
        simpleName="CadetVocab:react-native-async-storage_async-storage"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:react-native-community_netinfo::release"
        simpleName="CadetVocab:react-native-community_netinfo"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:react-native-community_slider::release"
        simpleName="CadetVocab:react-native-community_slider"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo::release"
        simpleName="host.exp.exponent:expo"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:react-native-safe-area-context::release"
        simpleName="CadetVocab:react-native-safe-area-context"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:react-native-screens::release"
        simpleName="CadetVocab:react-native-screens"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:react-native-vector-icons::release"
        simpleName="CadetVocab:react-native-vector-icons"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-application::release"
        simpleName="host.exp.exponent:expo-application"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-constants::release"
        simpleName="host.exp.exponent:expo-constants"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-dev-launcher::release"
        simpleName="CadetVocab:expo-dev-launcher"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-dev-menu::release"
        simpleName="host.exp.exponent:expo-dev-menu"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-file-system::release"
        simpleName="host.exp.exponent:expo-file-system"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-font::release"
        simpleName="host.exp.exponent:expo-font"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-image-loader::release"
        simpleName="host.exp.exponent:expo-image-loader"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-image-picker::release"
        simpleName="host.exp.exponent:expo-image-picker"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-keep-awake::release"
        simpleName="host.exp.exponent:expo-keep-awake"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-splash-screen::release"
        simpleName="host.exp.exponent:expo-splash-screen"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-modules-core::release"
        simpleName="host.exp.exponent:expo-modules-core"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-dev-menu-interface::release"
        simpleName="host.exp.exponent:expo-dev-menu-interface"/>
    <dependency
        name="com.facebook.react:react-android:0.72.6:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name="com.facebook.fresco:fresco:2.5.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:2.5.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.facebook.fresco:animated-gif:2.5.0@aar"
        simpleName="com.facebook.fresco:animated-gif"/>
    <dependency
        name="com.facebook.fresco:webpsupport:2.5.0@aar"
        simpleName="com.facebook.fresco:webpsupport"/>
    <dependency
        name="com.facebook.react:hermes-android:0.72.6:release@aar"
        simpleName="com.facebook.react:hermes-android"/>
    <dependency
        name="com.google.android.material:material:1.2.1@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="com.github.CanHub:Android-Image-Cropper:4.3.1@aar"
        simpleName="com.github.CanHub:Android-Image-Cropper"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.tracing:tracing-ktx:1.1.0@aar"
        simpleName="androidx.tracing:tracing-ktx"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.13.2@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.5.7@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.activity:activity-ktx:1.7.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-extensions"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment:1.5.7@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.7.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.browser:browser:1.2.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.transition:transition:1.2.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.media:media:1.0.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.9.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.core:core:1.9.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.1.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.3.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.fresco:animated-base:2.5.0@aar"
        simpleName="com.facebook.fresco:animated-base"/>
    <dependency
        name="com.facebook.fresco:animated-drawable:2.5.0@aar"
        simpleName="com.facebook.fresco:animated-drawable"/>
    <dependency
        name="com.facebook.fresco:drawee:2.5.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.facebook.fresco:middleware:2.5.0@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:2.5.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:2.5.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:2.5.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:2.5.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:2.5.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:2.5.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:2.5.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:2.5.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:2.5.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.facebook.fresco:soloader:2.5.0@aar"
        simpleName="com.facebook.fresco:soloader"/>
    <dependency
        name="com.facebook.soloader:soloader:0.10.5@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio:2.9.0@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-dev-client::release"
        simpleName="CadetVocab:expo-dev-client"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-modules-core$android-annotation-processor"
        simpleName=":"/>
    <dependency
        name="com.squareup:kotlinpoet-ksp:1.12.0@jar"
        simpleName="com.squareup:kotlinpoet-ksp"/>
    <dependency
        name="com.squareup:kotlinpoet:1.12.0@jar"
        simpleName="com.squareup:kotlinpoet"/>
    <dependency
        name="io.insert-koin:koin-core-jvm:3.4.0@jar"
        simpleName="io.insert-koin:koin-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.10.5@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="com.facebook.fresco:fbcore:2.5.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="com.parse.bolts:bolts-tasks:1.4.0@jar"
        simpleName="com.parse.bolts:bolts-tasks"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.databinding:viewbinding:7.4.2@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.3@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.13.2@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.annotation:annotation:1.3.0@jar"
        simpleName="androidx.annotation:annotation"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-manifests::release"
        simpleName="host.exp.exponent:expo-manifests"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-json-utils::release"
        simpleName="host.exp.exponent:expo-json-utils"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-modules-core$android-annotation"
        simpleName=":"/>
    <dependency
        name="D:\CadetVocab - Copy\android@@:expo-updates-interface::release"
        simpleName="host.exp.exponent:expo-updates-interface"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-reflect:1.8.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-reflect"/>
    <dependency
        name="com.google.devtools.ksp:symbol-processing-api:1.8.10-1.0.9@jar"
        simpleName="com.google.devtools.ksp:symbol-processing-api"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.7.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.7.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.8.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.soloader:annotation:0.10.5@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="com.android.installreferrer:installreferrer:1.0@aar"
        simpleName="com.android.installreferrer:installreferrer"/>
    <dependency
        name="commons-io:commons-io:2.6@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="com.google.code.gson:gson:2.8.6@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="commons-codec:commons-codec:1.10@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.13.2@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.13.2@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
  </package>
</dependencies>
