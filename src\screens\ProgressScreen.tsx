import React, { useEffect, useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Platform,
  Dimensions,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import {
  Text,
  Avatar,
  Card,
  useTheme,
  IconButton,
  ActivityIndicator,
  ProgressBar,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootStackParamList } from '@/types';
import { auth } from '@/config/firebase';
import { userService } from '@/services/userService';
import { colors } from '@/theme/colors';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { TAB_BAR_HEIGHT, BOTTOM_SPACING } from '@/constants/layout';
import ProtectedScreen from '../components/ProtectedScreen';
import { bookmarkService, BookmarkedQuestion } from '../services/bookmarkService';
import { quizService } from '../services/quizService';
import { QuizSet } from '../types';

interface QuizHistoryItem {
  quizId: string;
  title: string;
  score: number;
  totalQuestions: number;
  completedAt: Date;
  status: 'completed' | 'incomplete';
}

type ProgressScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const formatDate = (date: Date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (days === 0) return 'Today';
  if (days === 1) return 'Yesterday';
  if (days < 7) return `${days} days ago`;
  return date.toLocaleDateString();
};

const getInitials = (name: string = '') => {
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

interface UserStats {
  wordsLearned: number;
  quizzesTaken: number;
  correctAnswers: number;
  totalQuestions: number;
  accuracy: number;
  totalXP: number;
  streak: number;
  lastQuizDate: string | null;
}

interface QuizTitleCache {
  [quizSetId: string]: string;
}

const ProgressScreen = () => {
  const navigation = useNavigation<ProgressScreenNavigationProp>();
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userStats, setUserStats] = useState<UserStats>({
    wordsLearned: 0,
    quizzesTaken: 0,
    correctAnswers: 0,
    totalQuestions: 0,
    accuracy: 0,
    totalXP: 0,
    streak: 0,
    lastQuizDate: null,
  });

  const [quizHistory, setQuizHistory] = useState<QuizHistoryItem[]>([]);
  const [bookmarks, setBookmarks] = useState<BookmarkedQuestion[]>([]);
  const [quizTitles, setQuizTitles] = useState<QuizTitleCache>({});

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const user = auth.currentUser;
        if (!user) {
          setError('User not authenticated');
          setLoading(false);
          return;
        }

        // First try to get the latest data from server
        const serverProfile = await userService.fetchUserProfileFromServer(user.uid);
        if (serverProfile?.stats) {
          setUserStats(serverProfile.stats);
          // Update local cache with server data
          await userService.updateLocalUserProfile(user.uid, serverProfile);
        } else {
          // Fallback to local cache if server fetch fails
          const localProfile = await userService.getUserProfile(user.uid);
          if (localProfile?.stats) {
            setUserStats(localProfile.stats);
          }
        }

        // Fetch quiz titles
        const quizSets = await quizService.getAllQuizSets();
        const titleMap: QuizTitleCache = {};
        quizSets.forEach(set => {
          titleMap[set.id] = set.title;
        });
        setQuizTitles(titleMap);
        
        setLoading(false);
      } catch (error) {
        console.error('Error fetching user data:', error);
        setError('Failed to load progress data');
        setLoading(false);
      }
    };

    fetchUserData();

    // load bookmarks
    bookmarkService.getBookmarks().then(setBookmarks);
  }, []);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text>Loading your progress...</Text>
      </View>
    );
  }

  return (
    <ProtectedScreen screenName="Progress">
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        
        <View style={[styles.header, { backgroundColor: theme.colors.primary }]}>
          <Text style={styles.headerTitle}>Your Progress</Text>
          <View style={styles.streakContainer}>
            <Icon name="local-fire-department" size={24} color={colors.primary} />
            <Text style={styles.streakText}>{userStats.streak} Day Streak!</Text>
          </View>
        </View>

        <ScrollView 
          style={styles.content} 
          contentContainerStyle={{
            paddingHorizontal: styles.content.padding,
            paddingTop: styles.content.padding,
            paddingBottom: TAB_BAR_HEIGHT + 
                           (Platform.OS === 'ios' ? insets.bottom : BOTTOM_SPACING) + 
                           16
          }}
        >
          {/* Stats Cards */}
          <View style={styles.statsGrid}>
            <View style={styles.statsCard}>
              <Icon name="star" size={24} color={colors.primary} />
              <Text style={styles.statsNumber}>{userStats.totalXP}</Text>
              <Text style={styles.statsLabel}>Total XP</Text>
            </View>
            <View style={styles.statsCard}>
              <Icon name="check-circle" size={24} color={colors.primary} />
              <Text style={styles.statsNumber}>{userStats.quizzesTaken}</Text>
              <Text style={styles.statsLabel}>Quizzes Taken</Text>
            </View>
            <View style={styles.statsCard}>
              <Icon name="track-changes" size={24} color={colors.primary} />
              <Text style={styles.statsNumber}>{userStats.accuracy}%</Text>
              <Text style={styles.statsLabel}>Accuracy</Text>
            </View>
            <View style={styles.statsCard}>
              <Icon name="menu-book" size={24} color={colors.primary} />
              <Text style={styles.statsNumber}>{userStats.wordsLearned}</Text>
              <Text style={styles.statsLabel}>Words Learned</Text>
            </View>
          </View>

          {/* Bookmarked Quizzes */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Bookmarked Questions</Text>
            <View style={styles.bookmarkListContainer}>
              {bookmarks.length > 0 ? (
                <ScrollView
                  showsVerticalScrollIndicator={false}
                  nestedScrollEnabled
                >
                  {[...bookmarks]
                    .sort((a, b) => b.addedAt - a.addedAt)
                    .map(b => (
                    <TouchableOpacity
                      key={b.id}
                      style={styles.bookmarkItem}
                      onPress={() => navigation.navigate('Quiz', { 
                         quizSetId: b.quizSetId,
                         isBookmarkQuestion: true,
                         bookmarkedQuestion: b.question,
                       })}
                    >
                      <Icon name="bookmark" size={24} color={colors.primary} />
                      <View style={styles.bookmarkContent}>
                        <Text style={styles.bookmarkTitle}>{b.question.question}</Text>
                        <Text style={styles.bookmarkSubtitle}>{quizTitles[b.quizSetId] || `Quiz Set ${b.quizSetId}`}</Text>
                      </View>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              ) : (
                <View style={styles.emptyBookmarksContainer}>
                  <Icon name="bookmark-border" size={48} color={colors.text.secondary} />
                  <Text style={styles.emptyBookmarksText}>No bookmarked questions yet</Text>
                  <Text style={styles.emptyBookmarksSubtext}>
                    Bookmark questions during quizzes to review them later
                  </Text>
                </View>
              )}
            </View>
          </View>

        </ScrollView>
      </View>
    </ProtectedScreen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.secondary,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 16,
    paddingTop: StatusBar.currentHeight || 16,
    backgroundColor: colors.dark.medium,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    color: colors.text.primary,
    fontSize: 24,
    fontWeight: 'bold',
  },
  streakContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.dark.light,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  streakText: {
    color: colors.text.primary,
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  statsCard: {
    width: '48%',
    backgroundColor: colors.dark.medium,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  statsNumber: {
    color: colors.text.primary,
    fontSize: 24,
    fontWeight: 'bold',
    marginVertical: 8,
  },
  statsLabel: {
    color: colors.text.secondary,
    fontSize: 14,
    textAlign: 'center',
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    color: colors.text.primary,
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  weeklyActivityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 150,
    paddingVertical: 16,
    backgroundColor: colors.dark.medium,
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
  },
  activityBar: {
    width: 32,
    height: '100%',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  activityBarFill: {
    width: '100%',
    backgroundColor: colors.primary,
    borderRadius: 4,
  },
  activityDay: {
    color: colors.text.secondary,
    fontSize: 12,
    marginTop: 4,
  },
  activityCard: {
    backgroundColor: colors.dark.medium,
    marginBottom: 8,
    borderRadius: 12,
  },
  activityContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityDetails: {
    flex: 1,
  },
  activityTitle: {
    color: colors.text.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  activitySubtitle: {
    color: colors.text.secondary,
    fontSize: 14,
    marginTop: 4,
  },
  activityDate: {
    fontSize: 12,
  },
  bookmarkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.dark.medium,
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  bookmarkContent: {
    flex: 1,
    marginLeft: 12,
  },
  bookmarkTitle: {
    color: colors.text.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  bookmarkSubtitle: {
    color: colors.text.secondary,
    fontSize: 14,
    marginTop: 4,
  },
  bookmarkCard: {
    width: 220,
    backgroundColor: colors.dark.medium,
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    justifyContent: 'flex-start',
  },
  bookmarkCardText: {
    color: colors.text.primary,
    fontSize: 14,
  },
  bookmarkListContainer: {
    maxHeight: 300, // adjust height as needed
  },
  emptyBookmarksContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    backgroundColor: colors.dark.medium,
    borderRadius: 12,
  },
  emptyBookmarksText: {
    color: colors.text.primary,
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 16,
  },
  emptyBookmarksSubtext: {
    color: colors.text.secondary,
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
});

export default ProgressScreen; 

