import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { NavigationContainer } from '@react-navigation/native';
import { Platform } from 'react-native';
import HomeScreen from '../screens/HomeScreen';
import LibraryScreen from '../screens/LibraryScreen';
import ProgressScreen from '../screens/ProgressScreen';
import ProfileScreen from '../screens/ProfileScreen';
import QuizScreen from '../screens/QuizScreen';
import QuizCompleteScreen from '../screens/QuizCompleteScreen';
import AdminScreen from '../screens/AdminScreen';
import EditProfileScreen from '../screens/EditProfileScreen';
import ChangePasswordScreen from '../screens/ChangePasswordScreen';
import LoginScreen from '../screens/LoginScreen';
import RegisterScreen from '../screens/RegisterScreen';
import ForgotPasswordScreen from '../screens/ForgotPasswordScreen';
import PrivacyPolicyScreen from '../screens/PrivacyPolicyScreen';
import { RootStackParamList, TabParamList } from '../types/index';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../theme/colors';
import { getNavigationConfig } from '../utils/navigationConfig';
import ProtectedScreen from '../components/ProtectedScreen';

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();

const ProtectedProgressScreen = () => (
  <ProtectedScreen screenName="Progress">
    <ProgressScreen />
  </ProtectedScreen>
);

const ProtectedProfileScreen = () => (
  <ProtectedScreen screenName="Profile">
    <ProfileScreen />
  </ProtectedScreen>
);

const ProtectedEditProfileScreen = () => (
  <ProtectedScreen screenName="Edit Profile">
    <EditProfileScreen />
  </ProtectedScreen>
);

const ProtectedChangePasswordScreen = () => (
  <ProtectedScreen screenName="Change Password">
    <ChangePasswordScreen />
  </ProtectedScreen>
);

const MainTabs = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;
          switch (route.name) {
            case 'HomeTab':
              iconName = 'home';
              break;
            case 'LibraryTab':
              iconName = 'library-books';
              break;
            case 'ProgressTab':
              iconName = 'trending-up';
              break;
            case 'ProfileTab':
              iconName = 'person';
              break;
            default:
              iconName = 'help';
          }
          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.text.secondary,
        headerShown: false,
        ...Platform.select({
          web: {
            tabBarStyle: {
              paddingTop: 5,
              paddingBottom: 5,
            }
          }
        })
      })}
    >
      <Tab.Screen name="HomeTab" component={HomeScreen} />
      <Tab.Screen name="LibraryTab" component={LibraryScreen} />
      <Tab.Screen name="ProgressTab" component={ProtectedProgressScreen} />
      <Tab.Screen name="ProfileTab" component={ProtectedProfileScreen} />
    </Tab.Navigator>
  );
};

const AppNavigator = () => {
  const navigationConfig = getNavigationConfig();
  
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Main"
        screenOptions={{
          headerShown: false,
          ...navigationConfig.screenOptions,
        }}
      >
        <Stack.Screen name="Main" component={MainTabs} />
        <Stack.Screen name="Quiz" component={QuizScreen} />
        <Stack.Screen name="QuizComplete" component={QuizCompleteScreen} />
        <Stack.Screen name="Admin" component={AdminScreen} />
        <Stack.Screen name="Login" component={LoginScreen} />
        <Stack.Screen name="Register" component={RegisterScreen} />
        <Stack.Screen name="EditProfile" component={ProtectedEditProfileScreen} />
        <Stack.Screen name="ChangePassword" component={ProtectedChangePasswordScreen} />
        <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
        <Stack.Screen name="PrivacyPolicy" component={PrivacyPolicyScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator; 