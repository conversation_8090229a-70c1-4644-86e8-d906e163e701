import React, { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, Snackbar } from 'react-native-paper';
import { useNavigation, useRoute } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../types';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../theme/colors';
import { auth } from '../config/firebase';
import { userService } from '../services/userService';

type QuizCompleteScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'QuizComplete'>;
type QuizCompleteScreenRouteProp = RouteProp<RootStackParamList, 'QuizComplete'>;

const QuizCompleteScreen: React.FC = () => {
  const navigation = useNavigation<QuizCompleteScreenNavigationProp>();
  const route = useRoute<QuizCompleteScreenRouteProp>();
  const { score, totalQuestions, quizTitle, timeSpent, difficulty } = route.params;
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(true);

  const percentage = Math.round((score / totalQuestions) * 100);
  const minutes = Math.floor(timeSpent / 60);
  const seconds = timeSpent % 60;

  const updateStats = async () => {
    try {
      setIsUpdating(true);
      const user = auth.currentUser;
      if (!user) {
        setError('User not authenticated');
        return;
      }

      // Ensure difficulty is one of the allowed types, default to 'intermediate'
      const validDifficulty = difficulty && ['beginner', 'intermediate', 'advanced'].includes(difficulty) 
                              ? difficulty 
                              : 'intermediate';

      await userService.updateQuizStats(user.uid, {
        score,
        totalQuestions,
        difficulty: validDifficulty,
        quizSetId: route.params.quizSetId,
      });
    } catch (error) {
      console.error('Error updating stats:', error);
      setError('Failed to update stats. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  useEffect(() => {
    updateStats();
  }, [score, totalQuestions, difficulty]);

  const getGrade = () => {
    if (percentage >= 90) return 'Outstanding!';
    if (percentage >= 80) return 'Excellent!';
    if (percentage >= 70) return 'Good Job!';
    if (percentage >= 60) return 'Keep Practicing!';
    return 'Need More Practice';
  };

  const handleReturnHome = () => {
    navigation.reset({
      index: 0,
      routes: [{ name: 'Main' }],
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerText}>Quiz Complete!</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.scoreCircle}>
          <Text style={styles.scorePercentage}>{percentage}%</Text>
          <Text style={styles.scoreText}>{score}/{totalQuestions}</Text>
        </View>

        <Text style={styles.gradeText}>{getGrade()}</Text>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Icon name="timer" size={24} color={colors.primary} />
            <Text style={styles.statLabel}>Time Taken</Text>
            <Text style={styles.statValue}>
              {minutes}m {seconds}s
            </Text>
          </View>

          <View style={styles.statItem}>
            <Icon name="check-circle" size={24} color={colors.primary} />
            <Text style={styles.statLabel}>Correct Answers</Text>
            <Text style={styles.statValue}>{score}</Text>
          </View>

          <View style={styles.statItem}>
            <Icon name="trending-up" size={24} color={colors.primary} />
            <Text style={styles.statLabel}>Accuracy</Text>
            <Text style={styles.statValue}>{percentage}%</Text>
          </View>
        </View>

        <TouchableOpacity
          style={styles.returnButton}
          onPress={handleReturnHome}
        >
          <Icon name="home" size={24} color={colors.text.onPrimary} />
          <Text style={styles.returnButtonText}>  Home</Text>
        </TouchableOpacity>
      </View>

      <Snackbar
        visible={!!error}
        onDismiss={() => setError(null)}
        action={{
          label: 'Retry',
          onPress: () => {
            setError(null);
            updateStats();
          },
        }}
      >
        {error}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.secondary,
  },
  header: {
    backgroundColor: colors.primary,
    padding: 24,
    alignItems: 'center',
  },
  headerText: {
    color: colors.text.onPrimary,
    fontSize: 24,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
  },
  scoreCircle: {
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: colors.dark.medium,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 32,
    borderWidth: 8,
    borderColor: colors.primary,
  },
  scorePercentage: {
    color: colors.text.primary,
    fontSize: 48,
    fontWeight: 'bold',
  },
  scoreText: {
    color: colors.text.secondary,
    fontSize: 18,
    marginTop: 8,
  },
  gradeText: {
    color: colors.primary,
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 32,
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 32,
    flexWrap: 'wrap',
    gap: 8,
  },
  statItem: {
    alignItems: 'center',
    backgroundColor: colors.dark.medium,
    padding: 12,
    borderRadius: 12,
    flex: 1,
    minWidth: '30%',
  },
  statLabel: {
    color: colors.text.secondary,
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  statValue: {
    color: colors.text.primary,
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 4,
  },
  returnButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    marginTop: 'auto',
  },
  returnButtonText: {
    color: colors.text.onPrimary,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default QuizCompleteScreen; 